local UI_HeroAttrPanelModel = {}

UI_HeroAttrPanelModel.config = {["name"] = "UI_HeroAttrPanel", ["layer"] = UILayerType.Normal, ["type"] = UIType.Pop, ["isAutoClose"] = false, ["anim"] = 0,["background"] = 0, ["onEscape"] = false, ["tinyGamePath"] = nil}

function UI_HeroAttrPanelModel:Init(c)
    c.ui = {}    
    c.ui.m_imgHero = GetChild(c.uiGameObject,"leftBg/heroBg/m_imgHero",UEUI.Image)
    c.ui.m_btnTip = GetChild(c.uiGameObject,"leftBg/m_btnTip",UEUI.Button)
    c.ui.m_imgQuality = GetChild(c.uiGameObject,"leftBg/m_imgQuality",UEUI.Image)
    c.ui.m_imgKind = GetChild(c.uiGameObject,"leftBg/m_imgKind",UEUI.Image)
    c.ui.m_imgCareer = GetChild(c.uiGameObject,"leftBg/m_imgCareer",UEUI.Image)
    c.ui.m_txtName = GetChild(c.uiGameObject,"leftBg/m_txtName",UEUI.Text)
    c.ui.m_txtFight = GetChild(c.uiGameObject,"leftBg/m_txtFight",UEUI.Text)
    c.ui.m_imgFight = GetChild(c.uiGameObject,"leftBg/m_txtFight/m_imgFight",UEUI.Image)
    c.ui.m_btnChangeSkin = GetChild(c.uiGameObject,"leftBg/m_btnChangeSkin",UEUI.Button)
    c.ui.m_btnHeroReset = GetChild(c.uiGameObject,"leftBg/m_btnHeroReset",UEUI.Button)
    c.ui.m_txtAtk = GetChild(c.uiGameObject,"rightBg/attrBg1/m_txtAtk",UEUI.Text)
    c.ui.m_txtLife = GetChild(c.uiGameObject,"rightBg/attrBg2/m_txtLife",UEUI.Text)
    c.ui.m_txtDef = GetChild(c.uiGameObject,"rightBg/attrBg3/m_txtDef",UEUI.Text)
    c.ui.m_txtArmy = GetChild(c.uiGameObject,"rightBg/attrBg4/m_txtArmy",UEUI.Text)
    c.ui.m_txtCost = GetChild(c.uiGameObject,"rightBg/tipBg/m_txtCost",UEUI.Text)
    c.ui.m_imgCost = GetChild(c.uiGameObject,"rightBg/tipBg/m_txtCost/m_imgCost",UEUI.Image)
    c.ui.m_txtTip = GetChild(c.uiGameObject,"rightBg/tipBg/m_txtTip",UEUI.Text)
    c.ui.m_btnUpgrade = GetChild(c.uiGameObject,"rightBg/m_btnUpgrade",UEUI.Button)
    c.ui.m_txtUpgrade = GetChild(c.uiGameObject,"rightBg/m_btnUpgrade/m_txtUpgrade",UEUI.Text)
    c.ui.m_goUpgradeRed = GetChild(c.uiGameObject,"rightBg/m_btnUpgrade/m_goUpgradeRed")
    c.ui.m_goToy = GetChild(c.uiGameObject,"m_goToy")
    c.ui.m_goStar = GetChild(c.uiGameObject,"m_goStar")
    c.ui.m_goUpLevel = GetChild(c.uiGameObject,"m_goUpLevel")
    c.ui.m_txtAtkAdd = GetChild(c.uiGameObject,"m_goUpLevel/attrAddObj/m_txtAtkAdd",UEUI.Text)
    c.ui.m_txtLifeAdd = GetChild(c.uiGameObject,"m_goUpLevel/attrAddObj/m_txtLifeAdd",UEUI.Text)
    c.ui.m_txtDefAdd = GetChild(c.uiGameObject,"m_goUpLevel/attrAddObj/m_txtDefAdd",UEUI.Text)
    c.ui.m_txtLevel = GetChild(c.uiGameObject,"topCanvas/m_txtLevel",UEUI.Text)
    c.ui.m_btnGhost = GetChild(c.uiGameObject,"topCanvas/m_btnGhost",UEUI.Button)
    c.ui.m_txtGhost = GetChild(c.uiGameObject,"topCanvas/m_btnGhost/m_txtGhost",UEUI.Text)
    c.ui.m_imgGhostRed = GetChild(c.uiGameObject,"topCanvas/m_btnGhost/m_imgGhostRed",UEUI.Image)
    c.ui.m_goEquipment = GetChild(c.uiGameObject,"topCanvas/m_goEquipment")
    c.ui.m_goLeftTop = GetChild(c.uiGameObject,"topCanvas/m_goEquipment/m_goLeftTop")
    c.ui.m_goRightTop = GetChild(c.uiGameObject,"topCanvas/m_goEquipment/m_goRightTop")
    c.ui.m_goLeftBottom = GetChild(c.uiGameObject,"topCanvas/m_goEquipment/m_goLeftBottom")
    c.ui.m_goRightBottom = GetChild(c.uiGameObject,"topCanvas/m_goEquipment/m_goRightBottom")
    c.ui.m_goTipMask = GetChild(c.uiGameObject,"topCanvas/m_goTipMask")
    c.ui.m_goTipTarget = GetChild(c.uiGameObject,"topCanvas/m_goTipMask/m_goTipTarget")
    c.ui.m_txtTipShow = GetChild(c.uiGameObject,"topCanvas/m_goTipMask/m_goTipTarget/tipBg/m_txtTipShow",UEUI.Text)
    c.ui.m_goAttrTipMask = GetChild(c.uiGameObject,"topCanvas/m_goAttrTipMask")
    c.ui.m_goAttrTipTarget = GetChild(c.uiGameObject,"topCanvas/m_goAttrTipMask/m_goAttrTipTarget")
    c.ui.m_txtAttrName = GetChild(c.uiGameObject,"topCanvas/m_goAttrTipMask/m_goAttrTipTarget/tipBg/m_txtAttrName",UEUI.Text)
    c.ui.m_txtAttr = GetChild(c.uiGameObject,"topCanvas/m_goAttrTipMask/m_goAttrTipTarget/tipBg/m_txtAttr",UEUI.Text)
    c.ui.m_goAttrBg = GetChild(c.uiGameObject,"topCanvas/m_goAttrTipMask/m_goAttrTipTarget/tipBg/m_goAttrBg")
    c.ui.m_goAttr = GetChild(c.uiGameObject,"topCanvas/m_goAttrTipMask/m_goAttrTipTarget/tipBg/m_goAttr")
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_HeroAttrPanelModel