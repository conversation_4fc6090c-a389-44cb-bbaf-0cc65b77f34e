local UI_HeroAnimalPanel = Class(BaseView)

function UI_HeroAnimalPanel:OnInit()
    self.animalSpineList = {};
    EventMgr:Add(EventID.HERO_WINDOW_CLOSE, self.Close, self);
    EventMgr:Add(EventID.UPDATE_HERO_ANIMAL, self.OnUpdatePanel, self);
end

function UI_HeroAnimalPanel:OnCreate(param)
    self.heroId = param;
    self:OnUpdatePanel();
end

function UI_HeroAnimalPanel:OnRefresh(_type, param)
    if _type == 1 then
        self.heroId = param;
        self:OnUpdatePanel();
    end
end

function UI_HeroAnimalPanel:onDestroy()
    EventMgr:Remove(EventID.HERO_WINDOW_CLOSE, self.Close, self);
    EventMgr:Remove(EventID.UPDATE_HERO_ANIMAL, self.OnUpdatePanel, self);
end

function UI_HeroAnimalPanel:onUIEventClick(go,param)
    local name = go.name
    if name == "tipBtn" then
        UI_SHOW(UIDefine.UI_HeroDescTipWindow, LangMgr:GetLang(70000158));
    elseif name == "clickBtn1" or name == "clickBtn2" or name == "clickBtn3" or name == "clickBtn4" then
        local clickIndex = v2n(string.gsub(name, "clickBtn", ""));
        if clickIndex then
            local unLockState, limitStar = self.heroVo:GetHeroAnimalUnlock(clickIndex);
            if unLockState then
                UI_SHOW(UIDefine.UI_HeroAnimalReplaceWindow, self.heroId, clickIndex);
            else
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLangFormat(58006037, limitStar));
            end
        end
    end
    UIMgr:Refresh(UIDefine.UI_HeroDevelopWindow, 3);
end

function UI_HeroAnimalPanel:OnUpdatePanel()
    self.heroVo = HeroManager:GetHeroVoById(self.heroId);
    if not self.heroVo then return end
    
    local list = HeroManager:GetAnimalList(self.heroId);
    local len = #list;
    local startIdx = 1;
    local maxFight = 0;
    local typeListStr;
    for i = 1, HeroManager.ANIMAL_COUNT do
        local animalItem = self.ui["m_goAnimal" .. i];
        local nameTxt = GetChild(animalItem, "nameBg/nameTxt", UEUI.Text);
        local stateTxt = GetChild(animalItem, "nameBg/stateTxt", UEUI.Text);
        local clickBtn =  GetChild(animalItem, "clickBtn" .. i);
        local stateSp = GetChild(clickBtn, "stateSp");
        local lockSp = GetChild(clickBtn, "lockSp");
        local redSp = GetChild(clickBtn, "redSp");

        local attrItem = self.ui["m_goShowItem" .. i];
        local attrObj = GetChild(attrItem, "attrObj");
        local titleTxt = GetChild(attrItem, "titleTxt", UEUI.Text);
        local tipTxt = GetChild(attrItem, "tipTxt", UEUI.Text);
        local lockImg = GetChild(attrItem, "tipTxt/lockImg");
        
        nameTxt.text = "";
        stateTxt.text = "";
        tipTxt.text = "";
        
        local unLockState, limitStar = self.heroVo:GetHeroAnimalUnlock(i);
        local animalId;
        local redState = false;
        if unLockState then
            animalId = NetHeroData:GetHeroAnimalId(self.heroId, i);
            if animalId then
                nameTxt.text = ItemConfig:GetLangByID(animalId);
                titleTxt.text = ItemConfig:GetLangByID(animalId);

                local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item, animalId);
                local animalCfg = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_hero_animal, animalId);
                if config and config.img_spine then
                    if self.animalSpineList[i] ~= nil then
                        UEGO.Destroy(self.animalSpineList[i]);
                    end
                    local prefab = ResMgr:LoadAssetSync(config.img_spine, AssetDefine.LoadType.Instant);
                    local obj = CreateGameObjectWithParent(prefab, clickBtn);
                    SetUIIndex(obj, 2);
                    self.animalSpineList[i] = obj;
                    
                    local posDic = animalCfg and Split1(animalCfg.offset, "|") or {0, -60, 0};
                    obj.transform.localPosition = Vector3(v2n(posDic[1]), v2n(posDic[2]), v2n(posDic[3]));
                    obj.transform.localScale = Vector3(100, 100, 100);

                    GameUtil.SetLayer(obj, 5, 1);
                    local render = obj.transform:GetComponentInChildren(typeof(UE.Renderer))
                    SetRendererOrder(render, SortingLayerInGame.Default, self:GetViewSortingOrder());

                    local canvas = GetComponent(redSp, UE.Canvas);
                    canvas.sortingOrder = self:GetViewSortingOrder() + 1;
                end
                
                if animalCfg then
                    local atkTxt = GetChild(attrItem, "attrObj/atkTxt", UEUI.Text);
                    local hpTxt = GetChild(attrItem, "attrObj/hpTxt", UEUI.Text);
                    local defTxt = GetChild(attrItem, "attrObj/defTxt", UEUI.Text);
                    atkTxt.text = NumToGameString(animalCfg.atk);
                    hpTxt.text = NumToGameString(animalCfg.life);
                    defTxt.text = NumToGameString(animalCfg.defense);

                    local txtList = { "m_txtAuto58006017", "m_txtAuto58006019", "m_txtAuto58006018" };
                    local widthDic = { 180, 134, 230 };
                    for i = 1, 3 do
                        local titleTxt = GetChild(attrItem, "attrObj/" .. txtList[i], UEUI.Text);
                        if titleTxt then
                            local contentSizeFitter = GetComponent(titleTxt, UEUI.ContentSizeFitter);
                            if contentSizeFitter then
                                local width = titleTxt.preferredWidth;
                                local maxWidth = widthDic[i];
                                contentSizeFitter.enabled = width <= maxWidth;
                                if width > maxWidth then
                                    SetUISize(titleTxt, maxWidth, 60);
                                end
                            end
                        end
                    end
                end
            else
                stateTxt.text = LangMgr:GetLang(58006036);
                titleTxt.text = LangMgr:GetLang(58006036);

                if typeListStr == nil then
                    typeListStr = "";
                    local typeList = self.heroVo:GetHeroAnimalTypeList();
                    local nameStr;
                    for j = 1, #typeList do
                        nameStr = HeroManager:GetHeroAnimalTypeName(typeList[j]);
                        if j == 1 then
                            typeListStr = nameStr;
                        else
                            typeListStr = typeListStr .. "、" .. nameStr;
                        end
                    end
                end
                tipTxt.text = LangMgr:GetLang(58006038) .. typeListStr;
                redState = len > 0;
            end

            if not redState then
                local curFight = HeroManager:GetHeroAnimalFight(animalId);
                for j = startIdx, len do
                    startIdx = j;
                    if not list[j].heroId then
                        local animalFight = HeroManager:GetHeroAnimalFight(list[j].itemId);
                        if maxFight < animalFight then
                            maxFight = animalFight;
                        end
                        if curFight < maxFight then
                            redState = true;
                            break;
                        end
                    end
                end
            end
        else
            stateTxt.text = LangMgr:GetLang(58006035);
            titleTxt.text = LangMgr:GetLang(58006035);
            tipTxt.text = LangMgr:GetLangFormat(58006037, limitStar);
        end
        SetActive(stateSp, unLockState and not animalId);
        SetActive(lockSp, not unLockState);
        SetActive(attrObj, unLockState and animalId);
        SetActive(lockImg, not unLockState);
        SetActive(redSp, redState and self.heroVo:IsShowHeroRed());
        
        if self.animalSpineList[i] then
            SetActive(self.animalSpineList[i], unLockState and animalId);
        end

        local contentSizeFitter = GetComponent(tipTxt, UEUI.ContentSizeFitter);
        if contentSizeFitter then
            local width = tipTxt.preferredWidth;
            contentSizeFitter.enabled = width <= 850;
            if width > 850 then
                SetUISize(tipTxt, 850, 70);
            end
        end
    end
    --self:SortOrderAllCom(true);
end

return UI_HeroAnimalPanel