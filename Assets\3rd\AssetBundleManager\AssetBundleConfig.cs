﻿using System.IO;
using UnityEngine;

namespace AssetBundles
{
        public class AssetBundleConfig
        {
                public static string ResPackageName = "ResPackage";
                public static string AtlasPath = "Atlas"; //图集所在文件夹
                public static string SpritePath = "Sprite"; //图集散图所在文件夹
                public static string ConfigPath = "Config"; //配置表所在文件夹
                public static string ABVariant = "";
                public static string LuaDirName = "LuaScripts";
                public static string LuaABName = "script";
                public static string LuaSuffix = ".bytes";
                public static string ConfigName = "config";

                public static string resVerName = "resver.bytes";
                public static string appVerName = "appver.bytes";
                public static string fileListName = "abcheck.bytes";
                public static string vresPath = "vres.bytes"; //cdn资源两份做刷新缓存
                public static string switchPath = "switch.bytes";//一些开关
                public static string appVerNextName = "noticever.bytes";

                public static string Prefab_UI = "Prefab/UI"; //UIprefab所在文件夹
                 public static string Prefab_BG = "Prefab/bg"; //UIprefab所在文件夹
        

        public static bool IsIgnoreRes(string name)
                {
                        if (name.EndsWith(resVerName) || name.EndsWith(appVerName) || name.EndsWith(fileListName) || name.EndsWith(switchPath) || name.EndsWith(appVerNextName))
                                return true;
                        return false;
                }
                public static bool IsIgnoreLangRes(string name)
                {
                         return false;
                        if (!name.StartsWith("config/lang"))
                        {
                                return false;
                        }
                        if (name.Equals("config/language_config") || name.Equals("config/langmult") || name.Equals("config/lang_tg"))
                         {
                                return false;
                         }
                        string tableName = LanguageManager.Instance.GetLangTableName().ToLowerInvariant();
                        if (name.Equals("config/" + tableName))
                                return true;
                        return false;
                }
                public static string GetABCode()
                {
                        return "J50ND0L.03#CDFP=";
                }

                public static string GetStreamingAssetsPath
                {
                        get
                        {
                                return Application.streamingAssetsPath
                                    + "/"
                                    + Utility.AssetBundlesOutputPath
                                    + "/"
                                    + Utility.GetPlatformName();
                        }
                }

                public static string GetPersistentDataPath
                {
                        get
                        {
                                return GameHelper.persistentDataPath
                                       + "/"
                                       + Utility.AssetBundlesOutputPath
                                       + "/"
                                       + Utility.GetPlatformName();
                        }
                }

                public static string GetLocalAssetDataPath
                {
                        get
                        {
                                return "Assets/" + ResPackageName + "/";
                        }
                }

                public static string GetLuaScriptPath
                {
                        get
                        {
                                return GetLocalAssetDataPath + LuaABName;
                        }
                }

                public static string GetAssetFullPath(string filePath)
                {
                        if (filePath.StartsWith(GetLocalAssetDataPath))
                        {
                                return filePath;
                        }
                        else
                        {
                                return GetLocalAssetDataPath + filePath;
                        }
                }

                public static string GetAssetName(string filePath)
                {
                        return GetAssetFullPath(filePath);
                }

                public static string GetAssetBundlePath(string filePath)
                {
                        if (IsPersistentExsits(filePath))
                        {
                                return GetPersistentDataPath + "/" + filePath;
                        }
                        else
                        {
                                return GetStreamingAssetsPath + "/" + filePath;
                        }
                }
                public static bool CheckHasFile(string filePath)
                {
                        if (IsPersistentExsits(filePath))
                        {
                                return true;
                        }
                        else
                        {
                                string path = GetStreamingAssetsPath + "/" + filePath;
                                return File.Exists(path);
                        }
                }
                public static bool IsPersistentExsits(string filePath)
                {
                        string path = GetPersistentDataPath + "/" + filePath;
                        return File.Exists(path);
                }

                //先处理下载目录是否存在文件，如果不存在读取包体内部的
                public static string GetLocalVersionFilePath(string filePath, out int pathType)
                {
                        if (IsPersistentExsits(filePath))
                        {
                                pathType = 0;
                                return GetPersistentDataPath + "/" + filePath;
                        }
                        else
                        {
                                pathType = 1;
                                return GetStreamingAssetsPath + "/" + filePath;
                        }
                }

                public static string GetAssetBundleName(string assetAbsolutePath)
                {
                        if (!assetAbsolutePath.StartsWith(GetLocalAssetDataPath))
                        {
                                LogMan.Error("###### GetAssetBundleName: " + assetAbsolutePath);
                                return assetAbsolutePath;
                        }

                        /*
                         * assetBundlePath : 
                         * Sprite/ui/ui_page_login/login_pop_5.png
                         * Atlas/ui_background.spritesAtlas
                         */
                        string assetBundleName = string.Empty;
                        string assetBundlePath = assetAbsolutePath.Substring(GetLocalAssetDataPath.Length);
                        string[] abSplit = assetBundlePath.Split('/');

                        if (abSplit[0] == AssetBundleConfig.LuaABName)
                        {
                                assetBundleName = abSplit[0];
                        }
                        else if (abSplit[0] == AssetBundleConfig.AtlasPath)
                        {
                                string[] altasABPath = assetBundlePath.Split('.');
                                assetBundleName = altasABPath[0];
                        }
                        else if (abSplit[0] == "Prefab" && abSplit[1] == "UI" && abSplit.Length == 3)
                        {
                                string[] altasABPath = assetBundlePath.Split('.');
                                assetBundleName = altasABPath[0];
                        }
                          else if (abSplit[0] == "Prefab" && abSplit[1] == "bg" && abSplit.Length == 3)
                        {
                                string[] altasABPath = assetBundlePath.Split('.');
                                assetBundleName = altasABPath[0];
                        }
                        // else if (abSplit[0] == AssetBundleConfig.ConfigPath)
                        // {
                        //         string[] altasABPath = assetBundlePath.Split('.');
                        //         assetBundleName = altasABPath[0];
                        // }
                        else
                        {
                                int index = assetBundlePath.LastIndexOf('/');
                                assetBundleName = assetBundlePath.Substring(0, index);
                        }

                        return assetBundleName.ToLowerInvariant();
                }
        }
}
