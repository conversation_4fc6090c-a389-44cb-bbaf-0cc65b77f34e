﻿

<linker>

	<assembly fullname="Assembly-CSharp">
	    <type fullname="LevelManager" preserve="all"/>
		<type fullname="Coroutine_Runner" preserve="all"/>
		<type fullname="ActionInfoList" preserve="all"/>
		<type fullname="OrganBaseComponent" preserve="all"/>
		<type fullname="OrganComponentData" preserve="all"/>
		<type fullname="LuaDebugTool" preserve="all"/>
		<type fullname="LuaPerfect.ObjectRef" preserve="all"/>
		<type fullname="LuaPerfect.ObjectItem" preserve="all"/>
		<type fullname="LuaPerfect.ObjectFormater" preserve="all"/>
		<type fullname="TopTrigger" preserve="all"/>
		<type fullname="GameAniManager" preserve="all"/>
		<type fullname="AnimationCurves" preserve="all"/>
		<type fullname="ButtonPressed" preserve="all"/>
		<type fullname="DG.Tweening.DOTweenAnimation" preserve="all"/>
		<type fullname="DG.Tweening.DOTweenModuleUI" preserve="all"/>
		<type fullname="Spine.Unity.SkeletonGraphic" preserve="all"/>
		<type fullname="Spine.Unity.SkeletonAnimation" preserve="all"/>
		<type fullname="Spine.Unity.SkeletonRenderer" preserve="all"/>
		<type fullname="Spine.Unity.SkeletonDataAsset" preserve="all"/>
		<type fullname="Spine.AnimationState" preserve="all"/>
		<type fullname="LuaManager" preserve="all"/>
		<type fullname="AssetManager" preserve="all"/>
		<type fullname="CoroutineRunner" preserve="all"/>
		<type fullname="ControlExpand" preserve="all"/>
		<type fullname="Rijndael" preserve="all"/>
		<type fullname="PlayerPrefsEx" preserve="all"/>
		<type fullname="AnimEvent" preserve="all"/>
		<type fullname="PageView" preserve="all"/>
		<type fullname="Mosframe.TableView" preserve="all"/>
		<type fullname="Mosframe.TableViewV" preserve="all"/>
		<type fullname="Mosframe.TableViewH" preserve="all"/>
		<type fullname="Mosframe.TableViewCell" preserve="all"/>
		<type fullname="GameHelper" preserve="all"/>
		<type fullname="StartGame" preserve="all"/>
		<type fullname="TouchMono" preserve="all"/>
		<type fullname="UIDrag" preserve="all"/>
		<type fullname="UIDragXYDir" preserve="all"/>
		<type fullname="UICapture" preserve="all"/>
		<type fullname="UIRoot" preserve="all"/>
		<type fullname="UIMask" preserve="all"/>
		<type fullname="GameNetWork.Net.GameHttp" preserve="all"/>
		<type fullname="HttpMono" preserve="all"/>
		<type fullname="LanguageManager" preserve="all"/>
		<type fullname="LogMan" preserve="all"/>
		<type fullname="ScriptExtend" preserve="all"/>
		<type fullname="UserDataMono" preserve="all"/>
		<type fullname="SortingLayerMono" preserve="all"/>
		<type fullname="TutorialBlock" preserve="all"/>
		<type fullname="StorageManager" preserve="all"/>
		<type fullname="NetworkEventMgr" preserve="all"/>
		<type fullname="NetworkWebSocketEventMgr" preserve="all"/>
		<type fullname="NetworkWebTCPEventMgr" preserve="all"/>
		<type fullname="CCTableView" preserve="all"/>
		<type fullname="CCTableViewCell" preserve="all"/>
		<type fullname="CCTableViewController" preserve="all"/>
		<type fullname="LuaSdkHelper" preserve="all"/>
		<type fullname="GameSdkManager" preserve="all"/>
		<type fullname="SDKLoginModule" preserve="all"/>
		<type fullname="MapTiled" preserve="all"/>
		<type fullname="MonoLinkLuaData" preserve="all"/>
		<type fullname="MaterialSelect" preserve="all"/>
		<type fullname="UnityEngineObjectExtention" preserve="all"/>
		<type fullname="UIScrollView" preserve="all"/>
		<type fullname="LinkImageText" preserve="all"/>
		<type fullname="HyperlinkText" preserve="all"/>
		<type fullname="bc.IFGame" preserve="all"/>
		<type fullname="bc.MiniGameBase.ILuaGameObject" preserve="all"/>
		<type fullname="bc.MiniGameBase.CollisionTriggerListener" preserve="all"/>
		<type fullname="bc.MiniGameBase.LuaContainer" preserve="all"/>
		<type fullname="bc.MiniGameBase.GameObjectComs" preserve="all"/>
		<type fullname="UI.UGUIExtendMini.ScrollRectItem" preserve="all"/>
		<type fullname="GameLuaBehaviour_New" preserve="all"/>
		<type fullname="bc.MiniGameBase.MiniVibration" preserve="all"/>
		
	</assembly>

	<assembly fullname="UnityEngine.CoreModule">
	    <type fullname="UnityEngine.WaitForSeconds" preserve="all"/>
		<type fullname="UnityEngine.WaitForEndOfFrame" preserve="all"/>
		<type fullname="UnityEngine.WaitForFixedUpdate" preserve="all"/>
		<type fullname="UnityEngine.WaitForSecondsRealtime" preserve="all"/>
		<type fullname="UnityEngine.WaitUntil" preserve="all"/>
		<type fullname="UnityEngine.WaitWhile" preserve="all"/>
		<type fullname="UnityEngine.Object" preserve="all"/>
		<type fullname="UnityEngine.Ray2D" preserve="all"/>
		<type fullname="UnityEngine.GameObject" preserve="all"/>
		<type fullname="UnityEngine.Component" preserve="all"/>
		<type fullname="UnityEngine.Behaviour" preserve="all"/>
		<type fullname="UnityEngine.Transform" preserve="all"/>
		<type fullname="UnityEngine.Resources" preserve="all"/>
		<type fullname="UnityEngine.TextAsset" preserve="all"/>
		<type fullname="UnityEngine.Keyframe" preserve="all"/>
		<type fullname="UnityEngine.AnimationCurve" preserve="all"/>
		<type fullname="UnityEngine.MonoBehaviour" preserve="all"/>
		<type fullname="UnityEngine.SkinnedMeshRenderer" preserve="all"/>
		<type fullname="UnityEngine.Renderer" preserve="all"/>
		<type fullname="UnityEngine.RuntimePlatform" preserve="all"/>
		<type fullname="UnityEngine.Application" preserve="all"/>
		<type fullname="UnityEngine.Debug" preserve="all"/>
		<type fullname="UnityEngine.Events.UnityEvent" preserve="all"/>
		<type fullname="UnityEngine.Bounds" preserve="all"/>
		<type fullname="UnityEngine.Color" preserve="all"/>
		<type fullname="UnityEngine.LayerMask" preserve="all"/>
		<type fullname="UnityEngine.Mathf" preserve="all"/>
		<type fullname="UnityEngine.Plane" preserve="all"/>
		<type fullname="UnityEngine.Quaternion" preserve="all"/>
		<type fullname="UnityEngine.Ray" preserve="all"/>
		<type fullname="UnityEngine.Time" preserve="all"/>
		<type fullname="UnityEngine.Vector2" preserve="all"/>
		<type fullname="UnityEngine.Vector3" preserve="all"/>
		<type fullname="UnityEngine.Vector4" preserve="all"/>
		<type fullname="UnityEngine.Rect" preserve="all"/>
		<type fullname="UnityEngine.RectTransform" preserve="all"/>
		<type fullname="UnityEngine.RectOffset" preserve="all"/>
		<type fullname="UnityEngine.Sprite" preserve="all"/>
		<type fullname="UnityEngine.ResourceRequest" preserve="all"/>
		<type fullname="UnityEngine.SceneManagement.SceneManager" preserve="all"/>
		<type fullname="UnityEngine.AsyncOperation" preserve="all"/>
		<type fullname="UnityEngine.SystemInfo" preserve="all"/>
		<type fullname="UnityEngine.Random" preserve="all"/>
		<type fullname="UnityEngine.Screen" preserve="all"/>
		<type fullname="UnityEngine.MeshRenderer" preserve="all"/>
		<type fullname="UnityEngine.Color32" preserve="all"/>
		<type fullname="UnityEngine.Material" preserve="all"/>
		<type fullname="UnityEngine.TrailRenderer" preserve="all"/>
		<type fullname="UnityEngine.LineRenderer" preserve="all"/>
		<type fullname="UnityEngine.SpriteRenderer" preserve="all"/>
		<type fullname="UnityEngine.RenderTexture" preserve="all"/>
		<type fullname="UnityEngine.RenderTextureFormat" preserve="all"/>
		<type fullname="UnityEngine.RenderTextureReadWrite" preserve="all"/>
		<type fullname="UnityEngine.MeshFilter" preserve="all"/>
		<type fullname="UnityEngine.Vector3Int" preserve="all"/>
		<type fullname="UnityEngine.Space" preserve="all"/>
		<type fullname="UnityEngine.QualitySettings" preserve="all"/>
		<type fullname="UnityEngine.RenderSettings" preserve="all"/>
		
	</assembly>

	<assembly fullname="UIEffect">
	    <type fullname="Coffee.UIEffects.UIShadow" preserve="all"/>
		
	</assembly>

	<assembly fullname="mscorlib">
	    <type fullname="System.Object" preserve="all"/>
		<type fullname="System.Collections.Generic.List`1[System.Int32]" preserve="all"/>
		<type fullname="System.Collections.Generic.Dictionary`2[System.String,System.String]" preserve="all"/>
		<type fullname="System.Collections.Generic.Dictionary`2[System.String,UnityEngine.GameObject]" preserve="all"/>
		<type fullname="System.GC" preserve="all"/>
		
	</assembly>

	<assembly fullname="UnityEngine.AnimationModule">
	    <type fullname="UnityEngine.Animation" preserve="all"/>
		<type fullname="UnityEngine.AnimationState" preserve="all"/>
		<type fullname="UnityEngine.Animator" preserve="all"/>
		<type fullname="UnityEngine.AnimationClip" preserve="all"/>
		<type fullname="UnityEngine.AnimatorStateInfo" preserve="all"/>
		
	</assembly>

	<assembly fullname="UnityEngine.ParticleSystemModule">
	    <type fullname="UnityEngine.ParticleSystem" preserve="all"/>
		
	</assembly>

	<assembly fullname="UnityEngine.UIModule">
	    <type fullname="UnityEngine.CanvasGroup" preserve="all"/>
		<type fullname="UnityEngine.RenderMode" preserve="all"/>
		<type fullname="UnityEngine.Canvas" preserve="all"/>
		<type fullname="UnityEngine.RectTransformUtility" preserve="all"/>
		
	</assembly>

	<assembly fullname="UnityEngine.PhysicsModule">
	    <type fullname="UnityEngine.RaycastHit" preserve="all"/>
		<type fullname="UnityEngine.CapsuleCollider" preserve="all"/>
		<type fullname="UnityEngine.Physics" preserve="all"/>
		<type fullname="UnityEngine.SphereCollider" preserve="all"/>
		<type fullname="UnityEngine.BoxCollider" preserve="all"/>
		<type fullname="UnityEngine.Collider" preserve="all"/>
		<type fullname="UnityEngine.Rigidbody" preserve="all"/>
		<type fullname="UnityEngine.Collision" preserve="all"/>
		<type fullname="UnityEngine.MeshCollider" preserve="all"/>
		
	</assembly>

	<assembly fullname="UnityEngine.InputLegacyModule">
	    <type fullname="UnityEngine.Touch" preserve="all"/>
		<type fullname="UnityEngine.TouchPhase" preserve="all"/>
		<type fullname="UnityEngine.Input" preserve="all"/>
		
	</assembly>

	<assembly fullname="UnityEngine.UI">
	    <type fullname="UnityEngine.UI.CanvasScaler" preserve="all"/>
		<type fullname="UnityEngine.UI.CanvasScaler+ScaleMode" preserve="all"/>
		<type fullname="UnityEngine.UI.CanvasScaler+ScreenMatchMode" preserve="all"/>
		<type fullname="UnityEngine.UI.GraphicRaycaster" preserve="all"/>
		<type fullname="UnityEngine.UI.Text" preserve="all"/>
		<type fullname="UnityEngine.UI.InputField" preserve="all"/>
		<type fullname="UnityEngine.UI.Button" preserve="all"/>
		<type fullname="UnityEngine.UI.Image" preserve="all"/>
		<type fullname="UnityEngine.UI.RawImage" preserve="all"/>
		<type fullname="UnityEngine.UI.ScrollRect" preserve="all"/>
		<type fullname="UnityEngine.UI.Scrollbar" preserve="all"/>
		<type fullname="UnityEngine.UI.Toggle" preserve="all"/>
		<type fullname="UnityEngine.UI.ToggleGroup" preserve="all"/>
		<type fullname="UnityEngine.UI.Button+ButtonClickedEvent" preserve="all"/>
		<type fullname="UnityEngine.UI.ScrollRect+ScrollRectEvent" preserve="all"/>
		<type fullname="UnityEngine.UI.GridLayoutGroup" preserve="all"/>
		<type fullname="UnityEngine.UI.ContentSizeFitter" preserve="all"/>
		<type fullname="UnityEngine.UI.Slider" preserve="all"/>
		<type fullname="UnityEngine.UI.Dropdown" preserve="all"/>
		<type fullname="UnityEngine.UI.Dropdown+OptionData" preserve="all"/>
		<type fullname="UnityEngine.UI.Dropdown+OptionDataList" preserve="all"/>
		<type fullname="UnityEngine.UI.Dropdown+DropdownEvent" preserve="all"/>
		<type fullname="UnityEngine.UI.HorizontalLayoutGroup" preserve="all"/>
		<type fullname="UnityEngine.EventSystems.EventSystem" preserve="all"/>
		<type fullname="UnityEngine.UI.LayoutRebuilder" preserve="all"/>
		<type fullname="UnityEngine.EventSystems.EventTriggerType" preserve="all"/>
		<type fullname="UnityEngine.EventSystems.EventTrigger+Entry" preserve="all"/>
		<type fullname="UnityEngine.EventSystems.EventTrigger" preserve="all"/>
		<type fullname="UnityEngine.UI.LayoutElement" preserve="all"/>
		<type fullname="UnityEngine.UI.VerticalLayoutGroup" preserve="all"/>
		
	</assembly>

	<assembly fullname="wx-runtime-editor">
	    <type fullname="PlayerPrefs" preserve="all"/>
		
	</assembly>

	<assembly fullname="DOTween">
	    <type fullname="DG.Tweening.TweenSettingsExtensions" preserve="all"/>
		<type fullname="DG.Tweening.AutoPlay" preserve="all"/>
		<type fullname="DG.Tweening.AxisConstraint" preserve="all"/>
		<type fullname="DG.Tweening.Ease" preserve="all"/>
		<type fullname="DG.Tweening.LogBehaviour" preserve="all"/>
		<type fullname="DG.Tweening.LoopType" preserve="all"/>
		<type fullname="DG.Tweening.PathMode" preserve="all"/>
		<type fullname="DG.Tweening.PathType" preserve="all"/>
		<type fullname="DG.Tweening.RotateMode" preserve="all"/>
		<type fullname="DG.Tweening.ScrambleMode" preserve="all"/>
		<type fullname="DG.Tweening.TweenType" preserve="all"/>
		<type fullname="DG.Tweening.UpdateType" preserve="all"/>
		<type fullname="DG.Tweening.DOTween" preserve="all"/>
		<type fullname="DG.Tweening.DOVirtual" preserve="all"/>
		<type fullname="DG.Tweening.EaseFactory" preserve="all"/>
		<type fullname="DG.Tweening.Tweener" preserve="all"/>
		<type fullname="DG.Tweening.Tween" preserve="all"/>
		<type fullname="DG.Tweening.Sequence" preserve="all"/>
		<type fullname="DG.Tweening.TweenParams" preserve="all"/>
		<type fullname="DG.Tweening.Core.ABSSequentiable" preserve="all"/>
		<type fullname="DG.Tweening.Core.TweenerCore`3[UnityEngine.Vector3,UnityEngine.Vector3,DG.Tweening.Plugins.Options.VectorOptions]" preserve="all"/>
		<type fullname="DG.Tweening.Core.TweenerCore`3[UnityEngine.Vector3,DG.Tweening.Plugins.Core.PathCore.Path,DG.Tweening.Plugins.Options.PathOptions]" preserve="all"/>
		<type fullname="DG.Tweening.TweenExtensions" preserve="all"/>
		<type fullname="DG.Tweening.ShortcutExtensions" preserve="all"/>
		
	</assembly>

	<assembly fullname="DOTween43">
	    <type fullname="DG.Tweening.ShortcutExtensions43" preserve="all"/>
		
	</assembly>

	<assembly fullname="DOTween46">
	    <type fullname="DG.Tweening.ShortcutExtensions46" preserve="all"/>
		
	</assembly>

	<assembly fullname="DOTween50">
	    <type fullname="DG.Tweening.ShortcutExtensions50" preserve="all"/>
		
	</assembly>

	<assembly fullname="DOTweenPro">
	    <type fullname="DG.Tweening.DOTweenPath" preserve="all"/>
		
	</assembly>

	<assembly fullname="UnityEngine.Physics2DModule">
	    <type fullname="UnityEngine.BoxCollider2D" preserve="all"/>
		<type fullname="UnityEngine.Rigidbody2D" preserve="all"/>
		<type fullname="UnityEngine.Physics2D" preserve="all"/>
		<type fullname="UnityEngine.CircleCollider2D" preserve="all"/>
		<type fullname="UnityEngine.RelativeJoint2D" preserve="all"/>
		<type fullname="UnityEngine.CapsuleCollider2D" preserve="all"/>
		<type fullname="UnityEngine.Collision2D" preserve="all"/>
		<type fullname="UnityEngine.Collider2D" preserve="all"/>
		<type fullname="UnityEngine.PolygonCollider2D" preserve="all"/>
		<type fullname="UnityEngine.RigidbodyType2D" preserve="all"/>
		<type fullname="UnityEngine.RaycastHit2D" preserve="all"/>
		
	</assembly>

	<assembly fullname="UnityEngine.AudioModule">
	    <type fullname="UnityEngine.AudioSource" preserve="all"/>
		
	</assembly>

	<assembly fullname="Unity.TextMeshPro">
	    <type fullname="TMPro.TextMeshProUGUI" preserve="all"/>
		<type fullname="TMPro.TMP_InputField" preserve="all"/>
		<type fullname="TMPro.TextMeshPro" preserve="all"/>
		
	</assembly>

</linker>