﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using UnityEngine;

public class SDKUtility
{
        #region Http 
        /// <summary>
        /// 按照字典序排序 
        /// </summary>
        public static List<string> SortNetData(Dictionary<string, string> Params)
        {
                List<string> keys = new List<string>();
                Dictionary<string, string>.Enumerator tParamsSorted = Params.GetEnumerator();
                while (tParamsSorted.MoveNext())
                        keys.Add(tParamsSorted.Current.Key);

                tParamsSorted.Dispose();

                keys.Sort((str1, str2) => { return str1.CompareTo(str2); });

                return keys;
        }

        /// <summary>
        /// http get方式 的url传参拼接
        /// </summary>
        public static string TranslateData(Dictionary<string, string> tParams)
        {
                if (tParams == null)
                        return null;

                StringBuilder message = new StringBuilder();

                message.Append("?");
                foreach (var pairs in tParams)
                {
                        message.Append(pairs.Key + "=" + pairs.Value + "&");
                }
                message.Remove(message.Length - 1, 1);

                return message.ToString();
        }

        /// <summary>
        /// Get 方式的签名函数
        /// </summary>
        public static string GetPacketSign(Dictionary<string, string> tParams, string signKey)
        {
                if (tParams == null)
                        return null;
                List<string> sortKeys = SortNetData(tParams);

                StringBuilder sig = new StringBuilder();
                for (int i = 0; i < sortKeys.Count; i++)
                {
                        sig.Append(sortKeys[i]);
                        sig.Append("=");
                        sig.Append(tParams[sortKeys[i]]);
                }
                sortKeys = null;

                sig.Append(signKey);
                string sigStrOut = NetMD5(sig.ToString());
                sig = null;

                return sigStrOut;
        }

        #endregion

        #region MD5
        public static string MD5String(string jsonData)
        {
                string md5Str = string.Empty;
                MD5 md5 = MD5.Create();
                byte[] content = Encoding.ASCII.GetBytes(jsonData);
                byte[] hashArray = md5.ComputeHash(content);
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < hashArray.Length; i++)
                {
                        sb.Append(hashArray[i].ToString("x2"));
                }
                md5Str = sb.ToString();
                return md5Str;
        }

        public static string MD5FromFile(string filePath)
        {
                if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                        return string.Empty;

                string content = File.ReadAllText(filePath, Encoding.UTF8);
                return MD5String(content);
        }

        public static string NetMD5(string s)
        {
                byte[] sou = Encoding.UTF8.GetBytes(s);
                byte[] data = new MD5CryptoServiceProvider().ComputeHash(sou);
                string result = System.BitConverter.ToString(data);
                sou = null;
                data = null;
                return result.Replace("-", "").ToLower();
        }
        #endregion

        #region UID
#if UNITY_IOS
    [DllImport("__Internal")]
    private static extern string Get_UUID_By_KeyChain();
#endif

        public static string GetDeviceUID()
        {
                string uid = string.Empty;
#if UNITY_EDITOR || UNITY_ANDROID
                uid = SystemInfo.deviceUniqueIdentifier;
#elif UNITY_IOS
      uid = Get_UUID_By_KeyChain();
#endif
                Debug.Log("=> uid:" + uid);
                return uid;
        }




        #endregion


        #region TimeStamp

        public static long GetTimeStamp()
        {
                return (DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / 10000000;
        }

        #endregion
        /// <summary>
        /// 字符串格式化为字典
        /// </summary>
        /// <param name="param"> "key|value;key|value;"</param>
        /// <returns></returns>
        public static Dictionary<string, object> StringFormatDic(string param)
        {
                Dictionary<string, object> eventValues = new Dictionary<string, object>();
                if (string.IsNullOrEmpty(param)) return eventValues;

                string[] keyvalues = param.Split(';');
                if (keyvalues == null || keyvalues.Length <= 0) return eventValues;

                for (int i = 0; i < keyvalues.Length; i++)
                {
                        string[] valus = keyvalues[i].Split('|');
                        if (valus == null || valus.Length != 2) continue;
                        eventValues.Add(valus[0], valus[1]);
                }

                return eventValues;
        }

        static public string EscapeStr(string input)
        {
                var output = input
                    .Replace("\\", @"\\")
                        .Replace("\'", @"\'")
                        .Replace("\"", @"\""")
                        .Replace("\n", @"\n")
                        .Replace("\t", @"\t")
                        .Replace("\r", @"\r")
                        .Replace("\b", @"\b")
                        .Replace("\f", @"\f")
                        .Replace("\a", @"\a")
                        .Replace("\v", @"\v")
                        .Replace("\0", @"\0");
                /*          var surrogateMin = (char)0xD800;
                var surrogateMax = (char)0xDFFF;
                for (char sur = surrogateMin; sur <= surrogateMax; sur++)
                    output.Replace(sur, '\uFFFD');*/
                return output;
        }


        public static bool EmailIsMatch(string emailString)
        {
                Regex RegEmail = new Regex(@"^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$");
                Match m = RegEmail.Match(emailString);
                return m.Success;
        }

        public static string GetOSVer()
        {
#if UNITY_EDITOR
                return System.Environment.OSVersion.Version.ToString();
#elif UNITY_ANDROID
        return Platform_GetOsVersion();
#else
        return string.Empty;
#endif
        }

        public static string DEF_STR_PACKAGE_ACTIVITY = "com.unity3d.player.UnityPlayer";
        public static AndroidJavaClass s_MainClass = null;
        public static AndroidJavaClass shareUnityPlayer()
        {
                if (s_MainClass == null)
                        s_MainClass = new AndroidJavaClass(DEF_STR_PACKAGE_ACTIVITY);
                return s_MainClass;
        }

        public static AndroidJavaObject s_MainActivity = null;
        public static AndroidJavaObject shareActivity()
        {
                if (s_MainActivity == null)
                        s_MainActivity = shareUnityPlayer().GetStatic<AndroidJavaObject>("currentActivity");
                return s_MainActivity;
        }

        public static string DEF_STR_PACKAGE_BRIGE = "com.jndl.tools.ActivityTools";
        public static AndroidJavaClass s_BrigeJni = null;
        public static AndroidJavaClass shareBrigeJni()
        {
                if (s_BrigeJni == null)
                        s_BrigeJni = new AndroidJavaClass(DEF_STR_PACKAGE_BRIGE);
                return s_BrigeJni;
        }

        //////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        public static string Platform_GetOsVersion()
        {
                return shareBrigeJni().CallStatic<string>("GetOsVersion");
        }

        public static string Platform_GetDeviceUID()
        {
                return shareBrigeJni().CallStatic<string>("GetDeviceUID");
        }

#if !UNITY_EDITOR && UNITY_IOS
    [DllImport("__Internal")]
    private static extern string Get_IDFA();

    [DllImport("__Internal")]
    private static extern void _RequestTrackingAuthorizationWithCompletionHandler();

    [DllImport("__Internal")]
    private static extern int _GetAppTrackingAuthorizationStatus();
#endif

        /// <summary>
        /// 请求ATT授权窗口
        /// </summary>
        /// <param name="getResult"></param>
        public static void RequestTrackingAuthorizationWithCompletionHandler(Action<int> getResult)
        {
                //-1:"ios版本低于14"
                //0: "ATT 授权状态待定";
                //1: "ATT 授权状态受限";
                //2: "ATT 已拒绝";
                //3: "ATT 已授权";
#if !UNITY_EDITOR && UNITY_IOS
        _RequestTrackingAuthorizationWithCompletionHandler();
#endif

        }

        /// <summary>
        /// 获取当前ATT授权状态
        /// </summary>
        /// <returns></returns>
        public static int GetAppTrackingAuthorizationStatus()
        {
#if !UNITY_EDITOR && UNITY_IOS
        return _GetAppTrackingAuthorizationStatus();
#endif
                return 0;
        }


        public static string GetAdvertisingID()
        {
                string _strAdvertisingID = string.Empty;
#if !UNITY_EDITOR && UNITY_ANDROID
       try
        {
            using (AndroidJavaClass up = new AndroidJavaClass("com.unity3d.player.UnityPlayer"))
            {
                using (AndroidJavaObject currentActivity = up.GetStatic<AndroidJavaObject>("currentActivity"))
                {
                    using (AndroidJavaClass client = new AndroidJavaClass("com.google.android.gms.ads.identifier.AdvertisingIdClient"))
                    {
                        using (AndroidJavaObject adInfo = client.CallStatic<AndroidJavaObject>("getAdvertisingIdInfo", currentActivity))
                        {
                            if (adInfo != null)
                            {
                                _strAdvertisingID = adInfo.Call<string>("getId");
                                if (string.IsNullOrEmpty(_strAdvertisingID))
                                    _strAdvertisingID = "none";
                            }
                        }
                    }
                }
            }
        }
        catch (System.Exception e)
        {
            Debug.Log(e.Message);
        }
#elif !UNITY_EDITOR && UNITY_IOS
        if (GetAppTrackingAuthorizationStatus() == 3)
        {
            _strAdvertisingID = Get_IDFA();
        }
#endif

                return _strAdvertisingID;
        }

        public static void InputGuideInfo(string uid)
        {
                string guideText = "{\"1\":\"没有描述\",\"2\":\"合成仙子花\",\"3\":\"合成第一个英雄\",\"4\":\"唤醒睡美人\",\"5\":\"获得道具触发\",\"6\":\"获得道具触发\",\"7\":\"移动镜头\",\"8\":\"获得道具触发\",\"9\":\"等级触发\",\"10\":\"获得魔棒\",\"11\":\"指向魔棒\",\"12\":\"指向解锁地块\",\"20\":\"英雄施法引导\",\"21\":\"施法按钮与箭头\",\"22\":\"等待施法完成\",\"23\":\"点击英雄\",\"24\":\"箭头指向英雄准备掉落\",\"25\":\"英雄恢复移动\",\"26\":\"界面层级\",\"27\":\"箭头指向开启按钮\",\"28\":\"箭头指向掉落材料\",\"29\":\"箭头指向船坞升级按钮\",\"30\":\"升级船坞，开通订单\",\"31\":\"指引订单1\",\"32\":\"指引订单2\",\"40\":\"开通藏品\",\"50\":\"五合二引导\",\"51\":\"引导提示\",\"52\":\"引导结束提示\",\"53\":\"开通删除功能\",\"60\":\"免费宝箱引导\",\"61\":\"任务界面指引箭头\",\"62\":\"开通市场按钮\",\"63\":\"开放市场引导\",\"64\":\"开放引导按钮\",\"70\":\"宝石引导合成\",\"71\":\"宝石引导\",\"72\":\"显示宝石按钮\",\"73\":\"开启每日任务按钮\",\"74\":\"每日任务开启\",\"75\":\"7级开启地图切换入口\",\"80\":\"红工人引导\",\"81\":\"红工人修建\",\"82\":\"红工人弹窗\",\"83\":\"红工人点击结束引导\",\"90\":\"挖掘铲子引导\",\"91\":\"挖掘铲子引导弹窗\",\"92\":\"挖掘铲子引导结束\",\"100\":\"首次出现特殊订单弹指引引导\"}";

                string fileName = uid + "_net_1.bytes";

                string path = GameHelper.persistentDataPath + "/GameData/" + fileName;
                string data = FileUtility.SafeReadAllText(path);
                if (data != null)
                {
                        StringBuilder stringBuild = new StringBuilder();
                        stringBuild.Append("<color=green>根据存档数据获取玩家缺失的引导数据（红色为缺失数据）</color>" + "\n");
                        LitJson.JsonData jd_net = LitJson.JsonMapper.ToObject(data);
                        if (jd_net != null)
                        {
                                if (jd_net.ContainsKey("14"))
                                {
                                        LitJson.JsonData flags = jd_net["14"]["map_1"]["flags"];
                                        LitJson.JsonData jd_guide = LitJson.JsonMapper.ToObject(guideText);
                                        for (int i = 1; i <= 100; i++)
                                        {
                                                if (i == 20 || i == 40 || i == 50 || i == 60 || i == 70 || i == 73 || i == 75 || i == 80 || i == 90 || i == 100)
                                                {
                                                        stringBuild.AppendLine("----------------------------下一阶段--------------------------");
                                                }

                                                if (flags.ContainsKey(i.ToString()))
                                                {
                                                        stringBuild.AppendLine(string.Format("<color=white>{0}</color>", i + ": " + jd_guide[i.ToString()].ToString()));
                                                }
                                                else if (jd_guide.ContainsKey(i.ToString()))
                                                {
                                                        stringBuild.AppendLine(string.Format("<color=red>{0}</color>", i + ": " + jd_guide[i.ToString()].ToString()));
                                                }
                                        }

                                        LogMan.Info(stringBuild.ToString());
                                }
                        }
                }
        }
}
