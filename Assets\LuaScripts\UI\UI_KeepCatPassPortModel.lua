local UI_KeepCatPassPortModel = {}

UI_KeepCatPassPortModel.config = {["name"] = "UI_KeepCatPassPort", ["layer"] = UILayerType.Normal, ["type"] = UIType.Pop, ["isAutoClose"] = true, ["anim"] = 1,["background"] = 1, ["onEscape"] = false, ["tinyGamePath"] = nil}

function UI_KeepCatPassPortModel:Init(c)
    c.ui = {}    
    c.ui.m_panelUI = GetChild(c.uiGameObject,"m_panelUI")
    c.ui.m_btnClose = GetChild(c.uiGameObject,"m_panelUI/Go/m_btnClose",UEUI.Button)
    c.ui.m_btnVip = GetChild(c.uiGameObject,"m_panelUI/Go/bgContent/imgMap/m_btnVip",UEUI.Button)
    c.ui.m_txtRePrice = GetChild(c.uiGameObject,"m_panelUI/Go/bgContent/imgMap/m_btnVip/centerObj/m_txtRePrice",UEUI.Text)
    c.ui.m_btnVipTips = GetChild(c.uiGameObject,"m_panelUI/Go/bgContent/imgMap/m_btnVipTips",UEUI.Button)
    c.ui.m_govipTag = GetChild(c.uiGameObject,"m_panelUI/Go/bgContent/imgMap/m_govipTag")
    c.ui.m_imgVipTag = GetChild(c.uiGameObject,"m_panelUI/Go/bgContent/imgMap/m_imgVipTag",UEUI.Image)
    c.ui.m_txtTime = GetChild(c.uiGameObject,"m_panelUI/Go/bgContent/imgMap/m_txtTime",UEUI.Text)
    c.ui.m_slider3 = GetChild(c.uiGameObject,"m_panelUI/Go/bgContent/RightBG/fill_part/m_slider3",UEUI.Slider)
    c.ui.m_imgPassIcon = GetChild(c.uiGameObject,"m_panelUI/Go/bgContent/RightBG/fill_part/hor_list/m_imgPassIcon",UEUI.Image)
    c.ui.m_txtScore = GetChild(c.uiGameObject,"m_panelUI/Go/bgContent/RightBG/fill_part/hor_list/m_txtScore",UEUI.Text)
    c.ui.m_txtLevel = GetChild(c.uiGameObject,"m_panelUI/Go/bgContent/RightBG/fill_part/hor_list/Image (1)/m_txtLevel",UEUI.Text)
    c.ui.m_scrollview = GetChild(c.uiGameObject,"m_panelUI/Go/bgContent/RightBG/m_scrollview",UEUI.ScrollRect)
    c.ui.m_transItemParent = GetChild(c.uiGameObject,"m_panelUI/Go/bgContent/RightBG/m_scrollview/Viewport/m_transItemParent",UE.Transform)
    c.ui.m_goItem = GetChild(c.uiGameObject,"m_panelUI/Go/bgContent/RightBG/m_scrollview/Viewport/m_transItemParent/m_goItem")
    c.ui.m_btnChest = GetChild(c.uiGameObject,"m_panelUI/Go/bgContent/RightBG/chest_root/m_btnChest",UEUI.Button)
    c.ui.m_imgChestLock = GetChild(c.uiGameObject,"m_panelUI/Go/bgContent/RightBG/chest_root/m_imgChestLock",UEUI.Image)
    c.ui.m_goChestNumBg = GetChild(c.uiGameObject,"m_panelUI/Go/bgContent/RightBG/chest_root/m_goChestNumBg")
    c.ui.m_txtChestNum = GetChild(c.uiGameObject,"m_panelUI/Go/bgContent/RightBG/chest_root/m_goChestNumBg/m_txtChestNum",UEUI.Text)
    c.ui.m_goSliderChestBg = GetChild(c.uiGameObject,"m_panelUI/Go/bgContent/RightBG/chest_root/m_goSliderChestBg")
    c.ui.m_sliderChest = GetChild(c.uiGameObject,"m_panelUI/Go/bgContent/RightBG/chest_root/m_goSliderChestBg/m_sliderChest",UEUI.Slider)
    c.ui.m_txtChestProgress = GetChild(c.uiGameObject,"m_panelUI/Go/bgContent/RightBG/chest_root/m_goSliderChestBg/m_txtChestProgress",UEUI.Text)
    c.ui.m_goBox = GetChild(c.uiGameObject,"m_panelUI/Go/bgContent/RightBG/chest_root/m_goBox")
    c.ui.m_txtDesc = GetChild(c.uiGameObject,"m_panelUI/m_txtDesc",UEUI.Text)
    c.ui.m_btnHelp = GetChild(c.uiGameObject,"m_panelUI/m_btnHelp",UEUI.Button)
    c.ui.m_goEffectBg = GetChild(c.uiGameObject,"m_goEffectBg")
    c.ui.m_goTarget = GetChild(c.uiGameObject,"m_goTarget")
    c.ui.m_goPanel = GetChild(c.uiGameObject,"m_goTarget/m_goPanel")
    c.ui.m_goTarget2 = GetChild(c.uiGameObject,"m_goTarget2")
    c.ui.m_goGetTarget = GetChild(c.uiGameObject,"m_goGetTarget")
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_KeepCatPassPortModel