﻿#if USE_UNI_LUA
using LuaAPI = UniLua.Lua;
using RealStatePtr = UniLua.ILuaState;
using LuaCSFunction = UniLua.CSharpFunctionDelegate;
#else
using LuaAPI = XLua.LuaDLL.Lua;
using RealStatePtr = System.IntPtr;
using LuaCSFunction = XLua.LuaDLL.lua_CSFunction;
#endif

using XLua;
using System.Collections.Generic;


namespace XLua.CSObjectWrap
{
    using Utils = XLua.Utils;
    public class NetworkWebTCPEventMgrWrap 
    {
        public static void __Register(RealStatePtr L)
        {
			ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			System.Type type = typeof(NetworkWebTCPEventMgr);
			Utils.BeginObjectRegister(type, L, translator, 0, 0, 0, 0);
			
			
			
			
			
			
			Utils.EndObjectRegister(type, L, translator, null, null,
			    null, null, null);

		    Utils.BeginClassRegister(type, L, __CreateInstance, 6, 1, 0);
			Utils.RegisterFunc(L, Utils.CLS_IDX, "StartConnect", _m_StartConnect_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "StartConnect2", _m_StartConnect2_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SendMessage", _m_SendMessage_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "IsConnected", _m_IsConnected_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "Disconnect", _m_Disconnect_xlua_st_);
            
			
            
			Utils.RegisterFunc(L, Utils.CLS_GETTER_IDX, "Instance", _g_get_Instance);
            
			
			
			Utils.EndClassRegister(type, L, translator);
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int __CreateInstance(RealStatePtr L)
        {
            
			try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
				if(LuaAPI.lua_gettop(L) == 1)
				{
					
					NetworkWebTCPEventMgr gen_ret = new NetworkWebTCPEventMgr();
					translator.Push(L, gen_ret);
                    
					return 1;
				}
				
			}
			catch(System.Exception gen_e) {
				return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
			}
            return LuaAPI.luaL_error(L, "invalid arguments to NetworkWebTCPEventMgr constructor!");
            
        }
        
		
        
		
        
        
        
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_StartConnect_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    string _host = LuaAPI.lua_tostring(L, 1);
                    int _port = LuaAPI.xlua_tointeger(L, 2);
                    System.Action<int, long, int, byte[]> _luaCallback = translator.GetDelegate<System.Action<int, long, int, byte[]>>(L, 3);
                    
                        long gen_ret = NetworkWebTCPEventMgr.StartConnect( _host, _port, _luaCallback );

                        LuaAPI.lua_pushint64(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_StartConnect2_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    string _host = LuaAPI.lua_tostring(L, 1);
                    int _port = LuaAPI.xlua_tointeger(L, 2);
                    System.Action<int, long, int, byte[]> _luaCallback = translator.GetDelegate<System.Action<int, long, int, byte[]>>(L, 3);
                    
                        long gen_ret = NetworkWebTCPEventMgr.StartConnect2( _host, _port, _luaCallback );

                        LuaAPI.lua_pushint64(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SendMessage_xlua_st_(RealStatePtr L)
        {
		    try {
            
            
            
                
                {
                    long _iConnID = LuaAPI.lua_toint64(L, 1);
                    int _iProtoID = LuaAPI.xlua_tointeger(L, 2);
                    byte[] _msg = LuaAPI.lua_tobytes(L, 3);
                    
                    NetworkWebTCPEventMgr.SendMessage( _iConnID, _iProtoID, _msg );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_IsConnected_xlua_st_(RealStatePtr L)
        {
		    try {
            
            
            
                
                {
                    long _iConnID = LuaAPI.lua_toint64(L, 1);
                    
                        bool gen_ret = NetworkWebTCPEventMgr.IsConnected( _iConnID );

                        LuaAPI.lua_pushboolean(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_Disconnect_xlua_st_(RealStatePtr L)
        {
		    try {
            
            
            
                
                {
                    long _iConnID = LuaAPI.lua_toint64(L, 1);
                    
                    NetworkWebTCPEventMgr.Disconnect( _iConnID );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        
        
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _g_get_Instance(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			    translator.Push(L, NetworkWebTCPEventMgr.Instance);
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 1;
        }
        
        
        
		
		
		
		
    }
}
