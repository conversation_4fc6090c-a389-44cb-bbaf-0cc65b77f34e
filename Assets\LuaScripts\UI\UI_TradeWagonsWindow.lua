local UI_TradeWagonsWindow = Class(BaseView)
local GoTradeTrain = require("UI.GoTradeTrain")
local bgMoveSpeed = 1
local bgOffset = 34
local OriginBgHeight = 1920
local OriginTrainPosY = 494

local ParkingIcon = {
    [1] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win2_chewei.png",
    [2] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win2_chewei2.png",
}

local TrainIcon = {
    [1] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win2_huocetou1.png",
    [2] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win2_huocetou2.png",
}

local CurSelectWagon

function UI_TradeWagonsWindow:OnInit()

end

function UI_TradeWagonsWindow:OnCreate(isGuide)
    -- self.canMoveBg = true
    -- self.bgRect = GetComponent(self.ui.m_imgBg, UE.RectTransform)
    -- self.bgRect2 = GetComponent(self.ui.m_imgBg2, UE.RectTransform)
    -- self.firstBgRect = self.bgRect
    -- self.secondBgRect = self.bgRect2
    -- self.firstMoveX = self.firstBgRect.rect.width
    -- self.secondmoveX = self.secondBgRect.rect.width
    -- self.firstPosition = self.bgRect.transform.localPosition
    -- self.secondPosition = self.bgRect2.transform.localPosition
    self.readyLangStr = LangMgr:GetLang(70001057)

    local leagueId = LeagueManager:GetMyLeagueId()
    self.isJoinTeam = leagueId and leagueId > 0

    self.wagons = {}
    self.tradeCarSelectEffect = {}
    self.transport_train = {}
    self.ready_train = {}
    self.trains = {}
    self.hadTrainExit = false

    if not self.goTradeTrain then
        self:InitTrain()
    end
    self.fitBgRect = GetComponent(self.ui.m_imgFitBg, UE.RectTransform)
    self.fitBgHeight = self.fitBgRect.rect.height

    SetActive(self.ui.m_goWagonItem, false)
    SetActive(self.ui.m_goTradeTrain, false)
    SetActive(self.ui.m_goTrainItem, false)

    self:RefreshWagon(TradeWagonsManager.loadInfo.trade_index_info)
    self:RefreshPanel()

    self:RefreshHeroInfo()

    self:RefreshTrain()

    self:SetIsUpdateTick(true)

    -- 引导中
    if isGuide then
        self:Guide()
    end

    local txtJoinTeam = GetChild(self.ui.m_btnJoinTeam, "m_txtAuto70000607", UEUI.Text)
    txtJoinTeam.resizeTextMinSize = 3

    EventMgr:Add(EventID.UNION_ID_CHANGE, self.CheckLeagueInfo, self)
end

function UI_TradeWagonsWindow:OnRefresh(type, trainData)
    -- 刷新货车
    if type == 1 then
        self:RefreshWagon(TradeWagonsManager.loadInfo.trade_index_info)
        self:RefreshPanel()

        self:RefreshHeroInfo()
    -- 刷新火车
    elseif type == 2 then
        self.trainData = trainData
        self:RefreshPanel()
        self:RefreshTrain()
    -- 火车出站
    elseif type == 3 then
        self:MoveTrainExit(function ()
            self.hadTrainExit = true
            self:RefreshPanel()
            self:RefreshTrain()
        end)
    -- 刷新界面
    elseif type == 4 then
        self:RefreshPanel()
    -- 火车进站
    elseif type == 5 then
        self:RefreshTrain(true)
    end
end

function UI_TradeWagonsWindow:onDestroy()
    self:SetIsUpdateTick(false)
    EventMgr:Remove(EventID.UNION_ID_CHANGE, self.CheckLeagueInfo, self)
    CurSelectWagon = nil
    self.goTradeTrain = nil
    self.hadTrainExit = false
end

function UI_TradeWagonsWindow:onUIEventClick(go)
    local name = go.name
    -- 帮助按钮
    if name == "m_btnHelp" then
        UI_SHOW(UIDefine.UI_TradeWagonsHelp, 70000517)
    -- 返回按钮
    elseif name == "m_btnBack" then
        self:Close()
    -- 运输记录按钮
    elseif name == "m_btnDepartRecord" then
        UI_SHOW(UIDefine.UI_TradeWagonsRecord, TRADE_WAGONS_RECORD.Depart)
    -- 火车礼包按钮
    elseif name == "m_btnGift" then
        UI_SHOW(UIDefine.UI_FastTrainGift)
    -- 火车进站按钮（临时）
    elseif name == "m_btnTrainEnter" then
        self:MoveTrainEnter()
    -- 火车出站按钮（临时）
    elseif name == "m_btnTrainExit" then
        self:MoveTrainExit()
    -- 召唤火车
    elseif name == "m_btnCallTrain" then
        self:CallTrain()
    -- 加入团队
    elseif name == "m_btnJoinTeam" then
        if not LeagueManager:IsOpenUnion() then
            local str = LangMgr:GetLangFormat(7076, LeagueManager:GetConfigById(2))
            UI_SHOW(UIDefine.UI_WidgetTip, str)
            return
        end
        UI_SHOW(UIDefine.UI_Union, 2)
    end
end

function UI_TradeWagonsWindow:TickUI(deltaTime)
    if self.fitBgHeight ~= self.fitBgRect.rect.height then
        self.fitBgHeight = self.fitBgRect.rect.height
        local factor = self.fitBgHeight / OriginBgHeight
        local newPosY = OriginTrainPosY * factor
        SetUIPos(self.ui.m_goTradeTrain, 5.5, newPosY)
        SetUIPos(self.ui.m_goTradeTrain2, 5.5, newPosY)
    end
    if self.canMoveBg then
        self:StartMoveBg(deltaTime)
    end
    self:RefreshTime()
    self:RefreshTrainTime()
    TradeWagonsManager:TickUI()
end

function UI_TradeWagonsWindow:CheckLeagueInfo()
    local leagueId = LeagueManager:GetMyLeagueId()
    self.isJoinTeam = leagueId and leagueId > 0
    SetActive(self.ui.m_btnCallTrain, self.isJoinTeam)
    SetActive(self.ui.m_btnGift, self.isJoinTeam)
    SetActive(self.ui.m_btnJoinTeam, not self.isJoinTeam)
    self:RefreshTrain()
end

--- 刷新界面
function UI_TradeWagonsWindow:RefreshPanel()
    -- 今日运输次数
    local departCount = TradeWagonsManager.loadInfo.today_trade_times
    local departCountMax = v2n(TradeWagonsManager:GetTradeSettingConfig(11))
    self.ui.m_txtCountToday.text = string.format("%s%s/%s", LangMgr:GetLang(70000461), departCountMax - departCount, departCountMax)

    local config29 = TradeWagonsManager:GetTradeSettingConfig(29)
    if config29 then
        local costTable = string.split(config29, "|")
        local needItemID = v2n(costTable[1])
        local needNum = v2n(costTable[2])
        local curNum = BagManager:GetBagItemCount(needItemID)
        self.ui.m_txtCountStart.text = string.format("%s/%s", curNum, needNum)
    end
end

--region ----------------------------------------- 货车 -----------------------------------------

--- 检查今日运输次数
--- @return boolean hasCount 是否还有剩余次数
function UI_TradeWagonsWindow:CheckDepartCount()
    local departCount = TradeWagonsManager.loadInfo.today_trade_times
    local departCountMax = v2n(TradeWagonsManager:GetTradeSettingConfig(11))
    return departCount < departCountMax
end

--- 获取已解锁的车位
--- @return table unlockPos 已解锁的位置
function UI_TradeWagonsWindow:GetUnlockPos()
    local unlockPos = {}

    -- 默认位置 1 和 2
    local defaultPos = TradeWagonsManager:GetTradeSettingConfig(3)
    local defaultPosTable = string.split(defaultPos, "|")
    for _, value in ipairs(defaultPosTable) do
        unlockPos[value] = true
    end

    -- 等级达到条件，解锁 3
    local pos3 = TradeWagonsManager:GetTradeSettingConfig(4)
    local pos3Table = string.split(pos3, "|")
    local level = NetUpdatePlayerData:GetLevel()
    if level >= v2n(pos3Table[2]) then
        unlockPos[pos3Table[1]] = true
    end

    -- 购买月卡，解锁 4
    local pos4 = TradeWagonsManager:GetTradeSettingConfig(5)
    local pos4Table = string.split(pos4, "|")
    local isUnlockMonthCard = NetMonthCardData:CheckUnlock(v2n(pos4Table[2]))
    if isUnlockMonthCard then
        unlockPos[pos4Table[1]] = true
    end

    return unlockPos
end

--- 刷新货车
--- @param data table 货车位置信息
function UI_TradeWagonsWindow:RefreshWagon(data)
    -- 按照索引排序
    table.sort(data, function (a, b)
        if a.index ~= b.index then
            return a.index < b.index
        end
        return false
    end)

    local unlockPos = self:GetUnlockPos()

    local parent = self.ui.m_transWagon
    for i = 1, 4, 1 do
        local pos = GetChild(parent, "pos" .. i)
        if pos then
            local index = i
            local wagonData = data[i]

            -- 货车节点
            local wagon
            if pos.transform.childCount == 0 then
                wagon = CreateGameObjectWithParent(self.ui.m_goWagonItem, pos.transform)
                SetUIPos(wagon, 0, 0)
            else
                wagon = pos.transform:GetChild(0)
            end
            SetActive(wagon, true)

            if wagonData then
                -- 解锁状态
                local isUnlock = unlockPos[tostring(i)]
                local imgAdd = GetChild(wagon, "add", UEUI.Image)
                local unlockIconIndex = isUnlock and 1 or 2
                SetUIImage(imgAdd, ParkingIcon[unlockIconIndex], false)

                local btnAdd = GetChild(wagon, "add", UEUI.Button)
                btnAdd.onClick:RemoveAllListeners()
                btnAdd.onClick:AddListener(function ()
                    if isUnlock then
                        if self:CheckDepartCount() then
                            TradeWagonsManager:RequestWagonInfoMe(index, function (dataInfo)
                                UI_SHOW(UIDefine.UI_TradeWagonsDepart, index, dataInfo)
                            end)
                        else
                            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000569))
                        end
                    else
                        if index == 3 then
                            local pos3 = TradeWagonsManager:GetTradeSettingConfig(4)
                            local pos3Table = string.split(pos3, "|")
                            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLangFormat(54, pos3Table[2]))
                        elseif index == 4 then
                            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000614))
                        end
                    end
                end)

                -- 发车状态
                local depart = GetChild(wagon, "depart")
                local isReady = wagonData.status == TRADE_WAGONS_STATUS.Ready
                or wagonData.status == TRADE_WAGONS_STATUS.None
                local isDepart = wagonData.status == TRADE_WAGONS_STATUS.Depart
                local isFinish = wagonData.status == TRADE_WAGONS_STATUS.Finish
                SetActive(depart, isDepart or isFinish)
                SetActive(btnAdd, isReady)

                -- 货车图标
                local icon = GetChild(wagon, "depart/icon", UEUI.Image)
                local quality = wagonData.quality
                if quality > 0 then
                    SetUIImage(icon, TradeWagonsManager:GetWagonIcon(quality), true)
                end

                -- 货车 spine
                local spine = GetChild(wagon, "depart/icon/spine", CS.Spine.Unity.SkeletonGraphic)
                if quality > 0 then
                    spine.material = ResMgr:LoadAssetSync(TradeWagonsManager:GetWagonMaterial(quality), AssetDefine.LoadType.Instant)
                    spine.skeletonDataAsset = ResMgr:LoadAssetSync(TradeWagonsManager:GetWagonSpine(quality), AssetDefine.LoadType.Instant)
                    spine:Initialize(true)
                end

                -- 选中特效
                local tradeCarSelectEffect = GetChild(wagon, "depart/icon/TradeCarSelectEffect")
                self.tradeCarSelectEffect[index] = tradeCarSelectEffect

                -- 设置选中特效位置
                local selectEffectPos = TradeWagonsManager:GetWagonsSelectEffectPos(quality)
                SetUIPos(tradeCarSelectEffect, selectEffectPos[1], selectEffectPos[2])

                -- 火焰特效
                local fire1 = GetChild(wagon, "battlt_huochehuo_1")
                local fire2 = GetChild(wagon, "battlt_huochehuo_2")

                -- 货车图标按钮
                local btnIcon = GetChild(wagon, "depart/icon", UEUI.Button)
                btnIcon.onClick:RemoveAllListeners()
                btnIcon.onClick:AddListener(function ()
                    CurSelectWagon = index
                    if CurSelectWagon then
                        self:HideSelectEffect()
                        SetActive(tradeCarSelectEffect, CurSelectWagon == index)
                    else
                        SetActive(tradeCarSelectEffect, false)
                    end

                    TradeWagonsManager:RequestWagonInfoMe(index, function (dataInfo)
                        UI_SHOW(UIDefine.UI_TradeWagonsDetail, TRADE_WAGONS_DETAIL.Me, dataInfo.info)
                    end)
                end)

                -- 奖励按钮
                local btnGift = GetChild(wagon, "depart/gift", UEUI.Button)
                btnGift.onClick:RemoveAllListeners()
                btnGift.onClick:AddListener(function ()
                    -- 先打开货车详情界面
                    TradeWagonsManager:RequestWagonInfoMe(index, function (dataInfo)
                        UI_SHOW(UIDefine.UI_TradeWagonsDetail, TRADE_WAGONS_DETAIL.Me, dataInfo.info)
                        -- 再打开领奖界面
                        TradeWagonsManager:RequestWagonReward(index, function (rewardData)
                            UI_UPDATE(UIDefine.UI_TradeWagonsWindow, 1)
                            UI_UPDATE(UIDefine.UI_TradeWagonsView, 1)
                            UI_UPDATE(UIDefine.UI_ActivityRankCenter, 2)
                            RedPointMgr:Dirty(RedID.TradeWagons)
                            SetActive(btnAdd, true)
                            SetActive(depart, false)
                            local reward = {}
                            for _, value in ipairs(rewardData.rewards) do
                                local item = {
                                    code = value.code,
                                    amount = value.amount
                                }
                                table.insert(reward, item)
                            end
                            UI_SHOW(UIDefine.UI_EquipmentRecharge, reward)
                            SetActive(tradeCarSelectEffect, false)
                            SetActive(fire1, false)
                            SetActive(fire2, false)
                        end)
                        SetActive(fire1, false)
                        SetActive(fire2, false)
                    end)
                end)
                SetActive(btnGift, isFinish)

                local txtTime = GetChild(wagon, "depart/time", UEUI.Text)

                if CurSelectWagon then
                    self:HideSelectEffect()
                    SetActive(tradeCarSelectEffect, CurSelectWagon == index)
                else
                    SetActive(tradeCarSelectEffect, false)
                end

                local attacked_times = 0
                if TradeWagonsManager.wagonsInfoMe[index] then
                    attacked_times = TradeWagonsManager.wagonsInfoMe[index].info.attacked_times
                end

                SetActive(fire1, false)
                SetActive(fire2, false)
                if attacked_times  == 1 then
                    SetActive(fire1, true)
                elseif attacked_times  == 2 then
                    SetActive(fire1, true)
                    SetActive(fire2, true)
                end

                local isShowDepart = depart.gameObject.activeSelf
                if not isShowDepart then
                    SetActive(fire1, false)
                    SetActive(fire2, false)
                end

                self.wagons[i] = {
                    data = wagonData,
                    txtTime = txtTime,
                    btnGift = btnGift
                }
            else
                -- 解锁状态
                local isUnlock = false
                local imgAdd = GetChild(wagon, "add", UEUI.Image)
                local unlockIconIndex = isUnlock and 1 or 2
                SetUIImage(imgAdd, ParkingIcon[unlockIconIndex], false)
                -- 发车状态
                local depart = GetChild(wagon, "depart")
                SetActive(depart, false)
            end
        end
    end
end

--- 刷新英雄信息
function UI_TradeWagonsWindow:RefreshHeroInfo()
    for i = 1, 4, 1 do
        local info = TradeWagonsManager.loadInfo.trade_index_info[i]
        if info then
            if info.is_unlock then
                TradeWagonsManager:RequestWagonInfoMe(i, function (dataInfo)

                end)
            end
        end
    end
end

--- 刷新货车倒计时
function UI_TradeWagonsWindow:RefreshTime()
    for _, wagon in pairs(self.wagons) do
        if wagon.data.end_timestamp then
            local remain = wagon.data.end_timestamp - TimeMgr:GetServerTime()
            if remain > 0 then
                wagon.txtTime.text = TimeMgr:ConverSecondToString(remain)
                SetActive(wagon.txtTime, true)
            else
                wagon.txtTime.text = TimeMgr:ConverSecondToString(0)
                SetActive(wagon.txtTime, false)
                if wagon.data.status == TRADE_WAGONS_STATUS.Depart then
                    wagon.data.status = TRADE_WAGONS_STATUS.Finish
                    SetActive(wagon.btnGift, true)
                end
            end
        end
    end
end

--- 开始移动背景
--- @param deltaTime number 增量时间
function UI_TradeWagonsWindow:StartMoveBg(deltaTime)
    self:MoveBg(self.ui.m_imgBg.transform, bgMoveSpeed, 1, deltaTime)
    self:MoveBg(self.ui.m_imgBg2.transform, bgMoveSpeed, 2, deltaTime)
end

--- 移动背景
--- @param rect any 背景图节点
--- @param speed number 速度
--- @param type number 类型编号
--- @param deltaTime number 增量时间
function UI_TradeWagonsWindow:MoveBg(rect, speed, type, deltaTime)
    local length = 0
    if type == 1 then
        length = -1 * (self.firstMoveX)
    elseif type == 2 then
        length = -1 * (self.secondmoveX)
    end
    local rectX = rect.anchoredPosition.x
    if rectX >= length then
        rect.transform:Translate(Vector3.left * speed * deltaTime)
    else
        self.firstBgRect, self.secondBgRect = self.secondBgRect, self.firstBgRect
        self:ChangeBgPositon(type)
    end
end

--- 改变背景图位置
--- @param type number 类型编号
function UI_TradeWagonsWindow:ChangeBgPositon(type)
    local firstRectX = self.firstBgRect.anchoredPosition.x - bgOffset
    if type == 1 then
        firstRectX = firstRectX + self.secondmoveX
    elseif type == 2 then
        firstRectX = firstRectX + self.firstMoveX
    end

    self.secondBgRect.anchoredPosition = Vector2.New(firstRectX, 0)
end

--- 隐藏选中效果
function UI_TradeWagonsWindow:HideSelectEffect()
    for _, value in pairs(self.tradeCarSelectEffect) do
        SetActive(value, false)
    end
end

--- 打开新手引导
function UI_TradeWagonsWindow:Guide()

    local function Guide5()
        -- body
    end

    -- 最后一步引导回调
    local function GuideCallback4()
        local centerPos = UIRectPosFit(self.ui.m_btnDepartRecord)
        local rt = GetComponent(self.ui.m_btnDepartRecord, typeof(UE.RectTransform))
        local width = rt.rect.width
        local height = rt.rect.height
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetCenter, centerPos)
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetShow,{2, 0, 90})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetBtnSize,{0.5, 0.5})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetDialog,{-0.5, 0, 70000609})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetArrow,{centerPos[1] / 100, centerPos[2] / 100 - 2, 0, 0, 180})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetCallBack,function ()
            UI_CLOSE(UIDefine.UI_GuideMask)
            UI_SHOW(UIDefine.UI_TradeWagonsRecord, TRADE_WAGONS_RECORD.Depart, Guide5)
        end)
    end

    -- 引导积分达标奖励
    local function Guide4()
        local centerPos = UIRectPosFit(self.ui.m_txtCountToday)
        UI_SHOW(UIDefine.UI_GuideMask, {
            {2, 0, 150},             -- 遮罩类型和大小
            centerPos,                 -- 遮罩位置
            {2.9, 0.8},                    -- 遮罩按钮大小
            0.5,                       -- 缩放动画的时长
            function() GuideCallback4() end,   -- 点击回调
            {centerPos[1] / 100, centerPos[2] / 100 - 2.2, 0, 0, 180},   -- 箭头位置
            {-0.5, 0, 70000608},                    -- 对话框位置和内容
            "Sprite/new_hero/headFrame_1.png",   -- 对话框头像
            nil,
        })
    end

    -- 引导点击车位
    local function Guide3()
        UI_CLOSE(UIDefine.UI_GuideMask)
        local index = 1
        TradeWagonsManager:RequestWagonInfoMe(index, function (dataInfo)
            UI_SHOW(UIDefine.UI_TradeWagonsDepart, index, dataInfo, true, Guide4)
        end)
    end

    -- 关闭帮助界面，引导每日主题
    local function Guide2()
        local pos1 = GetChild(self.ui.m_transWagon, "pos1")
        local centerPos = UIRectPosFit(pos1)
        centerPos[2] = centerPos[2] + 20
        UI_SHOW(UIDefine.UI_GuideMask, {
            {2, 0, 150},             -- 遮罩类型和大小
            centerPos,                 -- 遮罩位置
            {1.9, 0.85},                    -- 遮罩按钮大小
            0.5,                       -- 缩放动画的时长
            function() Guide3() end,   -- 点击回调
            {centerPos[1] / 100, centerPos[2] / 100 - 2.2, 0, 0, 180},   -- 箭头位置
            {-0.5, -2, 70000606},                    -- 对话框位置和内容
            "Sprite/new_hero/headFrame_1.png",   -- 对话框头像
            nil,
        })
    end

    -- 第一步引导回调，打开帮助界面
    local function GuideCallback()
        UI_CLOSE(UIDefine.UI_GuideMask)
        -- DOScale(self.ui.m_imgHelp.transform, 1, 0.5)
        UI_SHOW(UIDefine.UI_TradeWagonsHelp, 70000517, Guide2)
    end

    -- 第一步引导帮助按钮
    local centerPos = UIRectPosFit(self.ui.m_btnHelp)
    UI_SHOW(UIDefine.UI_GuideMask, {
        {2, 0, 90},                -- 遮罩类型和大小
        centerPos,                 -- 遮罩位置
        {0.5, 0.5},                    -- 遮罩按钮大小
        0.5,                       -- 缩放动画的时长
        function() GuideCallback() end,   -- 点击回调
        {centerPos[1] / 100, centerPos[2] / 100 - 2, 0, 0, 180},   -- 箭头位置
        {-0.5, 0, 70000604},                    -- 对话框位置和内容
        "Sprite/new_hero/headFrame_1.png",   -- 对话框头像
        nil,
    })
    -- DOScale(self.ui.m_imgHelp.transform, 1.5, 0.5)
end

--endregion -------------------------------------- 货车 -----------------------------------------

--region ----------------------------------------- 火车 -----------------------------------------

--- 初始化火车
function UI_TradeWagonsWindow:InitTrain()
    self.goTradeTrain = GoTradeTrain:Create(self.ui.m_goTradeTrain)

    -- 等待加载完成后再进行初始化
    self.goTradeTrain:WaitForLoad(function(trainInstance)
        trainInstance:SetAnimAuto(true)
        trainInstance:SetEffectPlunder(self.ui.m_goPlunderTrainEffect)
        trainInstance.btnAddHead.onClick:AddListener(function ()
            self:ShowTrainHead()
        end)
        trainInstance.btnAddHeadGold.onClick:AddListener(function ()
            self:ShowTrainHead()
        end)
        trainInstance:SetHeadButton(function ()
            if self.trainData then
                local hasConductor = self.trainData.captain.id ~= 0
                -- 没有列车长
                if not hasConductor then
                    local leagueDetails = LeagueManager:GetMyLeagueDetails()
                    local myPlayerInfo = NetUpdatePlayerData:GetPlayerInfo()

                    -- 会长或者副会长
                    if myPlayerInfo.id == v2n(leagueDetails.own)
                    or myPlayerInfo.id == v2n(leagueDetails.deputy1)
                    or myPlayerInfo.id == v2n(leagueDetails.deputy2)
                    or myPlayerInfo.id == v2n(leagueDetails.deputy3) then
                        UI_SHOW(UIDefine.UI_SelectConductor, self.trainData)
                    else
                        UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000471))
                    end
                    return
                end

                local isDepart = self.trainData.status == TRADE_TRAIN_STATUS.Transporting
                if isDepart then
                    UI_SHOW(UIDefine.UI_TradeWagonsTrainDetail, TRADE_WAGONS_DETAIL.Me, self.trainData)
                else
                    TradeWagonsManager:RequestTrainInfo(self.trainData.record_id, function (data)
                        self.trainData = data.train_info
                        UI_SHOW(UIDefine.UI_TradeWagonsCarriage, self.trainData, 1)
                    end)
                end
            end
        end)

        trainInstance:ShowBtnAdd(false)
        trainInstance:SetBtnAddClickEvent(function ()
            self:ShowTrainDetail()
        end)

        local btnTrain = GetComponent(trainInstance.trans, UEUI.Button)
        btnTrain.onClick:AddListener(function ()
            if self.trainData and self.trainData.captain.id ~= 0 and self.trainData.status == TRADE_TRAIN_STATUS.Transporting then
                UI_SHOW(UIDefine.UI_TradeWagonsTrainDetail, TRADE_WAGONS_DETAIL.Me, self.trainData)
            end
        end)
        local btnTrainGold = GetComponent(trainInstance.transGold, UEUI.Button)
        btnTrainGold.onClick:AddListener(function ()
            if self.trainData and self.trainData.captain.id ~= 0 and self.trainData.status == TRADE_TRAIN_STATUS.Transporting then
                UI_SHOW(UIDefine.UI_TradeWagonsTrainDetail, TRADE_WAGONS_DETAIL.Me, self.trainData)
            end
        end)

        self.train = GetComponent(trainInstance.go, UE.RectTransform)
        self.trainGold = GetComponent(trainInstance.goGold, UE.RectTransform)
        self.trainHead = GetChild(self.ui.m_goTradeTrain, "TradeTrain/border/head")
        self.headNode = CreateCommonHead(self.trainHead, 0.49)
        SetMyHeadAndBorderByGo(self.headNode)

        local smoke = CreateGameObjectWithParent(self.ui.m_goTrainSmoke, trainInstance.trans)
        smoke.name = "m_goTrainSmoke"
        local smokeGold = CreateGameObjectWithParent(self.ui.m_goTrainSmoke, trainInstance.transGold)
        smokeGold.name = "m_goTrainSmoke"

        self.battlt_huoche_start = GetChild(trainInstance.trans, "m_goTrainSmoke/battlt_huoche_start")
        self.battlt_huoche_stop = GetChild(trainInstance.trans, "m_goTrainSmoke/battlt_huoche_stop")

        self.battlt_huoche_startGold = GetChild(trainInstance.transGold, "m_goTrainSmoke/battlt_huoche_start")
        self.battlt_huoche_stopGold = GetChild(trainInstance.transGold, "m_goTrainSmoke/battlt_huoche_stop")

        CollectCardManager:SetMaskSize(self.battlt_huoche_start, self.ui.m_goEffectMask)
        CollectCardManager:SetMaskSize(self.battlt_huoche_stop, self.ui.m_goEffectMask)

        CollectCardManager:SetMaskSize(self.battlt_huoche_startGold, self.ui.m_goEffectMask)
        CollectCardManager:SetMaskSize(self.battlt_huoche_stopGold, self.ui.m_goEffectMask)
    end)
end

--- 刷新火车
function UI_TradeWagonsWindow:RefreshTrain(isNeedAnim)
    if not self.isJoinTeam then
        SetActive(self.ui.m_btnCallTrain, self.isJoinTeam)
        SetActive(self.ui.m_btnGift, self.isJoinTeam)
        SetActive(self.ui.m_btnJoinTeam, not self.isJoinTeam)
        return
    end

    local league_train = TradeWagonsManager.trainLoadInfo.league_train
    -- 运输中的火车
    local transport_train = {}
    -- 准备中的火车
    local ready_train = {}
    if IsTableNotEmpty(league_train) then
        for _, value in ipairs(league_train) do
            if value.status == TRADE_TRAIN_STATUS.Transporting then
                table.insert(transport_train, value)
            elseif value.status == TRADE_TRAIN_STATUS.NotReady
            or value.status == TRADE_TRAIN_STATUS.Ready then
                table.insert(ready_train, value)
            end
        end

        -- 出发时间早的排上面
        table.sort(transport_train, function (a, b)
            if a.depart_timestamp ~= b.depart_timestamp then
                return a.depart_timestamp < b.depart_timestamp
            end
            return false
        end)

        -- 有三辆火车
        if #transport_train > 2 then
            local temp = deepcopy(transport_train)
            transport_train = {}
            for _, value in ipairs(temp) do
                if #transport_train == 2 then
                    table.insert(ready_train, value)
                    break
                end
                table.insert(transport_train, value)
            end
        end

        self.transport_train = transport_train
        self.ready_train = ready_train

        self:RefreshTrainHistory(transport_train)

        self.trainData = nil
        if IsTableNotEmpty(ready_train) then
            self.trainData = ready_train[1]
        end

        if IsTableEmpty(ready_train) then
            SetActive(self.ui.m_btnCallTrain, true)
            SetActive(self.ui.m_goTradeTrain, false)
        else
            SetActive(self.ui.m_btnCallTrain, false)
            SetActive(self.ui.m_goTradeTrain, true)
        end

        if not self.trainData then return end

        local quality = {}
        for _, value in pairs(self.trainData.train_quality) do
            if value.part > 0 then
                quality[value.part] = value.quality
            end
        end

        -- 安全调用 GoTradeTrain 的方法
        if self.goTradeTrain then
            self.goTradeTrain:WaitForLoad(function(trainInstance)
                trainInstance:SetBodyIconByQualityList(quality)
                trainInstance:CheckGold(self.trainData)
                trainInstance:ShowEffectPlunder(self.trainData)

                SetActive(trainInstance.btnAddHead, false)
                SetActive(trainInstance.btnAddHeadGold, false)
                trainInstance:ShowBtnAdd(false)

                -- 未指定列车长
                if self.trainData.status == TRADE_TRAIN_STATUS.NotReady then
                    if self.trainData.captain.id == 0 then
                        SetActive(trainInstance.btnAddHead, true)
                        SetActive(trainInstance.btnAddHeadGold, true)
                        trainInstance:ShowBtnAdd(false)
                    end
                -- 已指定列车长
                elseif self.trainData.status == TRADE_TRAIN_STATUS.Ready then
                    SetActive(trainInstance.btnAddHead, false)
                    SetActive(trainInstance.btnAddHeadGold, false)
                    trainInstance:ShowBtnAdd(true)

                    local myPlayerInfo = NetUpdatePlayerData:GetPlayerInfo()
                    if self.trainData.captain.id == myPlayerInfo.id then
                        local myCaptain = {
                            name = myPlayerInfo.name,
                            icon = myPlayerInfo.head,
                            border = myPlayerInfo.headBorder
                        }
                        trainInstance:SetConductorInfo(myCaptain)
                    else
                        trainInstance:SetConductorInfo(self.trainData.captain)
                    end
                -- 运输中
                elseif self.trainData.status == TRADE_TRAIN_STATUS.Transporting then
                    SetActive(trainInstance.btnAddHead, false)
                    SetActive(trainInstance.btnAddHeadGold, false)
                    trainInstance:ShowBtnAdd(false)
                    local myPlayerInfo = NetUpdatePlayerData:GetPlayerInfo()
                    if self.trainData.captain.id == myPlayerInfo.id then
                        local myCaptain = {
                            name = myPlayerInfo.name,
                            icon = myPlayerInfo.head,
                            border = myPlayerInfo.headBorder
                        }
                        trainInstance:SetConductorInfo(myCaptain)
                    else
                        trainInstance:SetConductorInfo(self.trainData.captain)
                    end

                    if self.hadTrainExit then
                        self.hadTrainExit = false
                        -- 停留在轨道上的火车，3 秒后开回来
                        TimeMgr:CreateTimer(UIDefine.UI_TradeWagonsWindow, function()
                            if not self.ui then return end
                            self:MoveTrainEnter()
                        end, 3, 1)
                    end
                end

                local isShowBorder = self.trainData.captain.id ~= 0
                trainInstance:ShowBorder(isShowBorder)
            end)
        end

        if isNeedAnim then
            self:MoveTrainEnter()
        else
            if UIMgr:GetOnly("TradeTrainAnim", true) then
                SetActive(self.ui.m_goTradeTrain, true)
            else
                UIMgr:SetOnly("TradeTrainAnim", true)
                self:MoveTrainEnter()
            end
        end
    end

    if IsTableEmpty(ready_train) then
        SetActive(self.ui.m_btnCallTrain, true)
        SetActive(self.ui.m_goTradeTrain, false)
    else
        SetActive(self.ui.m_btnCallTrain, false)
        SetActive(self.ui.m_goTradeTrain, true)
    end
end

--- 刷新历史运输的火车
--- @param transport_train table 运输中的火车
function UI_TradeWagonsWindow:RefreshTrainHistory(transport_train)
    local trainItem = self.ui.m_goTrainItem
    local content = self.ui.m_goTrainHistory
    local trainItemCount = content.transform.childCount
    -- 先全部隐藏
    for i = 1, trainItemCount, 1 do
        local item = content.transform:GetChild(i - 1)
        SetActive(item, false)
    end

    for key, value in ipairs(transport_train) do
        local item
        -- 有可用的 item 直接获取
        if key <= trainItemCount then
            item = content.transform:GetChild(key - 1)
        -- item 不够用，创建新的
        else
            item = CreateGameObjectWithParent(trainItem, content)
        end

        local goldCount = 0
        for _, v in ipairs(value.train_quality) do
            if v.quality == SLG_QUALITY.UR then
                goldCount = goldCount + 1
            end
        end

        local trainIconIndex = 1
        if goldCount >= 5 then
            trainIconIndex = 2
        end

        -- 底框
        local border = GetComponent(item, UEUI.Image)
        local borderIconPath = TrainIcon[trainIconIndex]
        SetUIImage(border, borderIconPath, false)

        local txtName = GetChild(item, "border/name", UEUI.Text)
        local head = GetChild(item, "border/head")

        local headItem
        -- 有可用的 item 直接获取
        if head.transform.childCount > 0 then
            headItem = head.transform:GetChild(0)
        -- item 不够用，创建新的
        else
            headItem = CreateCommonHead(head, 0.38)
        end

        local myPlayerInfo = NetUpdatePlayerData:GetPlayerInfo()
        if myPlayerInfo.id == value.captain.id then
            txtName.text = myPlayerInfo.name
            SetHeadAndBorderByGo(headItem, myPlayerInfo.head, myPlayerInfo.headBorder)
        else
            txtName.text = value.captain.name
            SetHeadAndBorderByGo(headItem, value.captain.icon, value.captain.border)
        end

        -- 按钮
        local button = GetComponent(item, UEUI.Button)
        button.onClick:RemoveAllListeners()
        button.onClick:AddListener(function ()
            UI_SHOW(UIDefine.UI_TradeWagonsTrainDetail, TRADE_WAGONS_DETAIL.Me, value)
        end)

        local txtTime = GetChild(item, "time", UEUI.Text)

        SetActive(item, true)

        self.trains[value.record_id] = {
            txtTime = txtTime,
            data = value
        }
    end
end

--- 火车进入动画
function UI_TradeWagonsWindow:MoveTrainEnter()

    if self.isTrainMove then
        return
    end

    self.isTrainMove = true

    -- 安全调用 GoTradeTrain 的方法
    if self.goTradeTrain then
        self.goTradeTrain:WaitForLoad(function(trainInstance)
            local isGold = trainInstance:CheckGold(self.trainData)
            local train
            local smoke

            if isGold then
                train = self.trainGold
                smoke = self.battlt_huoche_stopGold
            else
                train = self.train
                smoke = self.battlt_huoche_stop
            end

            DOKill(train)

            local canvasRect = UIMgr:GetCanvasRectTrans()
            local canvasWidth = canvasRect.rect.width
            local trainWidth = train.rect.width

            local initPosX = -(canvasWidth + trainWidth) / 2

            initPosX = -1540

            SetUIPos(train, initPosX, 0)

            SetActive(self.ui.m_goTradeTrain, true)

            TimeMgr:CreateTimer("MoveTrainEnter", function()
                SetActive(smoke, true)
            end, 1, 1)

            DOLocalMoveX(train, 0, 3, function()
                TimeMgr:CreateTimer("MoveTrainEnter", function()
                    SetActive(smoke, false)
                    self.isTrainMove = false
                end, 2, 1)
            end, Ease.InOutQuad)
        end)
    else
        self.isTrainMove = false
    end
end

--- 火车退出动画
function UI_TradeWagonsWindow:MoveTrainExit(callback)

    if self.isTrainMove then
        return
    end

    self.isTrainMove = true

    -- 安全调用 GoTradeTrain 的方法
    if self.goTradeTrain then
        self.goTradeTrain:WaitForLoad(function(trainInstance)
            local isGold = trainInstance:CheckGold(self.trainData)
            local train
            local smoke

            if isGold then
                train = self.trainGold
                smoke = self.battlt_huoche_startGold
            else
                train = self.train
                smoke = self.battlt_huoche_start
            end

            DOKill(train)

            local canvasRect = UIMgr:GetCanvasRectTrans()
            local canvasWidth = canvasRect.rect.width
            local trainWidth = train.rect.width

            local endPosX = (canvasWidth + trainWidth) / 2

            endPosX = 1662

            SetActive(smoke, true)
            DOLocalMoveX(train, endPosX, 3, function()
                TimeMgr:CreateTimer("MoveTrainEnter", function()
                    SetActive(smoke, false)
                    SetActive(self.ui.m_goTradeTrain, false)
                    self.isTrainMove = false
                    if callback then
                        callback()
                    end
                end, 1, 1)
            end, Ease.InQuad)
        end)
    else
        self.isTrainMove = false
        if callback then
            callback()
        end
    end
end

--- 显示车头信息
function UI_TradeWagonsWindow:ShowTrainHead()
    if not self.trainData then return end
    local hasConductor = self.trainData.captain.id ~= 0
    -- 没有列车长
    if not hasConductor then
        local leagueDetails = LeagueManager:GetMyLeagueDetails()
        local myPlayerInfo = NetUpdatePlayerData:GetPlayerInfo()

        -- 会长或者副会长
        if myPlayerInfo.id == v2n(leagueDetails.own)
        or myPlayerInfo.id == v2n(leagueDetails.deputy1)
        or myPlayerInfo.id == v2n(leagueDetails.deputy2)
        or myPlayerInfo.id == v2n(leagueDetails.deputy3) then
            UI_SHOW(UIDefine.UI_SelectConductor, self.trainData)
        else
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000471))
        end
        return
    end

    local isDepart = self.trainData.status == TRADE_TRAIN_STATUS.Transporting
    if isDepart then
        UI_SHOW(UIDefine.UI_TradeWagonsTrainDetail, TRADE_WAGONS_DETAIL.Me, self.trainData)
    else
        TradeWagonsManager:RequestTrainInfo(self.trainData.record_id, function (data)
            self.trainData = data.train_info
            UI_SHOW(UIDefine.UI_TradeWagonsCarriage, self.trainData, 1)
        end)
    end
end

--- 显示车厢信息
function UI_TradeWagonsWindow:ShowTrainDetail()
    if not self.trainData then return end
    local isDepart = self.trainData.status == TRADE_TRAIN_STATUS.Transporting
    if isDepart then
        UI_SHOW(UIDefine.UI_TradeWagonsTrainDetail, TRADE_WAGONS_DETAIL.Me, self.trainData)
    else
        TradeWagonsManager:RequestTrainInfo(self.trainData.record_id, function (data)
            self.trainData = data.train_info
            UI_SHOW(UIDefine.UI_TradeWagonsCarriage, self.trainData, 0)
        end)
    end
end

--- 是否列车长
--- @return boolean isConductor 是否列车长
function UI_TradeWagonsWindow:IsConductor()
    if not self.trainData then return false end
    local playerInfo = NetUpdatePlayerData:GetPlayerInfo()
    return self.trainData.captain.id == playerInfo.id
end

--- 召唤火车
function UI_TradeWagonsWindow:CallTrain()
    local memberList = LeagueManager:GetMyLeagueDetails().memberList
    local memberCount = #memberList
    local memberNeedCount = v2n(TradeWagonsManager:GetTradeSettingConfig(22))
    if memberCount < memberNeedCount then
        UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLangFormat(70001113, memberNeedCount))
        return
    end

    local needJoinTime = v2n(TradeWagonsManager:GetTradeSettingConfig(37))
    local myPlayerInfo = NetUpdatePlayerData:GetPlayerInfo()
    local myJoinTime = 0

    for _, value in ipairs(memberList) do
        if v2n(value.id) == myPlayerInfo.id then
            myJoinTime = value.leagueJoinTime
        end
    end

    local joinDuration = TimeMgr:GetServerTime() - myJoinTime
    if joinDuration < needJoinTime then
        UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(1471))
        return
    end

    local config29 = TradeWagonsManager:GetTradeSettingConfig(29)
    if config29 then
        local costTable = string.split(config29, "|")
        local needItemID = v2n(costTable[1])
        local needNum = v2n(costTable[2])
        local curNum = BagManager:GetBagItemCount(needItemID)

        -- 召唤次数上限
        local dayLimit = v2n(TradeWagonsManager:GetTradeSettingConfig(30))
        local train_call_times = TradeWagonsManager.trainLoadInfo.train_call_times
        local league_depart_times = TradeWagonsManager.trainLoadInfo.league_depart_times
        local callTime = math.max(train_call_times, league_depart_times)
        if callTime >= dayLimit then
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000569))
            return
        end

        -- 道具数量足够
        if curNum >= needNum then
            TradeWagonsManager:RequestCallTrain(function (data)
                self:RefreshTrain(true)
            end)
        -- 道具数量不足
        else
            UI_SHOW(UIDefine.UI_FastTrainGift)
        end
    end
end

--- 刷新火车倒计时
function UI_TradeWagonsWindow:RefreshTrainTime()
    for _, train in pairs(self.trains) do
        if train.data.end_timestamp then
            local remain = train.data.end_timestamp - TimeMgr:GetServerTime()
            if remain > 0 then
                train.txtTime.text = TimeMgr:ConverSecondToString(remain)
            else
                train.txtTime.text = LangMgr:GetLang(70000505)
                if train.data.status == TRADE_TRAIN_STATUS.Transporting then
                    train.data.status = TRADE_TRAIN_STATUS.FinishedNotGiveReward

                    local remain_train = {}
                    for _, value in ipairs(self.transport_train) do
                        if value.status == TRADE_TRAIN_STATUS.Transporting then
                            table.insert(remain_train, value)
                        end
                    end
                    -- 出发时间早的排上面
                    table.sort(remain_train, function (a, b)
                        if a.depart_timestamp ~= b.depart_timestamp then
                            return a.depart_timestamp < b.depart_timestamp
                        end
                        return false
                    end)
                    self.transport_train = remain_train
                    self:RefreshTrainHistory(remain_train)
                end
            end
        end
    end

    if self.trainData then
        local departTime = self.trainData.depart_timestamp or 0
        local remain = departTime - TimeMgr:GetServerTime()
        if remain >= 0 then
            local needTime = v2n(TradeWagonsManager:GetTradeSettingConfig(31))
            if remain > needTime then
                SetActive(self.ui.m_txtTrainCountDown, false)
            else
                self.ui.m_txtTrainCountDown.text = self.readyLangStr .. TimeMgr:ConverSecondToString(remain)
                SetActive(self.ui.m_txtTrainCountDown, true)
            end
        else
            self.ui.m_txtTrainCountDown.text = self.readyLangStr .. TimeMgr:ConverSecondToString(0)
            SetActive(self.ui.m_txtTrainCountDown, false)
        end
    else
        SetActive(self.ui.m_txtTrainCountDown, false)
    end
end

--endregion -------------------------------------- 火车 -----------------------------------------

return UI_TradeWagonsWindow