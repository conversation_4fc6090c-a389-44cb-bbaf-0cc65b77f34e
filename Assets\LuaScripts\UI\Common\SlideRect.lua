-- 重复利用item的滑动列表
-- 1.支持水平滑动，垂直滑动 (未验证水平垂直的滑动)
-- 2.支持自动移动到最近的item (自动吸附)
-- 3.支持移动到指定index的item (MoveToIndex)
-- 4.支持移动到上一个或下一个item (MoveNext)
-- 5.支持注册Drag事件 (RegisterBeginDragEvent,RegisterOnDragEvent,RegisterEndDragEvent)
local SlideRect = Class()

function SlideRect:Init(scroll,slideType,syncCount,dynamicSetSize,getSize)
    self.scroll = scroll
    self.contentRect = scroll.content
	
    if slideType == 1 then 
        self.horizontalEnable = true
        self.viewportWidth = scroll.viewport.rect.width
    elseif slideType == 2 then
        self.verticalEnable = true
        self.viewportHeight = scroll.viewport.rect.height
    elseif slideType ==3 then
        self.horizontalEnable = true
        self.verticalEnable = true
        self.viewportWidth = scroll.viewport.rect.width
        self.viewportHeight = scroll.viewport.rect.height
    end
    self.dynamicItemSize = dynamicSetSize or false
    self.onGetSize = getSize
    self.isFirstDrag = true
    self.scrollType = slideType
    self.scrollSyncCount = syncCount or 1
	
	self.uiDrag = scroll:GetComponent(typeof(CS.UIDrag))
	self:RegisterUIDrag_Private()

end

function SlideRect:RefreshViewPort(scroll,slideType)
    if slideType == 1 then
        self.horizontalEnable = true
        self.viewportWidth = scroll.viewport.rect.width
    elseif slideType == 2 then
        self.verticalEnable = true
        self.viewportHeight = scroll.viewport.rect.height
    elseif slideType ==3 then
        self.horizontalEnable = true
        self.verticalEnable = true
        self.viewportWidth = scroll.viewport.rect.width
        self.viewportHeight = scroll.viewport.rect.height
    end
end

function SlideRect:RegisterUIDrag_Private()
	--scrollview组件下添加有UIDrag,设置回调
	if self.uiDrag then
		self.OnBeginDrag = {}
		self.OnDrag = {}
		self.OnEndDrag = {}
		
		self.uiDrag.m_BeginDrag = function(data,go,x,y)
			for i, v in ipairs(self.OnBeginDrag) do
				if v then v(data,go,x,y) end
			end
		end
		self.uiDrag.m_OnDrag = function(data,go,x,y)
			for i, v in ipairs(self.OnDrag) do
				if v then v(data,go,x,y) end
			end
		end
		self.uiDrag.m_EndDrag = function(data,go,x,y)
			for i, v in ipairs(self.OnEndDrag) do
				if v then v(data,go,x,y) end
			end
		end
	end
end

function SlideRect:GetContentWidth()
	return self.contentWidth
end

function SlideRect:GetContentHeight()
	return self.contentHeight
end
function SlideRect:SetItems(items,itemOffset,posOffset)
    self.itemOffset = itemOffset
    self.itemCount = #items
    self.items = items
    local value = 0
    if self.scrollType==1 then
        value = items[1].width
    elseif self.scrollType==2 then
        value = items[1].height
    end
    self.cellSize = value
    self.exchangeDistance = value+itemOffset
    self.posOffset = posOffset or Vector2.New(0,0)
    self.contentWidth = self.contentRect.rect.width
    self.contentHeight = self.contentRect.rect.height
    for i=1,self.itemCount do
        items[i]:SetParent(self.contentRect)
    end
    self:SetScrollCallback()
end

function SlideRect:GetDatas()
	return self.datas
end

---@param initIndex int @第一个数据的下标
---@param isGrid bool @是否自动吸附到最近的item（不使用附带的Button功能可以将 interactable 设置 false）
---@param minMoveVelocity float @当scrollview移动速度小于这个值时，执行吸附到最近item (default = 100)
---@param onMoveCompleted action @ 吸附到最近item后的回调
---@param moveTime  float @ 吸附移动所需要的时间 (default = 0.3)
function SlideRect:SetData(datas,initIndex,isGrid,onMoveCompleted,minMoveVelocity,moveTime)
	
    self.datas = datas
	if not initIndex then
		initIndex = 1
	end
    
	local showItemCount = 0

	if self.scrollType == 1 then
		showItemCount = self.viewportWidth // (self.itemOffset + self.cellSize)
	else showItemCount = self.viewportHeight // (self.itemOffset + self.cellSize)
	end

    if initIndex > #datas - (showItemCount + 1) then
        initIndex = math.floor((#datas - (showItemCount + 1)))
    end

	self.movedIndex = Mathf.Max(initIndex - 1,1)
	self.currentIndex = self.movedIndex--只有初始化的时候是一致的
    self.isFirstDrag = true
    local dLen = #datas
    local index = self.movedIndex
    for i=1,self.itemCount do
        if i~=1 and (i-1)%self.scrollSyncCount == 0 then
            index = index+1
        end
		--这里要按照顺序初始化Item,获取时Item的顺序才对
        local itm = self.items[(i + self.movedIndex - 2) % self.itemCount + 1]
        local tmp = 0
        local vec = Vector2.New(0,0)
        if datas[index] then
            itm:SetVisable(true)
            itm:SetItemState(datas[index],index)
            if self.dynamicItemSize then
                itm:SetItemSize()
            end
        else
            itm:SetVisable(false)
        end
        if self.scrollType==1 then
            tmp = itm.width/2+(index-1)*(self.itemOffset+itm.width)
            vec.x = tmp+self.posOffset.x
            vec.y = self.posOffset.y
        else
            if self.dynamicItemSize then
                local lastItm = self.items[i-1]
                if lastItm then
                    tmp =self.itemOffset + lastItm.height/2+itm.height/2 -lastItm:GetPosition().y
                else
                    tmp = itm.height/2+self.posOffset.y
                end
            else
                tmp = itm.height/2+(index-1)*(self.itemOffset+itm.height) + self.posOffset.y
            end
            vec.x = (itm.width+self.itemOffset)*((i-1)%self.scrollSyncCount) + self.posOffset.x-self.contentWidth/2+itm.width/2
            vec.y = -tmp
        end
        itm:SetPosition(vec)
		
    end
	
    
    local count = math.ceil(dLen/self.scrollSyncCount)
	---size content的大小
    local size = self.posOffset.x
    if self.scrollType==2 then
        size = self.posOffset.y
    end

    if self.dynamicItemSize then
        for i=1,count do
            size= size+self.onGetSize(datas[i])+self.itemOffset
        end
    else
        size = size+(self.itemOffset+self.cellSize)*count 
    end
    size =  size -self.itemOffset
    if self.scrollType==1 then

        if size<self.viewportWidth then
            size = self.viewportWidth
        end
        self.contentWidth = size
        self.offsetContentMinusViewport = size-self.viewportWidth
    elseif self.scrollType==2  then
        if size<self.viewportHeight then
            size = self.viewportHeight
            -- isScrollEnable = false
        end
        self.contentHeight = size
        self.offsetContentMinusViewport = size-self.viewportHeight
    end
   
    local edge = 0
    if self.scrollType==2 then
        edge=2
    end
    self.contentRect:SetInsetAndSizeFromParentEdge(edge,0,size)
    if self.itemCount>= dLen then
        self.exchangeItem = false
    else
        self.exchangeItem = true
    end
	
	self.oneCellPosition = (self.itemOffset + self.cellSize)/(self.offsetContentMinusViewport)
	local movePosition = self:GetPositionByIndex(initIndex)
	
	if self.scrollType==1 then
		
		self.scroll.horizontalNormalizedPosition = movePosition
	else
		self.scroll.verticalNormalizedPosition = 1 - movePosition
	end
	if isGrid then
		self:CheckUIDrag()
	end
	if isGrid and self.uiDrag then
		
		--自动移动到最近的格子的func
		local AutoMoveFunc = function ()
			local currentPosition = self:GetCurrentPostion()
			--遍历找最近的格子
			local targetIndex = self:GetNearIndex()
			if targetIndex ~= -1 then
				local targetPosition = self:GetPositionByIndex(targetIndex)
				self.currentIndex =	targetIndex
				self:MoveToPosition(currentPosition,targetPosition,moveTime,onMoveCompleted)
			end
		end
		
		self:RegisterBeginDragEvent(function ()
			self.isEndDrag = false
		end)
		local velocity = 0
		minMoveVelocity = minMoveVelocity or 100
		--注册OnScroll 松手滑动的过程中检测速度小于某值后，执行AutoMoveFunc
		self.scroll.onValueChanged:AddListener(function ()
			if self.isEndDrag then
				velocity = self:GetCurrentVelocity()
				if Mathf.Abs(velocity) < minMoveVelocity then
					self.isEndDrag = false
					AutoMoveFunc()
				end
			end
		end)
		
		self:RegisterEndDragEvent(function ()
				--如果速度比较小松手直接执行
				velocity = self:GetCurrentVelocity()
				if Mathf.Abs(velocity) < minMoveVelocity then
					AutoMoveFunc()
				else--否则在OnScroll监听速度,较小后再执行 
					self.isEndDrag = true
				end
		end)
	end
end


function SlideRect:SetScrollCallback()
    local function SetItemState(isPositive,isHorizontal,itemGroupIndex)
        local disCount = 0
        local dataIndex = 0
        local movedOff = 0
        if isPositive then
            disCount = self.movedIndex+self.itemCount/self.scrollSyncCount
            dataIndex =(self.movedIndex-1)*self.scrollSyncCount+self.itemCount
            movedOff = 1
        else
            disCount = self.movedIndex
            dataIndex = (self.movedIndex-1)*self.scrollSyncCount
            movedOff = -1
        end
        local val = (self.cellSize+self.itemOffset)*disCount-self.itemOffset-self.cellSize/2 + self.posOffset.x
        for i=1,self.scrollSyncCount do
            local itmIndex = (itemGroupIndex-1)*self.scrollSyncCount+i
            local pos = self.items[itmIndex]:GetPosition()
            
            if self.dynamicItemSize and not isPositive then
                itmIndex = itemGroupIndex-1
                if itmIndex==0 then
                    itmIndex = 3
                end
                dataIndex = dataIndex-1
            end
            local itm = self.items[itmIndex]
            local data = self.datas[dataIndex+i]
            if data then
                itm:SetVisable(true)
                itm:SetItemState(data,dataIndex+i)
                if self.dynamicItemSize then
                    itm:SetItemSize()
                end
            else
                itm:SetVisable(false)
            end
            if isHorizontal then
                pos.x = val
            else
                if self.dynamicItemSize then
                    if isPositive then
                        local lastIndex = (itemGroupIndex+self.itemCount-2)%self.itemCount+1
                        local lastItm = self.items[lastIndex]
                        pos.y = lastItm:GetPosition().y-lastItm.height/2 -self.itemOffset-itm.height/2
                    else
                        local curItm = self.items[itemGroupIndex]
                        local y = curItm:GetPosition().y
                        pos.y = y+curItm.height/2 +self.itemOffset +itm.height/2
                    end
                else
                    pos.y = -val-self.posOffset.y
                end
            end
            itm:SetPosition(pos)
        end
        self.movedIndex = self.movedIndex+movedOff
    end
    
    local function IsChangePos(positive,isHorizontal,movedOffset,distance,index)
        if positive then
            if movedOffset> distance then
                SetItemState(true,isHorizontal,index)
            end
        else
            if movedOffset< distance then
                SetItemState(false,isHorizontal,index)
            end
        end
    end
    
    local function Slide(isPositive,value,isHorizontal)
        --if value <0 or value>1 then
            --return
        --end
        
        local movedOffset = 0
		--moveIndex 显示的当前index
		--itemCount 实例化的Item
		--itmIndex 分组里的Index
        local itmIndex = self.movedIndex%(self.itemCount/self.scrollSyncCount)
        if itmIndex==0 then
            itmIndex = self.itemCount/self.scrollSyncCount
        end 
		local targetDistance = 0
		if self.scrollType==1 then
			targetDistance = self.exchangeDistance*self.movedIndex + self.posOffset.x
		else targetDistance = self.exchangeDistance*self.movedIndex + self.posOffset.y
		end
        if isHorizontal then
            movedOffset = value*self.offsetContentMinusViewport
        else
            movedOffset = (1-value)*self.offsetContentMinusViewport
            if self.dynamicItemSize then
                local itm = self.items[itmIndex]
                local y = -itm:GetPosition().y
                if isPositive then
                    targetDistance = itm.height/2+self.itemOffset+y
                else
                    targetDistance = -itm.height/2+y
                end
            end
        end
        IsChangePos(isPositive,isHorizontal,movedOffset,targetDistance,itmIndex)
    end
    
    local function onScroll(vec2)
        if not self.exchangeItem then
            return
        end
        if self.isFirstDrag then
            self.lastVecPos =vec2
            self.isFirstDrag = false
            return
        end
        local offset = vec2-self.lastVecPos
        self.lastVecPos =vec2
        
        if self.horizontalEnable then
            Slide(offset.x>0,vec2.x,true)
        end

        if self.verticalEnable then
            Slide(offset.y<0,vec2.y,false)
        end
    end
    self.scroll.onValueChanged:AddListener(onScroll)
end
------自动移动------

function SlideRect:MovePrevious(moveTime,OnComplete)
	--local targetIndex = self:GetNearIndex()
	self:MoveToIndex(self.currentIndex - 1,moveTime,OnComplete)
end
function SlideRect:MoveNext(moveTime,OnComplete)
	--local targetIndex = self:GetNearIndex()
	self:MoveToIndex(self.currentIndex + 1,moveTime,OnComplete)
end
function SlideRect:MoveToIndex(index,moveTime,OnComplete)
	if index < 1 or index > #self.datas then return end
	if self.currentIndex == index then return end 
	
	local targetPosition = self:GetPositionByIndex(index)
	self:MoveToPosition(self:GetCurrentPostion(),targetPosition,moveTime,OnComplete)
	self.currentIndex =	index
end
function SlideRect:MoveToPosition(currentPosition,targetPosition,moveTime,OnComplete)
	Tween.Kill("AutoMoveFunc")
	AddDOTweenNumberComplete(currentPosition,targetPosition,moveTime or 0.3,function (value)
			if self.scrollType == 1 then
				self.scroll.horizontalNormalizedPosition = value
			else
				self.scroll.verticalNormalizedPosition = 1 - value
			end
		end,function ()
			if OnComplete then
				OnComplete()
			end
		end):SetId("AutoMoveFunc")

end
------GetSet函数------
--遍历查找最近的格子位置
--todo:换一个快速的查找算法
function SlideRect:GetNearIndex()
	local dLen = #self.datas
	for i = 1, dLen do
		--四舍五入 减半个 self.oneCellPosition
		local currentPosition = self:GetCurrentPostion()
		if currentPosition < (i - 1) * self.oneCellPosition  - (self.oneCellPosition / 2)then
			local targetIndex = Mathf.Clamp(i - 1,1,dLen)
			return targetIndex
		end
	end
	return dLen
end
function SlideRect:GetPositionByIndex(index)
	return Mathf.Clamp01((index - 1) * self.oneCellPosition)
end
function SlideRect:GetCurrentPostion()
	if self.scrollType==1 then
		return self.scroll.horizontalNormalizedPosition
	else
		return 1 - self.scroll.verticalNormalizedPosition
	end
end
function SlideRect:GetCurrentVelocity()
	if self.scrollType == 1 then
		return self.scroll.velocity.x
	else
		return self.scroll.velocity.y
	end
end
function SlideRect:SetPos(pos)
	if self.scrollType==1 then
		self.scroll.horizontalNormalizedPosition = pos
	else
		self.scroll.verticalNormalizedPosition = pos
	end
end

function SlideRect:GetPos()
	if self.scrollType==1 then
		return self.scroll.horizontalNormalizedPosition
	else
		return self.scroll.verticalNormalizedPosition
	end
end
----UIDrag组件回调
function SlideRect:CheckUIDrag()
	if not self.uiDrag then
		self.uiDrag = self.scroll.gameObject:AddComponent(typeof(CS.UIDrag))
		self:RegisterUIDrag_Private()
		self.uiDrag.interactable = false
	end
end
function SlideRect:RegisterBeginDragEvent(action)
	if not action then return -1 end
	self:CheckUIDrag()
	table.insert(self.OnBeginDrag,action)
	return table.count(self.OnBeginDrag)
end
function SlideRect:RemoveBeginDragEventById(id)
	if not self.OnBeginDrag then return end
	if type(id) == "number" then
		table.remove(self.OnBeginDrag,id)
	end

end
function SlideRect:RemoveBeginDragEvent(action)
	if not self.OnBeginDrag or not action then return end
	for i, v in ipairs(self.OnBeginDrag) do
		if v == action then
			table.remove(self.OnBeginDrag,i)
			break
		end
	end
end
function SlideRect:RegisterOnDragEvent(action)
	if not action then return -1 end
	self:CheckUIDrag()
	table.insert(self.OnDrag,action)
	return table.count(self.OnDrag)
end
function SlideRect:RemoveOnDragEventById(id)
	if not self.OnDrag then return end
	if type(id) == "number" then
		table.remove(self.OnDrag,id)
	end

end
function SlideRect:RemoveOnDragEvent(action)
	if not self.OnDrag or not action then return end
	for i, v in ipairs(self.OnDrag) do
		if v == action then
			table.remove(self.OnDrag,i)
			break
		end
	end
end
function SlideRect:RegisterEndDragEvent(action)
	if not action then return -1 end
	self:CheckUIDrag()
	table.insert(self.OnEndDrag,action)
	return table.count(self.OnEndDrag)
end
function SlideRect:RemoveEndDragEventById(id)
	if not self.OnEndDrag then return end
	if type(id) == "number" then
		table.remove(self.OnEndDrag,id)
	end

end
function SlideRect:RemoveEndDragEvent(action)
	if not self.OnEndDrag or not action then return end
	for i, v in ipairs(self.OnEndDrag) do
		if v == action then
			table.remove(self.OnEndDrag,i)
			break
		end
	end
end
return SlideRect