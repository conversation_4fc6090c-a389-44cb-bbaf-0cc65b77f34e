local UI_TreasyreHuntTip = Class(BaseView)

function UI_TreasyreHuntTip:OnInit()
    self.activeId = nil
end

function UI_TreasyreHuntTip:OnCreate(activeId)
	self.activeId = activeId
	
	self:UpDateUI()	
end

function UI_TreasyreHuntTip:UpDateUI()
	local effectBg = GET_UI(self.uiGameObject, "m_Effect", "RectTransform")
	--EffectConfig:CreateEffect(72, 0, 0, 0, effectBg.transform, function(data, tGo, go)
		--self:SortOrderAllCom(true)
	--end)

	local activityItem =  LimitActivityController:GetActiveMessage(self.activeId)
	if activityItem == nil then
		return
	end
	local form = activityItem:GetForm()

	local img0 = GET_UI(self.uiGameObject, "imgActive0", TP(UEUI.Image))
	local img1 = GET_UI(self.uiGameObject, "imgActive1", TP(UEUI.Image))
	local img2 = GET_UI(self.uiGameObject, "imgActive2", TP(UEUI.Image))
	self.ui.m_txtDesc.text = LangMgr:GetLang(form.activeMess.activity_describe)
	SetImageSprite(img0, form.activeImg1, true)
	SetImageSprite(img1, form.activeImg2, true)
	SetImageSprite(img2, form.activeImg3, true)

    local bg = GetChild(self.uiGameObject, "bg")
    self:ShowSequenceAnim(bg)
end

function UI_TreasyreHuntTip:OnRefresh(param)

end

function UI_TreasyreHuntTip:onDestroy()
	self.activeId = nil
end

function UI_TreasyreHuntTip:onUIEventClick(go,param)
    local name = go.name
	if name == "btn_ok" or name == "btn_close" then
		self:Close()
	end	
end

function UI_TreasyreHuntTip:ShowSequenceAnim(root)
    local list = {
        GetChild(root,"Imgbg0"),
        GetChild(root,"row1"),
        GetChild(root,"Imgbg1"),
        GetChild(root,"row2"),
        GetChild(root,"Imgbg2"),
    }
    self.sequence = TweenMgr:CreateSequence(UIDefine.UI_TreasyreHuntTip, false, nil)
    for i = 1,#list do
        list[i].transform.localScale = Vector3.New(0,0,0)
        if(i > 1) then
            self.sequence:AppendInterval(0.15)
        end
        self.sequence:AppendCallback(function()
            self:ShowHelpCell(list[i].transform)
        end)
    end
end

function UI_TreasyreHuntTip:ShowHelpCell(root)
    root.localScale = Vector3.New(0,0,1)
    root:DOScale(Vector3.New(1.2,1.2,1),0.1):OnComplete(function()
        root:DOScale(Vector3.New(0.9,0.9,1),0.1):OnComplete(function()
            root:DOScale(Vector3.New(1.05,1.05,1),0.1):OnComplete(function()
                root:DOScale(Vector3.New(1,1,1),0.1)
            end)
        end)
    end)
end

return UI_TreasyreHuntTip