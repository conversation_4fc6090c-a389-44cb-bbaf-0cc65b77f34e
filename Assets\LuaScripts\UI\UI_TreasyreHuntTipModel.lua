local UI_TreasyreHuntTipModel = {}

UI_TreasyreHuntTipModel.config = {["name"] = "UI_TreasyreHuntTip", ["layer"] = UILayerType.Normal, ["type"] = UIType.Pop, ["isAutoClose"] = true, ["anim"] = 0,["background"] = 1, ["onEscape"] = false, ["tinyGamePath"] = nil}

function UI_TreasyreHuntTipModel:Init(c)
    c.ui = {}    
    c.ui.m_txtDesc = GetChild(c.uiGameObject,"bg/centerBg/m_txtDesc",UEUI.Text)
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_TreasyreHuntTipModel