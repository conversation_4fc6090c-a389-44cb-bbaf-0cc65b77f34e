local UI_UnionBoss = Class(BaseView)

local selectBossId;
local bossSkillNum = 4;

function UI_UnionBoss:OnInit()
    EventMgr:Add(EventID.UNION_ID_CHANGE, self.Close, self);
    EventMgr:Add(EventID.REFRESH_UNION_BOSS, self.OnUpdateBoss, self);

    selectBossId = LeagueManager.boss_id;
    self.selectBossInfo = LeagueManager.curBossInfo;

    self.deltaTime = 0;
    self.refreshTime = 0;
    self.bossItemList = {};
    self.firstIndex = 0;
    self.toyPrefabPath = "";
    self.showHurtList = {};
    self.hideHurtList = {};
    self.isPlayHurt = false;
    self.curPosY = 0;
    self.playWaitTime = 0;

    local buyCost = Split1(LeagueManager:GetConfigById(107), "|");
    self.buyItemId = v2n(buyCost[1]);
    self.buyCostNum = v2n(buyCost[2]);
    self.buyAddNum = v2n(LeagueManager:GetConfigById(108));
    self.buyLimitNum = v2n(LeagueManager:GetConfigById(109));
    self.refreshLimitTime = v2n(LeagueManager:GetConfigById(111));
end

function UI_UnionBoss:OnCreate(param)
    CreateCommonHead(self.ui.m_goHurtItem, 0.44, Vector2(-148, 8));
    table.insert(self.hideHurtList, self.ui.m_goHurtItem);

    self.ui.m_txtNone.text = LangMgr:GetLang(70000322);

    local canvas = GetComponent(self.ui.m_goWindow, UE.Canvas);
    canvas.sortingOrder = self:GetViewSortingOrder() + 5;

    local particleRenderer = GetChild(self.ui.m_goBossItem, "bg/fightObj/Particle System", UE.Renderer);
    local particleRenderer2 = GetChild(self.ui.m_goBossItem, "bg/fightObj/Particle System/Particle System (1)", UE.Renderer);
    SetRendererOrder(particleRenderer, SortingLayerInGame.Default, self:GetViewSortingOrder() + 6);
    SetRendererOrder(particleRenderer2, SortingLayerInGame.Default, self:GetViewSortingOrder() + 7);

    self.btnPress = self.ui.m_goBoss.transform:GetComponent(typeof(CS.ButtonPressed));
    self.btnPress.buttonPressed = function()
        local monsterInfo = LeagueManager:GetMonsterInfo(selectBossId);
        local monsterVo = HeroModule.new(monsterInfo.id);
        monsterVo:initData();
        monsterVo:SetHeroValueByKey("isMonster", true);
        monsterVo:SetHeroValueByKey("level", monsterInfo.level);
        monsterVo:SetHeroValueByKey("starLv", monsterInfo.star);
        monsterVo:SetHeroValueByKey("atk", monsterInfo.atk);
        monsterVo:SetHeroValueByKey("hp", monsterInfo.hp);
        monsterVo:SetHeroValueByKey("def", monsterInfo.def);
        monsterVo:SetHeroValueByKey("power", monsterInfo.fight);
        UI_SHOW(UIDefine.UI_SlgHeroDetailView, monsterVo);
    end

    --self.ui.m_scrollview.onValueChanged:AddListener(function()
    --    self:OnRound();
    --end);

    self:SetIsUpdateTick(true);
    self:StartHurtBullet();
    self:OnUpdateBoss();
    self:StartToIndex(self.firstIndex);

    self:TryGuide()
end

function UI_UnionBoss:OnRefresh(types, param)
    if types == 1 then
        if not UIMgr:ViewIsShow(UIDefine.UI_SlgChooseBattleView)
                and not UIMgr:ViewIsShow(UIDefine.UI_SlgBattleMainView) then
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000391));
        end
        self:OnSelectBoss(param);
    end
end

function UI_UnionBoss:onDestroy()
    EventMgr:Remove(EventID.UNION_ID_CHANGE, self.Close, self);
    EventMgr:Remove(EventID.REFRESH_UNION_BOSS, self.OnUpdateBoss, self);
    selectBossId = nil;
    self:CloseOtherWin();
end

function UI_UnionBoss:onUIEventClick(go, param)
    local name = go.name
    if name == "closeBtn" then
        self:Close();
    elseif name == "m_btnRecord" then
        UI_SHOW(UIDefine.UI_UnionBossRecord);
    elseif name == "m_btnReward" then
        UI_SHOW(UIDefine.UI_UnionBossReward);
    elseif name == "m_btnRank" then
        UI_SHOW(UIDefine.UI_UnionBossRank);
    elseif name == "m_goBest" then
        UI_SHOW(UIDefine.UI_UnionBossBest, selectBossId);
    elseif name == "m_btnFight" then
        if not self.canFight then
            if tonumber(tostring(self.selectBossInfo.hp)) <= 0 then
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000391));
            else
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000392));
            end
            return ;
        elseif LeagueManager:GetBossActEndTime() <= 0 then
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000388));
            return ;
        end

        local function openEmbattle()
            if tonumber(tostring(self.selectBossInfo.hp)) <= 0 then
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000391));
                LeagueManager:OnRequestBossLoad();
            else
                BattleSceneManager:GotoChooseUnionBossTeam(selectBossId);
            end
        end

        if LeagueManager:GetBossFightNum() > 0 then
            openEmbattle();
        elseif self.buyLimitNum > LeagueManager.charge_count then
            local costNum = self.buyCostNum + self.buyAddNum * LeagueManager.charge_count;
            local remianNum = self.buyLimitNum - LeagueManager.charge_count;
            UI_SHOW(UIDefine.UI_TipsTop, LangMgr:GetLangFormat(70000205, costNum, remianNum), function()
                local bagNum = BagManager:GetBagItemCount(self.buyItemId);
                if bagNum >= costNum then
                    LeagueManager:OnRequestBossBuyCount(function()
                        openEmbattle();
                    end)
                else
                    UI_SHOW(UIDefine.UI_ActCenter, 13)
                    --UI_SHOW(UIDefine.UI_Shop);
                end
            end);
        else
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000394));
        end
    elseif name == "m_btnSend" then
        UI_SHOW(UIDefine.UI_UnionBossCall, selectBossId);
    elseif string.startswith(name, "m_goSkill") then
        local index = v2n(string.gsub(name, "m_goSkill", ""));
        if index then
            local monsterHeroVo = LeagueManager:GetMonsterHeroVo(selectBossId);
            if monsterHeroVo then
                local skillConfig = monsterHeroVo:GetHeroSkillConfig(index);
                UI_SHOW(UIDefine.UI_HeroSkillTips, skillConfig);
            end
        end
    end
end

function UI_UnionBoss:TickUI(deltaTime)
    self.deltaTime = self.deltaTime + deltaTime;
    if self.deltaTime >= 1 then
        self.deltaTime = self.deltaTime - 1;

        self.remainTime = LeagueManager:GetBossActEndTime();
        if self.remainTime <= 0 then
            self.remainTime = 0;
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000388));
            self:Close();
            return ;
        end
        self.ui.m_txtTime.text = LangMgr:GetLangFormat(70000306, TimeMgr:CutBuyWorkTime(self.remainTime));

        self.refreshTime = self.refreshTime + 1;
        if self.refreshTime >= self.refreshLimitTime then
            self.refreshTime = 0;
            -- 每隔5秒，同步数据
            LeagueManager:OnRequestBossSync();
        end

        if self.playWaitTime > 0 then
            self.playWaitTime = self.playWaitTime - 1;
            if self.playWaitTime <= 0 then
                self:StartHurtBullet();
            end
        end
        if self.isPlayHurt then
            self.playHurtTime = self.playHurtTime + 1;
            if self.playHurtTime >= 2 then
                self.playHurtTime = 0;
                self:PlayHurtBullet();
            end
        end
    end
end

function UI_UnionBoss:OnVisibleChange(isVisible)
    if isVisible then
        if self.ui then
            local ani = GetChild(self.ui.m_goBossEff, "fazhen", UE.Animation);
            ani:Play();
        end
    end
end

function UI_UnionBoss:OnUpdateBoss()
    if not self.ui then
        return
    end

    local list = LeagueManager.bossList;
    table.sort(list, function(a, b)
        return a.boss_id < b.boss_id;
    end)
    local count = #list;
    local num = #self.bossItemList;
    local len = count > num and count or num;
    local rootTrans = self.ui.m_scrollview.content;
    local index = 1;
    for i = 1, len do
        local item = self.bossItemList[i];
        if not item then
            item = CreateGameObjectWithParent(self.ui.m_goBossItem, rootTrans);
            table.insert(self.bossItemList, item);
        end
        SetActive(item, i <= count);

        if i <= count then
            if list[i].boss_id == selectBossId then
                index = i;
            end
            self:OnUpdateItem(item, list[i], i);
        end
    end
    --self:OnRound();
    UIRefreshLayout(rootTrans);
    --self:CreateScheduleFun(function()
    --    self:OnRound();
    --    UIRefreshLayout(rootTrans);
    --end, 0.02, 1);

    -- 同步当前Boss数据
    if selectBossId == LeagueManager.boss_id then
        self.selectBossInfo = LeagueManager.curBossInfo;
    else
        self.selectBossInfo = list[index];
    end

    self.remainTime = LeagueManager:GetBossActEndTime();
    if self.remainTime < 0 then
        self.remainTime = 0;
    end
    self.ui.m_txtTime.text = LangMgr:GetLangFormat(70000306, TimeMgr:CutBuyWorkTime(self.remainTime));

    local monsterHeroVo = LeagueManager:GetMonsterHeroVo(selectBossId);
    local monsterInfo = LeagueManager:GetMonsterInfo(selectBossId);
    self.ui.m_txtLevel.text = "Lv." .. monsterInfo.level;
    self.ui.m_txtName.text = monsterHeroVo:GetHeroName();
    SetUIImage(self.ui.m_imgHead, monsterHeroVo:GetHeroHead(), false);
    SetUIImage(self.ui.m_imgKind, HeroManager:GetHeroKindIcon(monsterHeroVo:GetHeroKind()), true);
    SetUIImage(self.ui.m_imgCareer, HeroManager:GetHeroCareerIcon(monsterHeroVo:GetHeroCareer()), true);

    local value = self.selectBossInfo.hp / self.selectBossInfo.max_hp;
    local showValue = value * 100;
    if value <= 0 or value >= 1 then
        showValue = Mathf.Floor(showValue);
    else
        showValue = string.format("%.2f", showValue);
    end
    self.ui.m_imgProgress.fillAmount = value;
    self.ui.m_txtProgress.text = showValue .. "%";

    local showState = 0;
    local isCurBoss = selectBossId == LeagueManager.boss_id;
    local curBossHp = tonumber(tostring(self.selectBossInfo.hp));
    --Log.Error(selectBossId, isCurBoss, curBossHp);
    self.canFight = isCurBoss and curBossHp > 0;
    if self.canFight then
        self.ui.m_txtNum.text = LangMgr:GetLangFormat(70000311, GetStrRichColor(LeagueManager:GetBossFightNum(), "30ff00"));
        self.ui.m_txtFight.text = LangMgr:GetLang(70000310);
        showState = 1;
    elseif selectBossId <= LeagueManager.boss_id then
        local headObj = GetChild(self.ui.m_goFinally, "CustomHead");
        if not headObj then
            headObj = CreateCommonHead(self.ui.m_goFinally, 0.36, Vector2(-325, 10));
        end
        SetHeadAndBorderByGo(headObj, self.selectBossInfo.last_hit_player_icon, self.selectBossInfo.last_hit_player_border);
        self.ui.m_txtFinally.text = self.selectBossInfo.last_hit_player_name .. GetStrRichColor(LangMgr:GetLang(70000313), "ff2727");
        self.ui.m_txtFight.text = LangMgr:GetLang(70000312);
        showState = 2;
    else
        local lastBossId = selectBossId - 1;
        local lastMonsterHeroVo = LeagueManager:GetMonsterHeroVo(lastBossId);
        if lastMonsterHeroVo then
            local nameStr = lastMonsterHeroVo:GetHeroName();
            self.ui.m_txtNum.text = LangMgr:GetLangFormat(70000315, GetStrRichColor(nameStr, "30ff00"));
        end
        self.ui.m_txtFight.text = LangMgr:GetLang(70000314);
        showState = 3;
    end
    self:SetFightBtnState(showState == 1);
    SetActive(self.ui.m_btnSend, showState == 1);
    SetActive(self.ui.m_txtNum, showState ~= 2);
    SetActive(self.ui.m_goFinally, showState == 2);
    SetActive(self.ui.m_goBossEff, curBossHp > 0);

    local prefabPath = monsterHeroVo:GetHeroToyPrefab();
    if self.toyPrefabPath ~= prefabPath then
        self.toyPrefabPath = prefabPath;
        if self.toyObj then
            UEGO.Destroy(self.toyObj);
        end
        if prefabPath then
            ResMgr:LoadAssetAsync(prefabPath, AssetDefine.LoadType.Instant, function(prefab)
                self.toyObj = CreateGameObjectWithParent(prefab, self.ui.m_goBoss);

                local transform = self.toyObj.transform;
                local dic = monsterHeroVo:GetHeroToyOffset();
                transform.localPosition = Vector3(v2n(dic[1]), v2n(dic[2]), v2n(dic[3]));
                dic = monsterHeroVo:GetHeroToyScale();
                transform.localScale = Vector3(v2n(dic[1]), v2n(dic[2]), v2n(dic[3]));

                GameUtil.SetLayer(self.toyObj, BloomLayer, 1);
                local render = self.toyObj.transform:GetComponentInChildren(typeof(UE.Renderer))
                SetRendererOrder(render, SortingLayerInGame.Default, self:GetViewSortingOrder() + 2);
            end);
        end
    end

    for i = 1, bossSkillNum do
        local skillObj = self.ui["m_goSkill" .. i];
        local skillConfig = monsterHeroVo:GetHeroSkillConfig(i);
        local isShow = skillObj and skillConfig;
        if isShow then
            local skillSp = GetChild(skillObj, "skillSp", UEUI.Image);
            SetUIImage(skillSp, skillConfig.icon, false);
        end
        SetActive(skillObj, isShow);
    end

    if not self.isPlayHurt and self.playWaitTime <= 0 then
        self:StartHurtBullet();
    end
    SetActive(self.ui.m_goHurt, isCurBoss);

    self:OnUpdateBossTop();
end

function UI_UnionBoss:OnUpdateItem(item, data, index)
    local bg = GetChild(item, "bg", UEUI.Image);
    local selectSp = GetChild(item, "bg/selectSp");
    local bossBg = GetChild(item, "bg/bossBg", UEUI.Image);
    local bossImg = GetChild(item, "bg/bossBg/bossImg", UEUI.Image);

    local titleObj = GetChild(item, "bg/titleObj");
    local normalBg = GetChild(item, "bg/titleObj/normalBg", UEUI.Image);
    local levelTxt = GetChild(item, "bg/titleObj/normalBg/levelTxt", UEUI.Text);
    local beatBg = GetChild(item, "bg/titleObj/beatBg");
    local beatTxt = GetChild(item, "bg/titleObj/beatBg/beatTxt", UEUI.Text);

    local fightObj = GetChild(item, "bg/fightObj");
    local redDot = GetChild(item, "bg/redDot");

    local selectAni = GetComponent(item, UE.Animation);

    local isSelect = data.boss_id == selectBossId;
    local isDefeat = tonumber(tostring(data.hp)) <= 0;
    SetActive(selectSp, isSelect);
    SetActive(normalBg, not isDefeat);
    SetActive(beatBg, isDefeat);

    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.union_boss, data.boss_id);
    if config then
        if isDefeat then
            local iconStr = isSelect and "lianmengboss_main_boss_kuang_hui1.png" or "lianmengboss_main_boss_kuang_hui2.png";
            SetUIImage(bg, "Sprite/ui_lianmeng/" .. iconStr, true);
            SetUIImage(beatBg, "Sprite/ui_lianmeng/lianmengboss_main_boss_kuang_hui3.png", false);
        else
            local quality = config.boss_quality;
            if quality == 3 then
                local iconStr = isSelect and "lianmengboss_main_boss_kuang_per1.png" or "lianmengboss_main_boss_kuang_per21.png";
                SetUIImage(bg, "Sprite/ui_lianmeng/" .. iconStr, true);
                SetUIImage(normalBg, "Sprite/ui_lianmeng/lianmengboss_main_boss_kuang_per3.png", true);
            else
                local iconStr = isSelect and "lianmengboss_main_boss_kuang_gold1.png" or "lianmengboss_main_boss_kuang_gold2.png";
                SetUIImage(bg, "Sprite/ui_lianmeng/" .. iconStr, true);
                SetUIImage(normalBg, "Sprite/ui_lianmeng/lianmengboss_main_boss_kuang_gold3.png", true);
            end
        end

        if isSelect then
            SetUIPos(bossBg, 0, -1);
            SetUISize(bossBg, 371, 166);
            if isDefeat then
                SetUIPos(beatBg, -21, -56.5);
            else
                SetUIPos(normalBg, -19, -19);
            end
            SetUISize(item, 412, 212);
            selectAni:Play();
            --SetUIPos(fightObj, -150, 0);
            item.transform.localScale = Vector3.one;
            SetUIPos(bg, 0, 0);
            SetUIPos(levelTxt, -28.5, 87);
        else
            SetUIPos(bossBg, -2.5, 1);
            SetUISize(bossBg, 366, 162);
            if isDefeat then
                SetUIPos(beatBg, -20, -49);
            else
                SetUIPos(normalBg, -18, -11);
            end
            SetUISize(item, 390, 212);
            selectAni:Stop();
            --SetUIPos(fightObj, -150, 0);
            item.transform.localScale = Vector3.one * 0.9;
            SetUIPos(bg, 0, -15);
            SetUIPos(levelTxt, -28.5, 80);
        end
    end

    local monsterInfo = LeagueManager:GetMonsterInfo(data.boss_id);
    if monsterInfo then
        local starNum = HeroManager:GetStarOrderByLv(monsterInfo.star);
        for i = 1, 5 do
            local starSp = GetChild(normalBg, "starObj/star" .. i);
            if starSp then
                SetActive(starSp, i <= starNum);
            end
        end

        levelTxt.text = "Lv." .. monsterInfo.level;
    end
    beatTxt.text = data.last_hit_player_name;

    local heroConfig = HeroManager:GetHeroConfigById(monsterInfo.id);
    SetUIImage(bossImg, heroConfig.resources_jingtai, false);

    local isCurBoss = data.boss_id == LeagueManager.boss_id;
    if self.firstIndex == 0 and isCurBoss then
        self.firstIndex = index;
    end
    SetActive(redDot, isCurBoss and not isDefeat and LeagueManager:GetBossFightNum() > 0);
    SetActive(fightObj, isCurBoss and not isDefeat);

    RemoveUIComponentEventCallback(bg, UEUI.Button);
    AddUIComponentEventCallback(bg, UEUI.Button, function()
        self:OnSelectBoss(data.boss_id);
    end)
end

function UI_UnionBoss:OnSelectBoss(bossId)
    if selectBossId ~= bossId then
        selectBossId = bossId;
        self:OnUpdateBoss();

        LeagueManager:OnRequestBossTop(bossId, function(respData)
            if respData.boss_id == bossId then
                self:OnUpdateBossTop(respData.top);
            end
        end);
    end
end

function UI_UnionBoss:OnUpdateBossTop(topInfo)
    if topInfo == nil then
        if selectBossId ~= LeagueManager.boss_id then
            return ;
        end
        topInfo = LeagueManager.topInfo;
    end

    if topInfo and topInfo.list then
        local list = topInfo.list;
        local len = #list;
        for i = 1, 3 do
            local rankItem = self.ui["m_goRank" .. i];
            if rankItem then
                local icon = GetChild(rankItem, "icon", UEUI.Image);
                local titleTxt = GetChild(rankItem, "titleTxt", UEUI.Text);
                local nameTxt = GetChild(rankItem, "nameTxt", UEUI.Text);
                local noneTxt = GetChild(rankItem, "noneTxt");

                titleTxt.text = LangMgr:GetLangFormat(70000321, i);

                if len >= i then
                    local leagueInfo = list[i].league;
                    if leagueInfo then
                        SetUIImage(icon, LeagueManager:GetUnionImageById(leagueInfo.icon), false);
                        nameTxt.text = leagueInfo.name;
                    end
                else
                    SetUIImage(icon, "Sprite/ui_lianmeng/lianmengboss_main_icon_lianmeng.png", false);
                end
                SetActive(nameTxt, len >= i);
                SetActive(noneTxt, len < i);
                --SetActive(rankItem, len > 0);
                SetActive(rankItem, true);
            end
        end
        --SetActive(self.ui.m_txtNone, len <= 0);
    end
end

function UI_UnionBoss:SetFightBtnState(val)
    if val then
        UnifyOutline(self.ui.m_txtFight, "792500");
        SetUIImage(self.ui.m_btnFight, "Sprite/ui_lianmeng/lianmengboss_main_button1.png", false);
    else
        UnifyOutline(self.ui.m_txtFight, "202020");
        SetUIImage(self.ui.m_btnFight, "Sprite/ui_lianmeng/lianmengboss_main_button1_1.png", false);
    end
end

function UI_UnionBoss:OnRound()
    local rootWidth = self.ui.m_scrollview.viewport.rect.width;
    local rootPosX = self.ui.m_scrollview.content.anchoredPosition.x;
    local startHeight = 0;
    local cellWidth = 200;
    local radius = 750;
    local offsetX = 0;
    local offsetY = 650;
    for i = 1, #self.bossItemList do
        local item = self.bossItemList[i];
        local pos = GetUIPos(item);
        local posX = -pos.x - rootPosX;
        if posX < (cellWidth - startHeight) and posX > -(rootWidth + cellWidth) then
            local disX = Mathf.Abs(posX + rootWidth / 2 - offsetX);
            local disY = Mathf.Abs(radius ^ 2 - disX ^ 2);
            disY = disY ^ 0.5;

            local bg = GetChild(item, "bg");
            SetUIPos(bg, 0, offsetY - disY);
        end
    end
end

function UI_UnionBoss:StartToIndex(index)
    if not index or index < 4 then
        return ;
    end

    local rootTrans = self.ui.m_scrollview.content;
    local cellHeight = 166 + 20;
    local height = index * cellHeight;
    local offsetY = height - 985 / 2;
    if offsetY > 0 then
        SetUIPos(rootTrans, 0, offsetY);
    end
end

function UI_UnionBoss:StartHurtBullet()
    local list = LeagueManager.logList;
    local arr = {};
    for i = 1, #list do
        if v2s(list[i].damage) ~= "0" then
            table.insert(arr, list[i]);
        end
    end
    self.playerList = arr;
    self.playCount = #self.playerList;
    if self.playCount > 0 then
        self.isPlayHurt = true;
        self.playHurtTime = 0;
        self.playIndex = 0;
        self.playWaitTime = 0;
        self:PlayHurtBullet();
    else
        self.isPlayHurt = false;
        self:hideAllHurtItem();
    end
end

function UI_UnionBoss:PlayHurtBullet()
    if self.playIndex >= self.playCount then
        self.isPlayHurt = false;
        self.playWaitTime = 30;
        return ;
    end
    self.playIndex = self.playIndex + 1;

    local item = self:GetHurtItem();
    local bg = GetComponent(item, UEUI.Image);
    local nameTxt = GetChild(item, "nameTxt", UEUI.Text);
    local hurtSp = GetChild(item, "hurtSp", UEUI.Image);
    local numTxt = GetChild(item, "numTxt", UEUI.Text);
    local numColorText = GetChild(item, "numTxt", CS.ColorText);

    local data = self.playerList[self.playIndex];
    nameTxt.text = data.player_name;
    numTxt.text = NumToGameString(data.damage);

    local customHeadObj = GetChild(item, "CustomHead");
    SetHeadAndBorderByGo(customHeadObj, data.player_icon, data.player_border);

    if self.playIndex <= 3 then
        numColorText.colorTop = GetColorByHex("ff5b72");
        numColorText.colorBottom = GetColorByHex("fc3535");
        UnifyOutline(numTxt, "3f0c00");
        UnifyOutline(nameTxt, "781f00");
        SetUIImage(bg, "Sprite/ui_lianmeng/lianmengboss_main_danmu_2.png", false);
        SetUIImage(hurtSp, "Sprite/ui_lianmeng/lianmengboss_main_danmu_shanghai1.png", false);
    else
        numColorText.colorTop = GetColorByHex("ffa054");
        numColorText.colorBottom = GetColorByHex("ff682c");
        UnifyOutline(numTxt, "300900");
        UnifyOutline(nameTxt, "24183e");
        SetUIImage(bg, "Sprite/ui_lianmeng/lianmengboss_main_danmu_1.png", false);
        SetUIImage(hurtSp, "Sprite/ui_lianmeng/lianmengboss_main_danmu_shanghai2.png", false);
    end

    local topPosY = 0;
    local downPosY = -350;
    local offsetY = 80;
    local isTop = Mathf.Random(0, 50) <= 30;
    local posY;
    if isTop then
        posY = self.curPosY + offsetY;
        if posY >= topPosY then
            posY = self.curPosY - offsetY;
        else
            posY = Mathf.Random(posY, topPosY);
        end
    else
        posY = self.curPosY - offsetY;
        if posY <= downPosY then
            posY = self.curPosY + offsetY;
        else
            posY = Mathf.Random(downPosY, posY);
        end
    end
    self.curPosY = posY;

    local rootTrans = item.transform;
    local halfWidth = UIWidth / 2 + 200;
    DOKill(rootTrans);
    SetLocalPositionTrans(rootTrans, halfWidth, posY);
    DOLocalMoveX(rootTrans, -halfWidth, 16, function()
        self:hideHurtItem();
    end, Ease.Linear);
    SetActive(item, true);
end

function UI_UnionBoss:GetHurtItem()
    local item;
    if #self.hideHurtList > 0 then
        item = table.remove(self.hideHurtList, 1);
    else
        item = CreateGameObjectWithParent(self.ui.m_goHurtItem, self.ui.m_goHurt);
    end
    table.insert(self.showHurtList, item);
    return item;
end

function UI_UnionBoss:hideHurtItem()
    if #self.showHurtList > 0 then
        local item = table.remove(self.showHurtList, 1);
        SetActive(item, false);
        table.insert(self.hideHurtList, item);
    end
end

function UI_UnionBoss:hideAllHurtItem()
    local len = #self.showHurtList;
    for i = 1, len do
        local item = table.remove(self.showHurtList, 1);
        SetActive(item, false);
        table.insert(self.hideHurtList, item);
    end
end

function UI_UnionBoss:CloseOtherWin()
    if UIMgr:ViewIsShow(UIDefine.UI_UnionBossCall) then
        UI_CLOSE(UIDefine.UI_UnionBossCall);
    end
    if UIMgr:ViewIsShow(UIDefine.UI_UnionBossRecord) then
        UI_CLOSE(UIDefine.UI_UnionBossRecord);
    end
    if UIMgr:ViewIsShow(UIDefine.UI_UnionBossReward) then
        UI_CLOSE(UIDefine.UI_UnionBossReward);
    end
    if UIMgr:ViewIsShow(UIDefine.UI_UnionBossRank) then
        UI_CLOSE(UIDefine.UI_UnionBossRank);
    end
    if UIMgr:ViewIsShow(UIDefine.UI_UnionBossBest) then
        UI_CLOSE(UIDefine.UI_UnionBossBest);
    end
end

function UI_UnionBoss:TryGuide()

    if NetLeagueData:GetUnionBossGuide() == 1 then
        return
    end

    local function GuideOver()
        NetLeagueData:SetUnionBossGuide(1)
        UI_CLOSE(UIDefine.UI_GuideMask)
        local go = {
            ["name"] = "m_btnFight"
        }
        self:onUIEventClick(go)
    end

    -- 关闭帮助界面，引导每日主题
    local function Guide2()
        local centerPos = self:ConvertToRectPos(self.ui.m_btnFight)
        local rt = GetComponent(self.ui.m_btnFight, typeof(UE.RectTransform))
        local width = rt.rect.width
        local height = rt.rect.height
        UI_UPDATE(UIDefine.UI_GuideMask, GuideMask_Refresh.SetCenter, centerPos)
        UI_UPDATE(UIDefine.UI_GuideMask, GuideMask_Refresh.SetShow, { 3, width, height })
        UI_UPDATE(UIDefine.UI_GuideMask, GuideMask_Refresh.SetBtnSize, { 5, 5 })
        UI_UPDATE(UIDefine.UI_GuideMask, GuideMask_Refresh.SetDialog, { 3, centerPos[2] / 100 + height / 100 / 2 + 2, 70000674 })
        UI_UPDATE(UIDefine.UI_GuideMask, GuideMask_Refresh.SetArrow, { centerPos[1] / 100, centerPos[2] / 100 + height / 100 / 2 + 0.6, 0, 0, 0 })
        UI_UPDATE(UIDefine.UI_GuideMask, GuideMask_Refresh.SetCallBack, function()
            GuideOver()
        end)
    end


    -- 第一步引导帮助按钮
    local centerPos = self:ConvertToRectPos(self.ui.m_btnReward)
    UI_SHOW(UIDefine.UI_GuideMask, {
        { 2, 0, 90 }, -- 遮罩类型和大小
        centerPos, -- 遮罩位置
        { 2, 2 }, -- 遮罩按钮大小
        0.5, -- 缩放动画的时长
        function()
            Guide2()
        end, -- 点击回调
        { centerPos[1] / 100, centerPos[2] / 100 + 2, 0, 0, 0 }, -- 箭头位置
        { 1, 0, 70000673 }, -- 对话框位置和内容
        "Sprite/new_hero/headFrame_1.png", -- 对话框头像
        nil,
    })
end

--- 转换 UI 坐标
--- @param go any UI 节点
--- @return table 坐标表
function UI_UnionBoss:ConvertToRectPos(go)
    local cam = UIMgr:GetCamera()
    local screenPoint = UE.RectTransformUtility.WorldToScreenPoint(cam, go.transform.position)
    local _, pos = UE.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.uiRectTransform,
            Vector2.New(screenPoint.x, screenPoint.y), cam)
    local posTable = { pos.x, pos.y }
    return posTable
end

return UI_UnionBoss