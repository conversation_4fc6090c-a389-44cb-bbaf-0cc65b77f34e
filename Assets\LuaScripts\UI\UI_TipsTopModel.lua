local UI_TipsTopModel = {}

UI_TipsTopModel.config = {["name"] = "UI_TipsTop", ["layer"] = UILayerType.Top, ["type"] = UIType.Pop, ["isAutoClose"] = true, ["anim"] = 1,["background"] = 1, ["onEscape"] = false, ["tinyGamePath"] = nil}

function UI_TipsTopModel:Init(c)
    c.ui = {}    
    c.ui.m_btnClose = GetChild(c.uiGameObject,"m_btnClose",UEUI.Button)
    c.ui.m_txtTips = GetChild(c.uiGameObject,"m_txtTips",UEUI.Text)
    c.ui.m_txtTitle = GetChild(c.uiGameObject,"m_txtTitle",UEUI.Text)
    c.ui.m_btnOK = GetChild(c.uiGameObject,"m_btnOK",UEUI.Button)
    c.ui.m_txtOk = GetChild(c.uiGameObject,"m_btnOK/m_txtOk",UEUI.Text)
    c.ui.m_imgIconOk = GetChild(c.uiGameObject,"m_btnOK/ok/m_imgIconOk",UEUI.Image)
    c.ui.m_txtOk = GetChild(c.uiGameObject,"m_btnOK/ok/m_txtOk",UEUI.Text)
    c.ui.m_btnCancel = GetChild(c.uiGameObject,"m_btnCancel",UEUI.Button)
    c.ui.m_txtCancel = GetChild(c.uiGameObject,"m_btnCancel/m_txtCancel",UEUI.Text)
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_TipsTopModel