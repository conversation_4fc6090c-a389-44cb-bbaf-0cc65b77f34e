local UI_JJcChallenge = Class(BaseView)
function UI_JJcChallenge:OnInit()
    
end

function UI_JJcChallenge:OnCreate()
	self.canRefrsh = true
	self.serverData = JJcManager:GetData()
	self.enemies = self.serverData.enemies
	self.challenge_count = self.serverData.arena.challenge_count
	self.refresh_count = self.serverData.arena.refresh_count
	self.isUpLevel = self.serverData.arena.play_type == 1
	self.rank_fight_result_list = self.serverData.arena.rank_fight_result_list or {}
	self:InitPanel()
	EventMgr:Add(EventID.BAG_CHANGE, self.BagChange, self)
	EventMgr:Add(EventID.CLOSE_SlgChooseBattleView,self.PlayAnim,self)
end

function UI_JJcChallenge:PlayAnim(deltaTime)
	for i = 1, 5 do
		local enemiesObj = self.ui["m_goItem"..i]
		local fightObj = GetChild(enemiesObj,"obj/fightObj")
		local animation = GetComponent(fightObj,UE.Animation)
		animation:Play()
	end
end

function UI_JJcChallenge:BagChange()
	self.ui.m_txtRefresh.text = BagManager:GetBagItemCount(ItemID.JJcRefreshTicket)
	self.ui.m_txtChallenge.text = BagManager:GetBagItemCount(ItemID.JJcFightTicket)
end

function UI_JJcChallenge:OnRefresh(type)
	if type == 1 then
		self.serverData = JJcManager:GetData()
		self.enemies = self.serverData.enemies	
		self.challenge_count = self.serverData.arena.challenge_count
		self.refresh_count = self.serverData.arena.refresh_count
		self.isUpLevel = self.serverData.arena.play_type == 1
		self.rank_fight_result_list = self.serverData.arena.rank_fight_result_list
		self:InitPanel()
	end
end

function UI_JJcChallenge:InitPanel()
	
	for i = 1, 5 do
		local enemiesObj = self.ui["m_goItem"..i]
		local data = self.enemies[i]
		if enemiesObj and data then
			local name = GetChild(enemiesObj,"obj/myName",UEUI.Text)
			name.text = data.player_name
			
			local jjcRankItem = GetChild(enemiesObj,"obj/jjcRankItem")
			JJcManager:UpdateJJcItem(jjcRankItem,data.rank_id,data.rank_star)
			
			local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_rank,data.rank_id)
			local txt_level = GetChild(enemiesObj,"obj/txt_level",UEUI.Text)
			txt_level.text = LangMgr:GetLang(config.langid)
			
			local state = GetChild(enemiesObj,"obj/state",UEUI.Image)
			if self.isUpLevel then
				--local path = "Sprite/ui_slg_jingjisai/jingji_jingjisai_wu.png"
				--SetActive(state.gameObject,self.enemies[i].play_status ~= 0)
				SetActive(state.gameObject,false)
				if self.enemies[i].play_status == 1 then
					local path = "Sprite/ui_public/windows_icon_right.png"
					SetUIImage(state,path,true)
					SetActive(state.gameObject,true)
				end
				if self.enemies[i].play_status == 2 then
					local path = "Sprite/ui_public/windows_icon_wrong.png"
					SetUIImage(state,path,true)
					SetActive(state.gameObject,true)
				end
			else
				SetActive(state.gameObject,false)
			end

			local txt_power = GetChild(enemiesObj,"obj/txt_power",UEUI.Text)
			--txt_power.text = self:ToStringPower(data.power)
			txt_power.text = NumToGameString(data.power)
					
			local customHeadObj = GetChild(enemiesObj,"obj/headNode/CustomHead")
			local head_set
			if data.is_robot ~= 0 then
				head_set = ConfigMgr:GetDataByKey(ConfigDefine.ID.head_set, "item_id", data.player_icon)
			else
				head_set = ConfigMgr:GetDataByKey(ConfigDefine.ID.head_set, "id", data.player_icon)
			end		 
			SetHeadAndBorderByGo(customHeadObj,head_set and head_set.id or 1001,data.player_border or 0,function()
					
				end)
			customHeadObj.transform.localPosition = Vector3.zero
			local obj = GetChild(enemiesObj,"obj")
			local btn = GetChild(enemiesObj,"obj",UEUI.Button)
			if btn then
				AddUIComponentEventCallback(btn, UEUI.Button, function(arg1,arg2)
						local playerId = self.enemies[i].player_id
						local is_robot = self.enemies[i].is_robot
						local power = self.enemies[i].power
						if self.enemies[i].play_status ~= 0 and self.isUpLevel then
							UI_SHOW(UIDefine.UI_WidgetTip,LangMgr:GetLang(70000689))
							return
						end
						if JJcManager:IsInUpLevel() then
							BattleSceneManager:GotoChooseArenaTeam(playerId,is_robot,power)
							return
						end
						if self.challenge_count >= v2n(JJcManager:GetSettingById(5)) and BagManager:GetBagItemCount(ItemID.JJcFightTicket) < 1 then
							UI_SHOW(UIDefine.UI_SlgGetWay, {{id = ItemID.JJcFightTicket, needNum = 1}})
							return
						end	
						BattleSceneManager:GotoChooseArenaTeam(playerId,is_robot,power)
					end)
			end
			
			local fightObj = GetChild(enemiesObj,"obj/fightObj")
			if self.isUpLevel then
				SetActive(fightObj,self.enemies[i].play_status == 0)
			else
				SetActive(fightObj,true)
			end
			SetActive(enemiesObj,true)
		else
			SetActive(enemiesObj,false)
		end
	end
	if self.isUpLevel then
		SetActive(self.ui.m_btnRefresh,false)
		SetActive(self.ui.m_goState,true)
		self.ui.m_txtTitle.text = LangMgr:GetLang(70000549)
		local winNum = 0
		for k, v in pairs(self.rank_fight_result_list) do
			if v == 1 then
				winNum = winNum + 1
			end
		end
		self.ui.m_txtDes.text = LangMgr:GetLangFormat(70000685,winNum)
	else
		SetActive(self.ui.m_btnRefresh,true)
		SetActive(self.ui.m_goState,false)
		self.ui.m_txtTitle.text = LangMgr:GetLang(70000310)
		self.ui.m_txtDes.text = ""
	end
	local imageList = {}
	for i = 1, 5 do
		local img = GetChild(self.ui.m_goState,"stateIndex"..i,UEUI.Image)
		if img then
			if self.rank_fight_result_list[i] then
				if self.rank_fight_result_list[i] == 1 then
					SetUIImage(img,"Sprite/ui_slg_jingjisai/jingji_jingjisai_dagou.png",true)
				end
				if self.rank_fight_result_list[i] == 2 then
					SetUIImage(img,"Sprite/ui_slg_jingjisai/jingji_jingjisai_dacha.png",true)
				end
			end
		end
	end
	self.ui.m_txtEndTitle.text = LangMgr:GetLang(70000692)
	self.ui.m_txtRefresh.text = BagManager:GetBagItemCount(ItemID.JJcRefreshTicket)
	self.ui.m_txtChallenge.text = BagManager:GetBagItemCount(ItemID.JJcFightTicket)
	local freeTime = JJcManager:GetSettingById(6)
	local now_freeTime = math.floor(v2n(freeTime) - self.refresh_count)
	self.ui.m_txtFreeTime.text = LangMgr:GetLangFormat(70000544,now_freeTime)
	SetActive(self.ui.m_txtFreeTime.gameObject,now_freeTime > 0)
	
	if self.isUpLevel and table.count(self.enemies) >= 5 then
		SetActive(self.m_goPlayerList,true)
	else
		SetActive(self.m_goPlayerList,false)
	end
	if self.isUpLevel then
		SetActive(self.ui.m_txtEndTitle.gameObject,table.count(self.enemies) < 5)
	else
		SetActive(self.ui.m_txtEndTitle.gameObject,table.count(self.enemies) < 1)
	end
end

function UI_JJcChallenge:ToStringPower(power)
	local num = tostring(power)
	local new_num = ""
	local index = 0
	for i = string.len(num),1,-1  do
		local str = string.sub(num,i,i)
		index = index + 1
		if index%3 == 0 and i ~= 1 then
			str = ","..str
		end
		new_num = str..new_num
	end
	return new_num
end

function UI_JJcChallenge:onDestroy()
	EventMgr:Remove(EventID.BAG_CHANGE, self.BagChange, self)
	EventMgr:Remove(EventID.CLOSE_SlgChooseBattleView,self.PlayAnim,self)
end

function UI_JJcChallenge:onUIEventClick(go,param)
    local name = go.name
	if name == "m_btnClose" then
		self:Close()
	elseif name == "m_btnRefresh" then
		local playTick = false
		--if self.serverData.arena.
		if self.refresh_count >= v2n(JJcManager:GetSettingById(6)) and BagManager:GetBagItemCount(ItemID.JJcRefreshTicket) < 1 then
			UI_SHOW(UIDefine.UI_SlgGetWay, {{id = ItemID.JJcRefreshTicket, needNum = 1}})
			return
		end
		--if self.canRefrsh then
			--self.canRefrsh = false
			JJcManager:OnReqArenaRefresh()
		--end
	elseif name == "m_btnBuyF" then
		UI_SHOW(UIDefine.UI_SlgItemBuyTip,ItemID.JJcRefreshTicket)
	elseif name == "m_btnBuyC" then
		UI_SHOW(UIDefine.UI_SlgItemBuyTip,ItemID.JJcFightTicket)
	elseif name == "imgRefresh" then
		UI_SHOW(UIDefine.UI_ItemTips,ItemID.JJcRefreshTicket)
	elseif name == "imgFight" then
		UI_SHOW(UIDefine.UI_ItemTips,ItemID.JJcFightTicket)
	end
end



return UI_JJcChallenge