---



---
local Super = _BaseConfig
local GuideConfig = Class(Super)

-------------------------------------------------------

function GuideConfig:ctor()
end

----override super
--function GuideConfig:ReadConfig(tb)
--end
----override super
--function GuideConfig:ClearConfig()
--end

-------------------------------------------------------

function GuideConfig:GenWH()
    local w = 1080 
    local h = 1920
    local aspect = w / h

    local device_width = ScreenWidth
    local device_height = ScreenHeight
    local device_aspect = device_width / device_height

    local diff = Mathf.Abs(Mathf.Abs(aspect) - Mathf.Abs(device_aspect))
    if diff < 0.01 then
        self.m_ScreenRadioState = 0
    else
        if aspect < device_aspect then
            self.m_ScreenRadioState = 1

        elseif aspect > device_aspect then
            self.m_ScreenRadioState = 2
        end
    end
    Log.Warning("______ScTest : ", aspect, device_aspect, diff, self.m_ScreenRadioState)
end

function GuideConfig:ConvertPos(vt, anchor)
    if not anchor or anchor == 0 or anchor == 1 then
        return vt
    end

    if vt.x == 0 and vt.y == 0 then
        return vt
    end

    if self.m_ScreenRadioState == 1 then
        local ocx = (1080 / 2)
        local ncx = UIWidth / 2
        if anchor == 2 or anchor == 5 or anchor == 6 then
            vt.x = (vt.x + ocx) - ncx
        elseif anchor == 3 or anchor == 4 or anchor == 8 then
            vt.x = (vt.x - ocx) + ncx
        end

    elseif self.m_ScreenRadioState == 2 then
        local ocy = (1920 / 2)
        local ncy = UIHeight / 2
        if anchor == 2 or anchor == 3 or anchor == 9 then
            vt.y = (vt.y - ocy) + ncy
            if Game.IsWechatGame() then
                vt.y = vt.y - 150 / 2
            end
        elseif anchor == 4 or anchor == 5 or anchor == 7 then
            vt.y = (vt.y + ocy) - ncy
        end
    end
    return vt
end

function GuideConfig:ReadPosArr(json)
    if not json then
        return
    end
    local data = nil
    if not string.equals(json, "0") then
        data = {}

        local strIterator = string.gmatch(json, "(%d+)|(%d+)")
        for x, y in strIterator do
            table.insert(data, { GridX = tonumber(x), GridY = tonumber(y) })
        end
    else
        data = 0
    end
    return data
end

function GuideConfig:ReadHollow(json)
    if not json then
        return
    end
    local data = nil
    if not string.equals(json, "0") then
        data = {}
        local strArr = string.split(json, ';')
        for i, v in ipairs(strArr) do
            local strPos = string.split(v, '|')
            local lenType = #strPos
            if lenType == 2 then
                table.insert(data, { GridX = tonumber(strPos[1]), GridY = tonumber(strPos[2]) })
            elseif lenType == 1 then
                table.insert(data, { id = tonumber(strPos[1]) })
            end
        end
    else
        data = 0
    end
    return data
end

function GuideConfig:ReadGridLimit(json)
    if not json then
        return
    end
    local data = nil
    if not string.equals(json, "0") then
        data = {}
        local strArr = string.split(json, ';')
        for i, v in ipairs(strArr) do
            local strPos = string.split(v, '|')
            local lenType = #strPos
            if lenType == 3 then
                table.insert(data, { GridX = tonumber(strPos[1]), GridY = tonumber(strPos[2]), LockType = tonumber(strPos[3]) })
            elseif lenType == 2 then
                table.insert(data, { id = tonumber(strPos[1]), LockType = tonumber(strPos[2]) })
            end
        end
    else
        data = 0
    end
    return data
end

function GuideConfig:ReadVt2(json, isToVt3)
    if not json then
        return nil
    end
    local cx, cy = string.match(json, "(-?%d+.?%d*)|(-?%d+.?%d*)")
    if isToVt3 then
        return Vector3.New(tonumber(cx), tonumber(cy), 0)
    end
    return Vector2.New(tonumber(cx), tonumber(cy))
end

function GuideConfig:ReadTipInf(json)
    if not json then
        return nil
    end
    local arr = string.split(json, '|')
    local inf = {}
    inf["type"] = GetNumberValue(arr[1])
    inf["param"] = (arr[2])
    inf["x"] = GetNumberValue(arr[3], 0)
	inf["y"] = GetNumberValue(arr[4], 0)
    inf["iconIndex"] = GetNumberValue(arr[5], 0)
    return inf
end

function GuideConfig:ReadFingerInf(json)
    if not json then
        return nil
    end

    local fingerInf = {}
    local jId = json["finger_id"]
    if jId then
        fingerInf["id"] = jId
    end

    local jInf = json["finger_info"]
    if jInf then
        local function onGetFingerPos(str)
            local x = 0
            local y = 0
            local Id = nil
            local arrInf = string.split(str, '|')
            local len = #arrInf
            if len == 2 then
                x = tonumber(arrInf[1])
                y = tonumber(arrInf[2])
            elseif len == 3 then
                Id = tonumber(arrInf[1])
                x = tonumber(arrInf[2])
                y = tonumber(arrInf[3])
            end
            return x, y, Id
        end

        local arrInf = string.split(jInf, '#')
        local lenType = #arrInf
        local anchor = nil
        local posStr = nil
        if lenType == 1 then
            posStr = arrInf[1]

        elseif lenType == 2 then
            anchor = arrInf[1]
            posStr = arrInf[2]
        end

        fingerInf["anchor"] = anchor

        local listPos = string.split(posStr, ';')
        if #listPos == 2 then
            if listPos[1] then
                local x, y, Id = onGetFingerPos(listPos[1])
                fingerInf["pos_from"] = Vector3.New(tonumber(x), tonumber(y), 0)
                fingerInf["from_id"] = Id
            end

            if listPos[2] then
                local x, y, Id = onGetFingerPos(listPos[2])
                fingerInf["pos_to"] = Vector3.New(tonumber(x), tonumber(y), 0)
                fingerInf["to_id"] = Id
            end

            fingerInf["pos_start"] = Vector3.New(0, 0, 0)
            fingerInf["start_id"] = 0
        else
            if listPos[1] then
                local x, y, Id = onGetFingerPos(listPos[1])
                fingerInf["pos_start"] = Vector3.New(tonumber(x), tonumber(y), 0)
                fingerInf["start_id"] = Id
            end
            if listPos[2] then
                local x, y, Id = onGetFingerPos(listPos[2])
                fingerInf["pos_from"] = Vector3.New(tonumber(x), tonumber(y), 0)
                fingerInf["from_id"] = Id
            end
            if listPos[3] then
                local x, y, Id = onGetFingerPos(listPos[3])
                fingerInf["pos_to"] = Vector3.New(tonumber(x), tonumber(y), 0)
                fingerInf["to_id"] = Id
            end
        end
        return fingerInf
    end
    return nil
end

function GuideConfig:ReadMainUIFlags(json)
    if not json then
        return nil
    end

    local flags = {}
    local strIterator = string.gmatch(json, "(%d+)")
    for k in strIterator do
        flags[tostring(k)] = true
    end
    return flags
end

function GuideConfig:ReadArrowInf(json)
    if not json then
        return nil
    end
    if json["arrow_pos"] and json["arrow_type"] and json["arrow_dir"] then
        local type = tonumber(json["arrow_type"])
        local dir = tonumber(json["arrow_dir"])
        local pos = self:ReadVt2(json["arrow_pos"])
		if Game.Channel_IOS_Notch and json["arrow_pos_ios"] then 
			pos = self:ReadVt2(json["arrow_pos_ios"])
		end
        local inf = {}
        inf["type"] = type
        inf["dir"] = dir
        inf["pos"] = self:ConvertPos(pos, type)
        return inf
    end
    return nil
end

function GuideConfig:ReadHintInfByJson(json)
    if not json then
        return nil
    end
    local langId = json["hint_langid"]
    if langId == nil then
        return nil
    end
    local strId = json["hint_img_id"] or ""
    local arrId = string.split(strId, '|')
    return self:ReadHintInf(langId, arrId)
end

function GuideConfig:ReadHintInf(langId, arrId)
	local inf = {}
	inf["str"] = langId
	
	for i, v in ipairs(arrId) do
	 	inf["id" .. i] = tonumber(arrId[i])
	end
	
	--local strLang = LangMgr:GetLang(langId)
    --local function GetPosVt2ByStr(str, flag)
        --if string.empty(str) then
            --return nil
        --end
        --local arrPos = string.split(str, flag)
        --if #arrPos == 2 then
            --return Vector2.New(tonumber(arrPos[1]), tonumber(arrPos[2]))
        --end
        --return nil
    --end

    --local inf = {}
    --local strInf = string.split(strLang, '|')
    --for i, v in ipairs(strInf) do
        --if i == 1 then
            --inf["str"] = v
        --else
            --local idx = i - 1
            --inf["id" .. idx] = tonumber(arrId[idx])
            --inf["pos" .. idx] = GetPosVt2ByStr(v, ',')
        --end
    --end
    return inf
end

function GuideConfig:ReadFindList(json)
    if not json then
        return nil
    end

    local list = {}
    for id in string.gmatch(json, "(%d+)") do
        table.insert(list, tonumber(id))
    end
    return list
end

function GuideConfig:ReadHeroCtrl(json)
    if not json then
        return nil
    end

    local isHas = false
    local ctrlTb = {}
    local arrSp = string.split(json, ';')
    for i, v in ipairs(arrSp) do
        local strArr = string.split(v, ',')

        local heroInf = {}
        heroInf["id"] = tonumber(strArr[1])
        heroInf["state"] = tonumber(strArr[2])
        if heroInf["state"] == 1 then
            local strParam = string.split(strArr[3], '|')
            heroInf["pos"] = Vector2.New(tonumber(strParam[1]), tonumber(strParam[2]))
        end
        table.insert(ctrlTb, heroInf)
        isHas = true
    end
    if isHas then
        return ctrlTb
    end
    return nil
end

function GuideConfig:ReadCartoon(json)
    if not json then
        return nil
    end

    local strArr = string.split(json, '|')

    local inf = {}
    inf["type"] = tonumber(strArr[1])
    if inf.type == 2 then
        local str = ""
        local endIdx = #strArr
        for i=2, endIdx do
            if i == endIdx then
                str = str .. strArr[i]
            else
                str = str .. strArr[i] .. '|'
            end
        end
        inf["talk"] = str
    else
        inf["path"] = tostring(strArr[2])

        local strParam = string.split(strArr[3], ',')
        if #strParam == 3 then
            inf["x"] = tonumber(strParam[1])
            inf["y"] = tonumber(strParam[2])
            inf["z"] = tonumber(strParam[3])
        else
            inf["pid"] = tonumber(strArr[3])
        end

        inf["duration"] = tonumber(strArr[4])
    end
    return inf
end

function GuideConfig:ReadItemNew(json)
    if not json then
        return nil
    end

    local list = {}
    local spArr = string.split(json, ';')
    for k, v in pairs(spArr) do
        local spList = string.split(v, '|')
        local len = #spList

        if len == 2 then
            table.insert(list, { ["type"] = 1, ["id"] = tonumber(spList[1]), ["id2"] = tonumber(spList[2]) })
        elseif len == 3 then
            table.insert(list, { ["type"] = 0, ["id"] = tonumber(spList[1]), ["gx"] = tonumber(spList[2]), ["gy"] = tonumber(spList[3]) })
        elseif len == 4 then
            table.insert(list, { ["type"] = 2, ["id"] = tonumber(spList[1]), ["pid"] = tonumber(spList[2]), ["gx"] = tonumber(spList[3]), ["gy"] = tonumber(spList[4]) })
        end
    end
    return list
end

function GuideConfig:ReadOpenJudge(json)
    if not json then
        return nil
    end
    local spArr = string.split(json, ';')

    return { ["type"] = tonumber(spArr[1]), ["params"] = spArr[2] }
end

function GuideConfig:ReadStepInf(json)
    if not json then
        return nil
    end

    local inf = {}
    local arrStr = string.split(json, '|')
    for i, v in pairs(arrStr) do
        if i == 1 then
            inf["typeId"] = tonumber(v)
        else
            local t = tonumber(v)
            if t == nil then
                inf["param" .. (i - 1)] = v
            else
                inf["param" .. (i - 1)] = t
            end
        end
    end
    return inf
end

function GuideConfig:ReadMask(json)
    if not json then
        return nil
    end
    local arr = string.split(json, '|')
    if #arr >= 3 then
        local type = tonumber(arr[1])

        local strRect = string.split(arr[2], ',')
        local pos = Vector2.New(tonumber(strRect[1]), tonumber(strRect[2]))
        local size = Vector2.New(tonumber(strRect[3]), tonumber(strRect[4]))
        local opacity = tonumber(arr[3])
        local isLeak = GetBoolValue(arr[4], false)

        pos = self:ConvertPos(pos, type)

        local inf = {}
        inf["pos"] = pos
        inf["size"] = size
        inf["opacity"] = opacity
        inf["isLeak"] = isLeak
        return inf
    end
    return nil
end

function GuideConfig:ReadMapLock(json)
    local inf = {}
    inf["state"] = 0
    if json then
        local arr = string.split(json, '|')
        inf["state"] = tonumber(arr[1])
        inf["passId"] = tonumber(arr[2])
    end
    return inf
end

function GuideConfig:ReadZoom(json)
    if not json then
        return nil
    end
    return tonumber(json)
end

function GuideConfig:ReadLightGrid(json)
    if not json then
        return nil
    end
    local list = {}
    local spArr = string.split(json, ';')
    for k, v in pairs(spArr) do
        local spList = string.split(v, '|')
        local len = #spList
        if len == 2 then
            table.insert(list, { gx = tonumber(spList[1]), gy = tonumber(spList[2]) })
        elseif len == 3 then
            table.insert(list, { id = tonumber(spList[1]), gx = tonumber(spList[2]), gy = tonumber(spList[3]) })
        end
    end
    return list
end

function GuideConfig:ReadOrderGrid(json)
    if not json then
        return nil
    end
    local list = {}
    local spArr = string.split(json, ';')
    for k, v in pairs(spArr) do
        local spList = string.split(v, '|')
        local len = #spList
        if len == 4 then
            table.insert(list, { gx = tonumber(spList[1]), gy = tonumber(spList[2]), orderName = spList[3], orderIdx = tonumber(spList[4]) })
        end
    end
    return list
end

function GuideConfig:ReadCommand(json)
    if not json then
        return nil
    end

    local inf = {}
    local arr = string.split(json, '@')
    for i, v in ipairs(arr) do
        local strArr = string.split(v, '#')
        local arrCmd = string.split(strArr[2], ' ')
        inf[strArr[1]] = arrCmd
    end
    return inf
end

function GuideConfig:ReadSkipInf(json)
    if not json then
        return nil
    end
    local arr = string.split(json, '|')
    if #arr >= 5 then
        local inf = {}
        local type = tonumber(arr[1])
        local anchor = tonumber(arr[2])
        local strPos = string.split(arr[3], ',')
        local skipIds = string.split(arr[4], ',')
        local uiStateId = tonumber(arr[5])
        local pos = Vector2.New(tonumber(strPos[1]), tonumber(strPos[2]))
        pos = self:ConvertPos(pos, anchor)

        inf.skipPos = pos
        inf.skipIds = skipIds
        inf.uiStateId = uiStateId
        return inf
    end
    return nil
end

function GuideConfig:GenerateData()
    self:GenWH()
    self.m_GuideInf = {}
    self.m_TriggerInf = {}

    for id, inf in pairs(self.data) do

        local nextId = GetNumberValue(inf["next"], -1)
        local guide_dot = GetNumberValue(inf["guide_dot"], 0)
        --local type = GetNumberValue(inf["guide_type"], -1)
        local jumpId = GetNumberValue(inf["jump"], -1)
        local isUITop = GetNumberValue(inf["is_ui_top"], -1)
        local delay = GetNumberValue(inf["guide_delay"], 0)
        local judge = self:ReadOpenJudge(inf["type_parameter"])

        local cameraPos = self:ReadVt2(inf["camera_pos"], true)
        local zoom = self:ReadZoom(inf["zoom"])

        local hollow = self:ReadHollow(inf["map_hollow_info"])
		local circleHollowInfo = inf["map_circle_hollow_info"]
        local mapLock = self:ReadMapLock(inf["map_lock"])
        local mapLimit = self:ReadGridLimit(inf["map_limit"])
        local tipInf = self:ReadTipInf(inf["tip_type"])
        local fingerInfo = self:ReadFingerInf(inf)
        local mainUIFlags = self:ReadMainUIFlags(inf["ui_show_kind"])
        local mainUINoRestore = GetNumberValue(inf["ui_show_restore"], 0)
        local arrowInf = self:ReadArrowInf(inf)
        local hintInf = self:ReadHintInfByJson(inf)
        local heroCtrlArr = self:ReadHeroCtrl(inf["npc_pos"])
        local findList = self:ReadFindList(inf["find_id"])
        local findArrow = GetNumberValue(inf["find_arrow_state"], -1)
        local itemNew = self:ReadItemNew(inf["create_id"])
        local stepInf = self:ReadStepInf(inf["step_type"])
        local maskInf = self:ReadMask(inf["mask"])
		if Game.Channel_IOS_Notch and inf["mask_ios"] then
			 maskInf = self:ReadMask(inf["mask_ios"])
		end
        local cartoon = self:ReadCartoon(inf["cartoon"])
        local lightUp = self:ReadLightGrid(inf["light_up"])
        local orderChange = self:ReadOrderGrid(inf["order_grid"])
        local command = self:ReadCommand(inf["command"])
        local arrowCloseDelay = tonumber(inf["arrow_close_delay"])
        local closeSplash = tonumber(inf["close_splash"])
        local skipInf = self:ReadSkipInf(inf["skip_pos"])
        local limit_lv = GetNumberValue(inf["limit_lv"], -1)
		local clear_ui = GetNumberValue(inf["clear_ui"], -1)
		local is_TinyGame = GetNumberValue(inf["is_tinygame"], -1)
		local ui_circle = self:ReadUICircle(inf["ui_circle"])
		local map_id = self:ReadMapId(inf["map_id"])
		local dynamic = self:ReadDynamic(inf["dynamic"],map_id)
        local data = {}

        data.id = id
        --data.type = type
        data.jumpId = jumpId
        data.isUITop = isUITop
        data.delay = delay
     --   data.openJudge = judge
        data.cameraPos = cameraPos
        data.cartoon = cartoon
        data.zoom = zoom

        data.hollow = hollow
		data.circleHollowInfo = circleHollowInfo
        data.mapLock = mapLock
        data.mapLimit = mapLimit
        data.tipInf = tipInf
        data.fingerInfo = fingerInfo
        data.mainUIFlags = mainUIFlags
        data.mainUINoRestore = mainUINoRestore
        data.arrowInf = arrowInf
        data.hintInf = hintInf
        data.heroCtrlArr = heroCtrlArr
        data.findList = findList
        data.findArrow = findArrow
        data.itemNew = itemNew
        data.lightUp = lightUp
        data.orderChange = orderChange
        data.maskInf = maskInf
        data.closeSplashType = closeSplash

        data.nextId = nextId
        data.stepInf = stepInf
        data.dot = guide_dot
        data.arrowCloseDelay = arrowCloseDelay
        data.command = command
        data.skipInf = skipInf
        data.limitLv = limit_lv
		data.ClearUI = clear_ui
		data.isTinyGame = is_TinyGame
		data.ui_circle = ui_circle
		data.not_max = inf["not_max"]
		data.map_id = map_id
		data.no_view = inf["no_view"]
		data.dynamic = dynamic
        self.m_GuideInf[id] = data
		
        if judge then
            local jType = tostring(judge.type)
            local triggerInf = self.m_TriggerInf[jType]
            if not triggerInf then
                triggerInf = {}
            end
            triggerInf[tostring(judge.params)] = id
            self.m_TriggerInf[jType] = triggerInf
        end
    end
end

function GuideConfig:GetGuideInfByID(id)
    return self.m_GuideInf[id]
end

function GuideConfig:GetGuideTriggerMap(type)
    return self.m_TriggerInf[type]
end

function GuideConfig:ReadUICircle(json)
	if not json then
		return nil
	end
	local inf = {}
	local strArr = string.split(json, '|')
	inf["x"] = tonumber(strArr[1])
	inf["y"] = tonumber(strArr[2])
	inf["type"] = tonumber(strArr[3])
	inf["slider"] = tonumber(strArr[4])
	if inf["type"] == 2 then
		inf["slider2"] = tonumber(strArr[5])
	end
	return inf
end

function GuideConfig:ReadMapId(json)
	local p = {}
	if json then
		local param = Split1(json,"|")
		for index, value in ipairs(param) do
			table.insert(p,v2n(value))
		end
	end
	return p
end

function GuideConfig:ReadDynamic(json,mapIds)
	local p = {}
	if json then
		local param = Split1(json,";")
		for index, value in ipairs(mapIds) do
			local mapId =v2s(mapIds[index])
			if param[index] then
				p[mapId] = param[index]
			else
				p[mapId] = param[1]
			end
		end
	end
	return p
end

return GuideConfig