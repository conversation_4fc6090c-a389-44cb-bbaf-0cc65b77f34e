﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using AssetBundles;
using System;
using XLua;
using System.Text;

public class StorageManager : MonoBehaviour
{
    private static StorageManager mInstance = null;
    public static StorageManager Instance
    {
        get
        {
            if (mInstance == null)
            {
                mInstance = GameObject.FindObjectOfType(typeof(StorageManager)) as StorageManager;
                if (mInstance == null)
                {
                    GameObject go = new GameObject(typeof(StorageManager).Name);
                    mInstance = go.AddComponent<StorageManager>();
                    GameObject parent = GameObject.Find("Managers");
                    if (parent == null)
                    {
                        parent = new GameObject("Managers");
                        GameObject.DontDestroyOnLoad(parent);
                    }
                    if (parent != null)
                    {
                        go.transform.parent = parent.transform;
                    }
                }
            }
            return mInstance;
        }
    }

    public string Path_GameData = null;

    KVTextTool s_Info = null;

    public void Init()
    {
        Path_GameData = GameHelper.persistentDataPath + "/GameData/";

        FileUtility.CheckDirAndCreateWhenNeeded(Path_GameData);

        InitF();

        LogMan.Info("****** StorageManager:" + Path_GameData);
    }

    public void ClearOutDatedFiles(KVTextTool txt)
    {
        List<string> keys = txt.GetKeyListNew();
        if (keys.Count > 0)
        {
            string[] allFiles = FileUtility.GetAllFilesInFolder(Path_GameData);
            for (int i = 0; i < allFiles.Length; i++)
            {
                string path = allFiles[i];
                string name = FileUtility.GetFileName(path);
                if (name == "info.bytes")
                {
                    continue;
                }
                else
                {
                    for (int j = 0; j < keys.Count; j++)
                    {
                        string k = keys[j];
                        if (name.Contains(k))
                        {
                            string timeStamp = txt.GetValue(k);
                            if (!name.Contains(k + timeStamp))
                            {
                                FileUtility.SafeDeleteFile(path);
                                LogMan.Warning("______del outdated data: " + path);
                            }
                        }
                    }
                }
            }
        }
    }

    //info.bytes
    public void InitF()
    {
        string strInf = LoadGameData("info");
        if (!string.IsNullOrEmpty(strInf))
        {
            int md5Len = 32;
            if (strInf.Length > md5Len)
            {
                int idxStart = md5Len + 1;
                string md5 = strInf.Substring(0, md5Len);
                string strText = strInf.Substring(idxStart, strInf.Length - idxStart);
                if (md5.Equals(Rijndael.MD5String(strText)))
                {
                    s_Info = new KVTextTool(strText);
                }
                else
                {
                    LogMan.Error("###### GameInfo md5 err");
                }
            }
            else
            {
                LogMan.Error("###### GameInfo char err");
            }
        }

        if (s_Info == null)
        {
            KVTextTool txt = new KVTextTool();
            s_Info = txt;
        }
        // ClearOutDatedFiles(txt);
    }

    public string GetStringF(string key)
    {
        lock (s_Info)
        {
            return s_Info.GetValue(key);
        }
    }

    public void SetStringF(string key, string value)
    {
        lock (s_Info)
        {
            s_Info.UpdateKV(key, value);
        }
    }

    public void SaveF()
    {
        lock (s_Info)
        {
            string str = s_Info.GetPrettyText();
            string md5 = Rijndael.MD5String(str);
            SaveGameData("info", md5 + "\n" + str);
        }
    }

    public string ChangeSaveLastName(string key)
    {
        string n = "0";
        string t = GetStringF(key);
        if (!string.IsNullOrEmpty(t))
        {
            if (t.Equals("0"))
                n = "1";
            else
                n = "0";
        }
        SetStringF(key, n);

        return n;
    }


    //game data
    public bool SaveGameDataSafe(string key, string data)
    {
        string lastName = ChangeSaveLastName(key + "_last");
        return SaveGameData(key + "_" + lastName, data);
    }
    public string LoadGameDataSafe(string key)
    {
        string strTime0 = GetStringF(key);
        if (string.IsNullOrEmpty(strTime0))
        {
            return null;
        }
        string lastName = GetStringF(key + "_last");
        string data = LoadGameData(key + "_" + lastName);

        return data;
    }

    public string GetSecretStr(string data, bool isEncrypt)
    {
#if UNITY_EDITOR
        return data;
#else
        if (isEncrypt)
            return Rijndael.Encrypt(data);
        else
            return Rijndael.Decrypt(data);
#endif
    }

    public bool SaveGameData(string key, string data)
    {
        string path = Path_GameData + key + ".bytes";

        if (FileUtility.SafeWriteAllText(path, GetSecretStr(data, true)))
        {
            return true;
        }
        return false;
    }

    public string LoadGameData(string key)
    {
        string path = Path_GameData + key + ".bytes";

        return GetSecretStr(FileUtility.SafeReadAllText(path), false);
    }

    public void ClearGameData()
    {
        FileUtility.SafeDeleteDir(Path_GameData);
        FileUtility.CheckDirAndCreateWhenNeeded(Path_GameData);
        PlayerPrefs.DeleteAll();

        KVTextTool txt = new KVTextTool();
        s_Info = txt;
    }

    //save game
    public void SetStorage(LuaTable tb, long t, int isAsync)
    {
        if (isAsync != 0)
        {
            ThreadWorker.ThreadJob job = new ThreadWorker.ThreadJob();
            job.param0 = tb;
            job.param1 = t;

            job.param3 = new Dictionary<string, string>();

            StringBuilder sb = new StringBuilder();
            tb.ForEach<string, string>((key, json) =>
            {
                sb.Append(key);
                sb.Append("|");
                job.param3.Add(key, json);
            });

            job.param2 = sb.ToString();
            job.CallBack = (jobNow) =>
            {
                bool isCanSaveData = true;

                LuaTable table = (LuaTable)jobNow.param0;
                string strTimeStamp = ((long)jobNow.param1).ToString();

                Dictionary<string, string> tParam = new Dictionary<string, string>();

                tParam["opt"] = "3";
                foreach (KeyValuePair<string, string> keyValue in jobNow.param3)
                {
                    string key = keyValue.Key;
                    string json = keyValue.Value;
                    tParam[key] = json.EscapeStr();
                    if (SaveGameDataSafe(key, json))
                    {
                        SetStringF(key, strTimeStamp);
                    }
                    else
                    {
                        isCanSaveData = false;
                    }
                }
                tParam["dataId"] = (string)jobNow.param2;
                System.Action<int, long, string, object> callback = (state, pid, result, ext) =>
                            {
                                //Debug.LogError("存档已上传发送服务器！！！！！！！！！！");
                                if (state == 2)
                                {
                                    LitJson.JsonData jd = LitJson.JsonMapper.ToObject(result);
                                    if (jd.ContainsKey("info"))
                                    {
                                        if (jd["info"].ContainsKey("result"))
                                        {
                                            string res = jd["info"]["result"].ToString();
                                            if (res == "3")
                                            {
                                                LuaManager.Instance.CallLuaDoString("DebugConsole(\"restart\", 0, 0)");
                                            }
                                            else if (res == "5")
                                            {
#if UNITY_EDITOR
                                                table.ForEach<string, string>((key, json) =>
                                                {
                                                    LogMan.Error(key);
                                                    LogMan.Error(json);
                                                    LogMan.Error(json.EscapeStr());
                                                });
#endif
                                            }
                                        }
                                    }
                                }
                            };
                if (isAsync == 1)
                {
                    // Debug.LogError("開始存档已上传发送服务器！！！！！！！！！！");
                    // foreach (KeyValuePair<string, string> keyValue in jobNow.param3)
                    // {
                    //     string key = keyValue.Key;
                    //     string json = keyValue.Value;
                    //     FileUtility.SafeWriteAllText($"Assets/Resources/Config/{key}.txt", json);
                    // }
                    HttpMono.Instance.SendToGS(40000, "/player/updateplayer", tParam, callback);
                }

                if (isCanSaveData)
                {
                    SaveF();
                    LogMan.Info("****** Save Done");
                }
                else
                {
                    LogMan.Error("###### Save fail");
                    InitF();
                }
            };
            ThreadWorker.Instance.AddJob(job);
        }
        else
        {
            bool isCanSaveData = true;
            string strTimeStamp = t.ToString();

            tb.ForEach<string, string>((key, json) =>
            {
                if (SaveGameDataSafe(key + "local", json))
                {
                    SetStringF(key + "local", strTimeStamp);
                }
                else
                {
                    isCanSaveData = false;
                }
            });

            if (isCanSaveData)
            {
                SaveF();
                LogMan.Info("****** Save local Done");
            }
            else
            {
                LogMan.Error("###### Save local fail");
                InitF();
            }
        }
    }
}
