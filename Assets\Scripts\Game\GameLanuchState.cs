﻿using AssetBundles;
using LitJson;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.Networking;
using UnityEngine.UI;

public class GameLanuchState : MonoBehaviour
{
        //Create UI From OutSide
        public static string DEF_STR_PREFAB_LOGO = "Prefabs/UI_SplashLogo";
        public static GameObject CreateUILogo()
        {
                GameObject prefab = Resources.Load<GameObject>(DEF_STR_PREFAB_LOGO);
                return GameObject.Instantiate(prefab);
        }
        public static string DEF_STR_PREFAB_LOGOJapan = "Prefabs/UI_SplashJapan";
        public static GameObject CreateUIJapan()
        {
                GameObject prefab = Resources.Load<GameObject>(DEF_STR_PREFAB_LOGOJapan);
                return GameObject.Instantiate(prefab);
        }
        public static string DEF_STR_PREFAB = "Prefabs/UI_VersionUpdate";
        public static GameLanuchState CreateUI()
        {
                GameObject prefab = Resources.Load<GameObject>(DEF_STR_PREFAB);
                GameObject go = GameObject.Instantiate(prefab);
                return go.GetComponent<GameLanuchState>();
        }

        public Slider m_ImgPro2 = null;
        public Image m_ImgPro = null;

        public Text m_TxtDes = null;
        public Image m_Slider = null;
        public Text m_TxtVer = null;

        //process
        public enum EM_PROCESS_TYPE
        {
                NOTHING = 0,
                APP_VER_LOCAL,
                RES_VER_LOCAL,
                APP_VER_SERVER,
                RES_VER_SERVER,
                LOADING_GAME,
                RES_LIST_SERVER,
                RES_LIST_LOCAL,
                DOWNLOAD_FILES,
                SAVE_VER,
                START_GAME,
                RES_PATH_SERVER,
                APP_VER_SERVER_NEXT,

        };
        public EM_PROCESS_TYPE m_ProcessNow = 0;
        public string GetFTPPathServer()
        {
                return GameVersion.FTP_URL + "/" + m_StrAppVerServer + "/" + StartGame.Instance.GetGroup() + "/" + m_StrResVerPathServer + "/" + AssetBundles.Utility.GetPlatformName();
        }
        public string GetFTPPath()
        {
                return GameVersion.FTP_URL + "/" + m_StrAppVerLocal + "/" + StartGame.Instance.GetGroup() + "/" + m_StrResVerPathServer + "/" + AssetBundles.Utility.GetPlatformName();
        }
        public string GetFTPRootPath()
        {
                return GameVersion.FTP_URL + "/" + m_StrAppVerServer + "/" + StartGame.Instance.GetGroup();
        }
        public string GetVersionSavePath()
        {
                return AssetBundleConfig.GetPersistentDataPath;
        }

        private string m_StrAppVerServer = null;
        private string m_StrResVerServer = null;

        private string m_StrAppVerLocal = null;
        private string m_StrResVerLocal = null;

        private string m_StrFileListServer = null;
        private string m_StrFileListLocal = null;

        private string m_StrResVerPathServer = "vres1";

        private int m_UpdateFailCount = 0;
        void Start()
        {

                if (!StartGame.Instance.IsUseNet)
                {
                        Debug.Log("-- 去掉热更新判断");
                        StartProcess(EM_PROCESS_TYPE.LOADING_GAME);
                        return;
                }

                if (AssetManager.Instance.IsLocalMode())
                {
                        StartProcess(EM_PROCESS_TYPE.LOADING_GAME);
                }
                else
                {
                        //TODO 加载
                        //  StartProcess(EM_PROCESS_TYPE.LOADING_GAME);

                        StartProcess(EM_PROCESS_TYPE.APP_VER_LOCAL);
                }
        }

        void Update()
        {
                if (m_ProcessNow == EM_PROCESS_TYPE.DOWNLOAD_FILES)
                {
                        System.Tuple<int, int, float, string> tup = GameLanuchResource.Instance.GetProgress();
                        int pre = (int)(tup.Item3 * 100);
                        SetUIProgress(m_ProcessNow, pre, string.Format("Downloading...   {0}%   {1}   [{2}/{3}]", pre, tup.Item4, tup.Item1, tup.Item2));
                }

                StartGame.Instance.SetUIVer(m_TxtVer);
        }


        void ShowUINotice(EM_PROCESS_TYPE type, int state, object param = null,Action<int> action = null)
        {
                int langDes = 0;
                int langLeft = 1020;
                int langRight = 1021;
                UnityAction tFunLeft = null;
                UnityAction tFunRight = null;
                switch (type)
                {
                        //             case EM_PROCESS_TYPE.APP_VER_LOCAL:
                        //                 langDes = 1010;//Not happen
                        //                 break;
                        //             case EM_PROCESS_TYPE.RES_VER_LOCAL:
                        //                 langDes = 1011;//Not happen
                        //                 break;
                        case EM_PROCESS_TYPE.APP_VER_SERVER:
                                langDes = 1012;
                                if (state == 0)
                                {
                                        langDes = 1017;
                                        langLeft = 1018;
                                        langRight = 0;
                                        tFunRight = new UnityAction(() =>
                                        {
                                                Application.Quit();
                                        });
                                        tFunLeft = new UnityAction(() =>
                                        {
                                                Application.OpenURL((string)param);
                                                //Application.Quit();
                                        });
                                }
                                break;
                        case EM_PROCESS_TYPE.APP_VER_SERVER_NEXT:
                                langDes = 1012;
                               if(state == 1)
                                {
                                        langDes = 1031;
                                        langLeft = 1032;
                                        langRight = 1033;
                                        tFunRight = new UnityAction(() =>
                                        {
                                                 StartGame.Instance.CloseMsgBox();
                                                 if(action != null)
                                                        action(1);
                                        });
                                        tFunLeft = new UnityAction(() =>
                                        {
                                                Application.OpenURL((string)param);
                                                //Application.Quit();
                                        });
                                }
                                break;
                        case EM_PROCESS_TYPE.RES_VER_SERVER:
                                langDes = 1013;
                                break;
                        case EM_PROCESS_TYPE.RES_LIST_SERVER:
                                langDes = 1014;
                                break;
                        case EM_PROCESS_TYPE.RES_LIST_LOCAL:
                                langDes = 1015;
                                break;
                        case EM_PROCESS_TYPE.DOWNLOAD_FILES:
                                langDes = 1016;
                                break;
                }
                if (state == -1)
                {
                        tFunRight = new UnityAction(() =>
                        {
                                StartProcess(type);
                                StartGame.Instance.CloseMsgBox();
                        });
                        tFunLeft = new UnityAction(() =>
                        {
                                Application.Quit();
                        });
                }
                StartGame.Instance.ShowMsgBox(LanguageManager.Instance.GetLang(langDes), langLeft, langRight, tFunLeft, tFunRight);
        }

        public void SetUIProgress(EM_PROCESS_TYPE type, int progress, object param = null)
        {
                int langDes = 0;
                switch (type)
                {
                        case EM_PROCESS_TYPE.APP_VER_LOCAL:
                                langDes = 1000;
                                break;
                        case EM_PROCESS_TYPE.RES_VER_LOCAL:
                                langDes = 1001;
                                break;
                        case EM_PROCESS_TYPE.APP_VER_SERVER:
                                langDes = 1002;
                                break;
                        case EM_PROCESS_TYPE.RES_VER_SERVER:
                                langDes = 1003;
                                break;
                        case EM_PROCESS_TYPE.LOADING_GAME:
                                langDes = 1004;
                                break;
                        case EM_PROCESS_TYPE.RES_LIST_SERVER:
                                langDes = 1005;
                                break;
                        case EM_PROCESS_TYPE.RES_LIST_LOCAL:
                                langDes = 1006;
                                break;
                        case EM_PROCESS_TYPE.DOWNLOAD_FILES:
                                langDes = 1007;
                                break;
                        case EM_PROCESS_TYPE.SAVE_VER:
                                langDes = 1008;
                                break;
                        case EM_PROCESS_TYPE.START_GAME:
                                langDes = 1009;
                                break;
                        case EM_PROCESS_TYPE.RES_PATH_SERVER:
                                langDes = 1002;
                                 break;
                        case EM_PROCESS_TYPE.APP_VER_SERVER_NEXT:
                                langDes = 1002;
                                break;
                }
                m_ImgPro2.value = (float)progress / 100.0f;
                //m_ImgPro.fillAmount = (float)progress / 100.0f;

                // m_Slider.rectTransform.anchoredPosition = new Vector2(m_ImgPro2.value * m_ImgPro.rectTransform.sizeDelta.x, m_Slider.rectTransform.anchoredPosition.y);
                if (param == null)
                        m_TxtDes.text = LanguageManager.Instance.GetLang(langDes);
                else
                        m_TxtDes.text = (string)param;
        }

        private void StartProcess(EM_PROCESS_TYPE process)
        {
                m_ProcessNow = process;
                StartCoroutine(StartUpdateVersion(process));
        }

        /// <summary>
        /// 强更删除旧包的热更资源，通过比对版本号
        /// </summary>
        private void ClearOldABResource()
        {
                string str = FileUtility.SafeReadAllText(GameHelper.persistentDataPath + "/test_ver.bytes");
                if (string.IsNullOrEmpty(str) || string.Compare(str, Application.version) != 0)
                {
                        FileUtility.SafeDeleteDir(GetVersionSavePath());
                        LogMan.Info("****** Delete Dir:" + GetVersionSavePath());

                        FileUtility.SafeWriteAllText(GameHelper.persistentDataPath + "/test_ver.bytes", Application.version);
                        LogMan.Info("****** Version Test: Clear");
                }
                else
                {
                        LogMan.Info("****** Version Test: Ignore" + GetVersionSavePath());
                }
        }

        public IEnumerator StartUpdateVersion(EM_PROCESS_TYPE process)
        {
                switch (process)
                {
                        case EM_PROCESS_TYPE.APP_VER_LOCAL:
                                {
                                        SetUIProgress(process, 5);
                                        yield return new WaitForSeconds(0.1f);

                                        ClearOldABResource();

                                        SetUIProgress(process, 50);
                                        yield return new WaitForSeconds(0.5f);


                                        int pathType = 0;
                                        string url = AssetBundleConfig.GetLocalVersionFilePath(AssetBundleConfig.appVerName, out pathType);
                                        yield return HttpMono.Instance.GetFile(url, pathType, (int error, long code, string text) =>
                                        {
                                                SetUIProgress(process, 100);
                                                if (error == 0)
                                                {
                                                        m_StrAppVerLocal = Rijndael.Decrypt(text);
                                                }
                                                else
                                                {
                                                        m_StrAppVerLocal = Application.version;
                                                        LogMan.Debug("###### there hasn't ver-app file");
                                                }
                                                StartGame.Instance.SetVer(m_StrAppVerLocal, null);
                                                StartProcess(EM_PROCESS_TYPE.RES_VER_LOCAL);
                                        });
                                }
                                break;
                        case EM_PROCESS_TYPE.RES_VER_LOCAL:
                                {
                                        SetUIProgress(process, 20);
                                        int pathType = 0;
                                        string url = AssetBundleConfig.GetLocalVersionFilePath(AssetBundleConfig.resVerName, out pathType);
                                        yield return HttpMono.Instance.GetFile(url, pathType, (int error, long code, string text) =>
                                        {
                                                SetUIProgress(process, 100);
                                                if (error == 0)
                                                {
                                                        m_StrResVerLocal = Rijndael.Decrypt(text);
                                                }
                                                else
                                                {
                                                        m_StrResVerLocal = "0";
                                                        LogMan.Debug("###### there hasn't ver-res file : " + text);
                                                }
                                                StartGame.Instance.SetVer(null, m_StrResVerLocal);
                                                StartProcess(EM_PROCESS_TYPE.APP_VER_SERVER);
                                        });
                                }
                                break;
                        case EM_PROCESS_TYPE.APP_VER_SERVER:
                                {
                                        SetUIProgress(process, 20);
                                        //根据本地app版本获取服务器的app版本号m_StrAppVerServer,
                                        //后续根据服务器的app版本号做热更新资源，读取路径是vres1。
                                        string url = GetFTPPath() + "/" + AssetBundleConfig.appVerName;
                                        //url = url + string.Format("?t={0}", UnityEngine.Random.Range(100000, 1000000));
                                        System.Action<int, long, string> httpCall = (error, code, text2) =>
                                        {
                                                SetUIProgress(process, 100);
                                                if (error == 0)
                                                {
                                                        string text = Rijndael.Decrypt(text2);
                                                        string STR_KEY_OPEN_URL = "OpenUrl:";
                                                        string STR_KEY_CLOSE_NOTICE = "CloseNotice:";
                                                        if (text.StartsWith(STR_KEY_OPEN_URL))
                                                        {
                                                                ShowUINotice(process, 0, text.Substring(STR_KEY_OPEN_URL.Length));
                                                        }
                                                        else if (text.StartsWith(STR_KEY_CLOSE_NOTICE))
                                                        {
                                                                SetUIProgress(process, 100, text.Substring(STR_KEY_CLOSE_NOTICE.Length));
                                                        }
                                                        else
                                                        {
                                                                m_StrAppVerServer = text;
                                                                StartProcess(EM_PROCESS_TYPE.APP_VER_SERVER_NEXT);  
                                                        }
                                                }
                                                else
                                                {
                                                        ShowUINotice(process, -1);
                                                }
                                        };

                                        yield return HttpMono.Instance.Get(url, httpCall);
                                }
                                break;
                                case EM_PROCESS_TYPE.APP_VER_SERVER_NEXT:
                                {  
                                        string url = GetFTPPath() + "/" + AssetBundleConfig.appVerNextName;
                                        //url = url + string.Format("?t={0}", UnityEngine.Random.Range(100000, 1000000));
                                        System.Action<int, long, string> httpCall = (error, code, text3) =>
                                        {
                                                SetUIProgress(process, 100);
                                                if (error == 0)
                                                {
                                                        string STR_KEY_OPEN_URL2 = "OpenUrl:";
                                                        string text = Rijndael.Decrypt(text3);
                                                        if (text != null  && text.StartsWith(STR_KEY_OPEN_URL2))
                                                        {
                                                                ShowUINotice(process, 1, text.Substring(STR_KEY_OPEN_URL2.Length),(reslue)=>{
                                                                        StartProcess(EM_PROCESS_TYPE.RES_PATH_SERVER);  
                                                                });
                                                        }
                                                        else
                                                        {
                                                                StartProcess(EM_PROCESS_TYPE.RES_PATH_SERVER);
                                                        }    
                                                }
                                                else
                                                {
                                                        StartProcess(EM_PROCESS_TYPE.RES_PATH_SERVER);
                                                }
                                        };
                                                
                                        yield return HttpMono.Instance.Get(url, httpCall);
                                }
                                break;
                        case EM_PROCESS_TYPE.RES_PATH_SERVER:
                                {
                                        SetUIProgress(process, 20);
                                        string url = GetFTPRootPath() + "/" + AssetBundleConfig.vresPath;
                                        //url = url + string.Format("?t={0}", UnityEngine.Random.Range(100000, 1000000));
                                        System.Action<int, long, string> httpCall = (error, code, text2) =>
                                        {
                                                SetUIProgress(process, 100);
                                                if (error == 0)
                                                {
                                                        m_StrResVerPathServer = text2;
                                                        StartProcess(EM_PROCESS_TYPE.RES_VER_SERVER);
                                                }
                                                else
                                                {
                                                        StartProcess(EM_PROCESS_TYPE.RES_VER_SERVER);
                                                }
                                        };

                                        yield return HttpMono.Instance.Get(url, httpCall);
                                }
                                break;
                        case EM_PROCESS_TYPE.RES_VER_SERVER:
                                {
                                        SetUIProgress(process, 20);
                                        string url = GetFTPPathServer() + "/" + AssetBundleConfig.resVerName;
                                        // url = url + string.Format("?t={0}", UnityEngine.Random.Range(100000, 1000000));
                                        yield return HttpMono.Instance.Get(url, (int error, long code, string text) =>
                                        {
                                                SetUIProgress(process, 100);
                                                if (error == 0)
                                                {
                                                        m_StrResVerServer = Rijndael.Decrypt(text);
                                                        if (CheckVersionIsNeedUpdate(m_StrResVerServer, m_StrResVerLocal))
                                                        {
                                                                StartProcess(EM_PROCESS_TYPE.RES_LIST_SERVER);
                                                        }
                                                        else
                                                        {
                                                                StartProcess(EM_PROCESS_TYPE.LOADING_GAME);
                                                        }
                                                }
                                                else
                                                {
                                                        ShowUINotice(process, -1);
                                                }
                                        });
                                        break;
                                }
                        case EM_PROCESS_TYPE.LOADING_GAME:
                                {
                                        LoadServerSwitch();

                                        SetUIProgress(process, 0);
                                        GameSilentResource.Instance.SetData(GetFTPPathServer());
                                        AssetManager.Instance.Cleanup();
                                        yield return AssetManager.Instance.InitABManager();

                                        SetUIProgress(process, 5);

                                        yield return AssetManager.Instance.PreLoadAssetBundle(AssetBundleConfig.LuaABName);
 
                                        SetUIProgress(process, 10);

                                        yield return AssetManager.Instance.PreLoadAssetBundle("shader");

                                        SetUIProgress(process, 15);

                                        Shader.WarmupAllShaders();

                                        SetUIProgress(process, 20);

                                        // yield return AssetManager.Instance.PreLoadAssetBundle(AssetBundleConfig.ConfigName);
                                        yield return AssetManager.Instance.PreLoadAssetBundle("config");

                                        SetUIProgress(process, 30);

                                        yield return LuaManager.Instance.Initialize();

                                        SetUIProgress(process, 100);

                                        yield return null;

                                        StartProcess(EM_PROCESS_TYPE.START_GAME);
                                        yield return null;
                                }
                                break;
                        case EM_PROCESS_TYPE.RES_LIST_SERVER:
                                {
                                        SetUIProgress(process, 20);
                                        string url = GetFTPPathServer() + "/" + AssetBundleConfig.fileListName;
                                        yield return HttpMono.Instance.Get(url, (int error, long code, string text) =>
                                        {
                                                SetUIProgress(process, 100);
                                                if (error == 0)
                                                {
                                                        m_StrFileListServer = Rijndael.Decrypt(text);
                                                        StartProcess(EM_PROCESS_TYPE.RES_LIST_LOCAL);

                                                }
                                                else
                                                {
                                                        ShowUINotice(process, -1);
                                                }
                                        });
                                }
                                break;
                        case EM_PROCESS_TYPE.RES_LIST_LOCAL:
                                {
                                        SetUIProgress(process, 20);

                                        int pathType = 0;
                                        string url = AssetBundleConfig.GetLocalVersionFilePath(AssetBundleConfig.fileListName, out pathType);
                                        yield return HttpMono.Instance.GetFile(url, pathType, (int error, long code, string text) =>
                                        {
                                                SetUIProgress(process, 100);
                                                if (error == 0)
                                                {
                                                        m_StrFileListLocal = Rijndael.Decrypt(text);
                                                }
                                                else
                                                {
                                                        m_StrFileListLocal = "";
                                                }
                                                StartProcess(EM_PROCESS_TYPE.DOWNLOAD_FILES);
                                        });
                                }
                                break;
                        case EM_PROCESS_TYPE.DOWNLOAD_FILES:
                                {
                                        System.Action tFunWin = () =>
                                        {
                                                StartProcess(EM_PROCESS_TYPE.SAVE_VER);
                                        };

                                        // ShowUINotice(process, -1);
                                        System.Action<int, int> tFunLose = (dFailCount, wFailCount) =>
                                      {
                                              if (wFailCount > 0)
                                              {//clear
                                                      GameLanuchResource.Instance.ClearFileDownloaded();
                                              }
                                              if (m_UpdateFailCount > 10)
                                              {
                                                      ShowUINotice(EM_PROCESS_TYPE.RES_LIST_LOCAL, -1);
                                                      m_UpdateFailCount = 0;
                                              }
                                              else
                                              {
                                                      m_UpdateFailCount++;
                                                      StartProcess(EM_PROCESS_TYPE.RES_LIST_LOCAL);
                                              }
                                              //ShowUINotice(EM_PROCESS_TYPE.RES_LIST_LOCAL, -1);
                                      };

                                        GameLanuchResource.Instance.InitDownloader(GetFTPPathServer(), GetVersionSavePath(), tFunWin, tFunLose);
                                        GameLanuchResource.Instance.SetKVData(new KVTextTool(m_StrFileListLocal), true);
                                        GameLanuchResource.Instance.SetKVData(new KVTextTool(m_StrFileListServer), false);
                                        GameLanuchResource.Instance.StartDownload();
                                }
                                break;
                        case EM_PROCESS_TYPE.SAVE_VER:
                                {
                                        SetUIProgress(process, 20);

                                        GameLanuchResource.Instance.SaveLocalVersion(AssetBundleConfig.fileListName, true);

                                        string resPath = GetVersionSavePath() + "/" + AssetBundleConfig.resVerName;
                                        Debug.Log("save local ver: " + resPath);
                                        FileUtility.SafeWriteAllText(resPath, Rijndael.Encrypt(m_StrResVerServer));
                                        StartGame.Instance.SetVer(null, m_StrResVerServer);

                                        yield return null;
                                        SetUIProgress(process, 100);
                                        yield return null;
                                        StartProcess(EM_PROCESS_TYPE.LOADING_GAME);
                                }
                                break;
                        case EM_PROCESS_TYPE.START_GAME:
                                {
                                        while (StartGame.Instance.m_GoNotice)
                                        {
                                            yield return new WaitForSeconds(1f);
                                        }
                                        LogMan.Info("------ Lua Start");
                                        GameAniManager.Instance.Init();
                                        LuaManager.Instance.StartLua();
                                }
                                break;
                        default:
                                break;
                }
        }

        bool CheckVersionIsNeedUpdate(string serverVer, string localVer)
        {
                if (string.IsNullOrEmpty(serverVer))
                        return false;

                if (serverVer.IndexOf('.') == -1)
                {
                        long.TryParse(serverVer, out long serverTime);
                        long.TryParse(localVer, out long localTime);
                        return serverTime > localTime;
                }

                string[] server = serverVer.Split('.');
                string[] local = localVer.Split('.');

                if (server.Length >= 3 && local.Length >= 3)
                {
                        int serverVer1 = int.Parse(server[0]);
                        int localVer1 = int.Parse(local[0]);
                        if (serverVer1 > localVer1)
                                return true;

                        int serverVer2 = int.Parse(server[1]);
                        int localVer2 = int.Parse(local[1]);
                        if (serverVer2 > localVer2)
                                return true;

                        int serverVer3 = int.Parse(server[2]);
                        int localVer3 = int.Parse(local[2]);
                        if (serverVer3 > localVer3)
                                return true;
                }

                return false;
        }

        private void LoadServerSwitch() {
            string url = GetFTPPathServer() + "/" + AssetBundleConfig.switchPath;
        
            HttpMono.Instance.HTTPGet(url, (int error, long code, string text) =>
            {
                if (error == 0)
                {
                    StartGame.Instance.SetServerSwitch(text);
                }
            });

        }
}
