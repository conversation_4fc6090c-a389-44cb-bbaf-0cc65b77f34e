using System.Collections.Generic;
using System;
using UnityEngine;
using XLua;
using UnityEngine.EventSystems;
using DeletegateCall;
using Mosframe;
using Spine.Unity;
using bc.MiniGameBase;
using System.Reflection;

public static class GenConfig
{
        //lua中要使用到C#库的配置，比如C#标准库，或者Unity API，第三方库等。
        [LuaCallCSharp]
        public static List<Type> LuaCallCSharp = new List<Type>() {
        typeof(Coffee.UIEffects.UIShadow),
        typeof(TopTrigger),
        
		// unity
		typeof(System.Object),
        typeof(UnityEngine.Object),
        typeof(Animation),
        typeof(AnimationState),
        typeof(Animator),
        typeof(Ray2D),
        typeof(GameObject),
        typeof(Component),
        typeof(Behaviour),
        typeof(Transform),
        typeof(Resources),
        typeof(TextAsset),
        typeof(Keyframe),
        typeof(AnimationCurve),
        typeof(AnimationClip),
        typeof(MonoBehaviour),
        typeof(ParticleSystem),
        typeof(SkinnedMeshRenderer),
        typeof(Renderer),
        typeof(WWW),
        typeof(RuntimePlatform),
        typeof(Application),
        //typeof(WWW),
        typeof(List<int>),
        typeof(Action<string>),
        typeof(Action<int, long, string>),
        typeof(Action<int, long, int, string>),
        typeof(Action<int, long, string, object>),
        typeof(Dictionary<string, string>),
        typeof(UnityEngine.Debug),
        typeof(Delegate),
        typeof(Dictionary<string, GameObject>),
        typeof(UnityEngine.Events.UnityEvent),
        typeof(CanvasGroup),
        typeof(Bounds),
        typeof(Color),
        typeof(LayerMask),
        typeof(Mathf),
        typeof(Plane),
        typeof(Quaternion),
        typeof(Ray),
        typeof(RaycastHit),
        typeof(Time),
        typeof(Touch),
        typeof(TouchPhase),
        typeof(Vector2),
        typeof(Vector3),
        typeof(Vector4),
       // typeof(AnimationCurve), repeat
        typeof(GameAniManager),
        typeof(AnimationCurves),

        // 渲染
        typeof(RenderMode),
        
        // UGUI  
        typeof(UnityEngine.Canvas),
        typeof(UnityEngine.Rect),
        typeof(UnityEngine.RectTransform),
        typeof(UnityEngine.RectOffset),
        typeof(UnityEngine.Sprite),
        typeof(UnityEngine.UI.CanvasScaler),
        typeof(UnityEngine.UI.CanvasScaler.ScaleMode),
        typeof(UnityEngine.UI.CanvasScaler.ScreenMatchMode),
        typeof(UnityEngine.UI.GraphicRaycaster),
        typeof(UnityEngine.UI.Text),
        typeof(UnityEngine.UI.InputField),
        typeof(UnityEngine.UI.Button),
        typeof(UnityEngine.UI.Image),
        typeof(UnityEngine.UI.RawImage),
        typeof(UnityEngine.UI.ScrollRect),
        typeof(UnityEngine.UI.Scrollbar),
        typeof(UnityEngine.UI.Toggle),
        typeof(UnityEngine.UI.ToggleGroup),
        typeof(UnityEngine.UI.Button.ButtonClickedEvent),
        typeof(UnityEngine.UI.ScrollRect.ScrollRectEvent),
        typeof(UnityEngine.UI.GridLayoutGroup),
        typeof(UnityEngine.UI.ContentSizeFitter),
        typeof(UnityEngine.UI.Slider),
        typeof(UnityEngine.UI.Dropdown),
        typeof(UnityEngine.UI.Dropdown.OptionData),
        typeof(UnityEngine.UI.Dropdown.OptionDataList),
        typeof(UnityEngine.UI.Dropdown.DropdownEvent),
        typeof(UnityEngine.UI.HorizontalLayoutGroup),
        typeof(UnityEngine.Resources),
        typeof(UnityEngine.ResourceRequest),
        typeof(UnityEngine.SceneManagement.SceneManager),
        typeof(AsyncOperation),
        typeof(PlayerPrefs),
        typeof(System.GC),
        typeof(EventSystem),
        typeof(UnityEngine.UI.LayoutRebuilder),

        // UGUI Extend
        typeof(ButtonPressed),

        //DOTween
        typeof(DG.Tweening.DOTweenAnimation),
        typeof(DG.Tweening.TweenSettingsExtensions),

        typeof(DG.Tweening.AutoPlay),
        typeof(DG.Tweening.AxisConstraint),
        typeof(DG.Tweening.Ease),
        typeof(DG.Tweening.LogBehaviour),
        typeof(DG.Tweening.LoopType),
        typeof(DG.Tweening.PathMode),
        typeof(DG.Tweening.PathType),
        typeof(DG.Tweening.RotateMode),
        typeof(DG.Tweening.ScrambleMode),
        typeof(DG.Tweening.TweenType),
        typeof(DG.Tweening.UpdateType),

        typeof(DG.Tweening.DOTween),
        typeof(DG.Tweening.DOVirtual),
        typeof(DG.Tweening.EaseFactory),
        typeof(DG.Tweening.Tweener),
        typeof(DG.Tweening.Tween),
        typeof(DG.Tweening.Sequence),
        typeof(DG.Tweening.TweenParams),
        typeof(DG.Tweening.Core.ABSSequentiable),

        typeof(DG.Tweening.Core.TweenerCore<Vector3, Vector3, DG.Tweening.Plugins.Options.VectorOptions>),
        typeof(DG.Tweening.Core.TweenerCore<Vector3, DG.Tweening.Plugins.Core.PathCore.Path, DG.Tweening.Plugins.Options.PathOptions>),
        typeof(DG.Tweening.TweenCallback),
        typeof(DG.Tweening.TweenExtensions),
        typeof(DG.Tweening.TweenSettingsExtensions),
        typeof(DG.Tweening.ShortcutExtensions),
        typeof(DG.Tweening.ShortcutExtensions43),
        typeof(DG.Tweening.ShortcutExtensions46),
        typeof(DG.Tweening.ShortcutExtensions50),
        //typeof(DG.Tweening.DOTweenProShortcuts),
        //typeof(DG.Tweening.DOTweenModuleAudio),
        //typeof(DG.Tweening.DOTweenModulePhysics),
        //typeof(DG.Tweening.DOTweenModulePhysics2D),
        //typeof(DG.Tweening.DOTweenModuleSprite),
        typeof(DG.Tweening.DOTweenModuleUI),
        
        
       
        //dotween pro 的功能
        typeof(DG.Tweening.DOTweenPath),
        typeof(DG.Tweening.TweenExtensions),

        //Spine
        typeof(SkeletonGraphic),
        typeof(SkeletonAnimation),
        typeof(SkeletonRenderer),
        typeof(SkeletonDataAsset),
        typeof(Spine.AnimationState),
        //custom
        typeof(SystemInfo),
        typeof(LuaManager),
        typeof(AssetManager),
        typeof(CoroutineRunner),
        typeof(WaitForSeconds),
        typeof(ControlExpand),
        typeof(Rijndael),
        typeof(PlayerPrefsEx),
        typeof(AnimEvent),
        typeof(PageView),
        typeof(TableView),
        typeof(TableViewV),
        typeof(TableViewH),
        typeof(TableViewCell),
        typeof(GameHelper),
        typeof(StartGame),
        typeof(TouchMono),
        typeof(UIDrag),
        typeof(UIDragXYDir),
        typeof(UICapture),
        typeof(UIRoot),
        typeof(UIMask),
        typeof(GameNetWork.Net.GameHttp),
        typeof(HttpMono),
        typeof(LanguageManager),
        typeof(LogMan),
        typeof(BoxCollider2D),
        typeof(ScriptExtend),
        typeof(UserDataMono),
        typeof(SortingLayerMono),
        typeof(UnityEngine.AudioSource),
        typeof(TutorialBlock),
        typeof(StorageManager),
        typeof(NetworkEventMgr),
        typeof(NetworkWebSocketEventMgr),
        typeof(NetworkWebTCPEventMgr),
        typeof(CCTableView),
        typeof(CCTableViewCell),
        typeof(CCTableViewController),
        typeof(LuaSdkHelper),
        typeof(GameSdkManager),
        typeof(SDKLoginModule),
        typeof(MapTiled),
        typeof(MonoLinkLuaData),
        typeof(MaterialSelect),
        typeof(UnityEngineObjectExtention),
        typeof(UIScrollView),
        typeof(Action<GameObject, int>),
        typeof(Action<string, GameObject,int>),
        typeof(LinkImageText),
        typeof(HyperlinkText),

      //  typeof(Firebase.Analytics.FirebaseAnalytics),

        //XLua
        //typeof(XLua.ObjectTranslator),

        //tinyGame
        typeof(bc.IFGame),
        typeof(bc.MiniGameBase.ILuaGameObject),
        typeof(bc.MiniGameBase.CollisionTriggerListener),
        typeof(bc.MiniGameBase.LuaContainer),
        typeof(bc.MiniGameBase.GameObjectComs),
        //typeof(bc.MiniGameBase.OrganBaseComponent),


        typeof(Rigidbody2D),
        typeof(Physics2D),
        typeof(UnityEngine.Random),
        typeof(CapsuleCollider),
        typeof(UnityEngine.Input),
        typeof(Screen),
        typeof(MeshRenderer),
        typeof(Physics),
        typeof(Color32),
        typeof(Material),
        typeof(UnityEngine.EventSystems.EventTriggerType),
        typeof(UnityEngine.EventSystems.EventTrigger.Entry),
        typeof(UnityEngine.EventSystems.EventTrigger),
        typeof(TrailRenderer),
        typeof(LineRenderer),
        typeof(CircleCollider2D),
        typeof(RelativeJoint2D),
        typeof(CapsuleCollider2D),
        typeof(SphereCollider),
        typeof(BoxCollider),
        typeof(Collider),
        typeof(Rigidbody),
        typeof(SpriteRenderer),
        typeof(UnityEngine.UI.LayoutElement),
        typeof(UnityEngine.UI.VerticalLayoutGroup),
        typeof(TMPro.TextMeshProUGUI),
        typeof(TMPro.TMP_InputField),
        typeof(TMPro.TextMeshPro),
        typeof(Collision2D),
        typeof(Collision),
        typeof(UI.UGUIExtendMini.ScrollRectItem),
        typeof(UnityEngine.AnimatorStateInfo),
        typeof(RenderTexture),
        typeof(RenderTextureFormat),
        typeof(RenderTextureReadWrite),
        
        typeof(MeshCollider),
        typeof(MeshFilter),
        typeof(Collider2D),
 
        
        //解环
        typeof(PolygonCollider2D),
        typeof(GameLuaBehaviour_New),
        typeof(RigidbodyType2D),
        //小游戏
        typeof(LevelManager),
        typeof(Vector3Int),
        typeof(Space),
        typeof(RectTransformUtility),
        typeof(RaycastHit2D),
        typeof(MiniVibration),
        typeof(UnityEngine.QualitySettings),
        typeof(UnityEngine.RenderSettings),

        };

        //C#静态调用Lua的配置（包括事件的原型），仅可以配delegate，interface
        [CSharpCallLua]
        public static List<Type> CSharpCallLua = new List<Type>() {
		// unity
        typeof(DeleteCell<string>),
		typeof(Action),
        typeof(Action<int>),
        typeof(Action<int, string>),
        typeof(Action<int, int, string>),
        typeof(Action<int,GameObject>),
        typeof(Action<WWW>),
        typeof(UnityEngine.Event),
        typeof(UnityEngine.Events.UnityAction),
        typeof(System.Collections.IEnumerator),
        typeof(UnityEngine.Events.UnityAction<Vector2>),
        typeof(Action<float>),
        typeof(Action<string>),
        typeof(Action<float, float>),
        typeof(Action<string, string, int>),
        typeof(Action<int, long, string>),
        typeof(Action<int, long, int, string>),
        typeof(Action<int, long, string, object>),
        typeof(Action<int, long, int, byte[]>),
        typeof(Action<PointerEventData, GameObject, float, float>),
        typeof(Action<PointerEventData, GameObject>),
        typeof(Action<bool>),
        typeof(DeleteCall<int>),
        typeof(FuncCount),
        typeof(FuncSize),
        typeof(FuncUpdateCell),
        typeof(FuncLoadFinish),
        typeof(FuncHideCell),
        typeof(FuncUpdateCellData),

        typeof(System.Action<Vector2[] , int>),
        typeof(System.Action<Vector2[] , int, int>),
        typeof(Action<GameObject, int>),
        typeof(Action<string, GameObject,int>),
//tinyGame
        typeof(bc.IFGame),
        typeof(bc.MiniGameBase.ILuaGameObject),

        typeof(UnityEngine.Events.UnityAction<Collision2D>),
        typeof(UnityEngine.Events.UnityAction<Collision>),

        typeof(Action<byte[]>),
    };
#if UNITY_2018_1_OR_NEWER
    [BlackList]
    public static Func<MemberInfo, bool> MethodFilter = (memberInfo) =>
    {
        if (memberInfo.DeclaringType.IsGenericType && memberInfo.DeclaringType.GetGenericTypeDefinition() == typeof(Dictionary<,>))
        {
            if (memberInfo.MemberType == MemberTypes.Constructor)
            {
                ConstructorInfo constructorInfo = memberInfo as ConstructorInfo;
                var parameterInfos = constructorInfo.GetParameters();
                if (parameterInfos.Length > 0)
                {
                    if (typeof(System.Collections.IEnumerable).IsAssignableFrom(parameterInfos[0].ParameterType))
                    {
                        return true;
                    }
                }
            }
            else if (memberInfo.MemberType == MemberTypes.Method)
            {
                var methodInfo = memberInfo as MethodInfo;
                if (methodInfo.Name == "TryAdd" || methodInfo.Name == "Remove" && methodInfo.GetParameters().Length == 2)
                {
                    return true;
                }
            }
        }
        return false;
    };
#endif
    //黑名单
    [BlackList]
        public static List<List<string>> BlackList = new List<List<string>>()  {
		// unity
            new List<string>(){"UnityEngine.AudioSource", "PlayOnGamepad","System.Int32"},
        new List<string>(){"UnityEngine.AudioSource", "DisableGamepadOutput"},
        new List<string>(){"UnityEngine.AudioSource", "SetGamepadSpeakerMixLevel","System.Int32","System.Int32"},
        new List<string>(){"UnityEngine.AudioSource", "SetGamepadSpeakerMixLevelDefault","System.Int32"},
        new List<string>(){"UnityEngine.AudioSource", "SetGamepadSpeakerRestrictedAudio","System.Int32","System.Boolean"},
        new List<string>(){"UnityEngine.AudioSource", "GamepadSpeakerSupportsOutputType","UnityEngine.GamepadSpeakerOutputType"},
       new List<string>(){"UnityEngine.AudioSource", "gamepadSpeakerOutputType"},

        new List<string>(){"UnityEngine.WWW", "movie"},
                new List<string>(){"UnityEngine.Texture2D", "alphaIsTransparency"},
        new List<string>(){"UnityEngine.WWW", "GetMovieTexture"},
        new List<string>(){"UnityEngine.Texture2D", "alphaIsTransparency"},
                new List<string>(){"UnityEngine.Security", "GetChainOfTrustValue"},
                new List<string>(){"UnityEngine.CanvasRenderer", "onRequestRebuild"},
                new List<string>(){"UnityEngine.Light", "areaSize"},
                new List<string>(){"UnityEngine.AnimatorOverrideController", "PerformOverrideClipListCleanup"},
		#if !UNITY_WEBPLAYER
		new List<string>(){"UnityEngine.Application", "ExternalEval"},
		#endif
		new List<string>(){"UnityEngine.GameObject", "networkView"}, //4.6.2 not support
		new List<string>(){"UnityEngine.Component", "networkView"},  //4.6.2 not support
		new List<string>(){"System.IO.FileInfo", "GetAccessControl", "System.Security.AccessControl.AccessControlSections"},
                new List<string>(){"System.IO.FileInfo", "SetAccessControl", "System.Security.AccessControl.FileSecurity"},
                new List<string>(){"System.IO.DirectoryInfo", "GetAccessControl", "System.Security.AccessControl.AccessControlSections"},
                new List<string>(){"System.IO.DirectoryInfo", "SetAccessControl", "System.Security.AccessControl.DirectorySecurity"},
                new List<string>(){"System.IO.DirectoryInfo", "CreateSubdirectory", "System.String", "System.Security.AccessControl.DirectorySecurity"},
                new List<string>(){"System.IO.DirectoryInfo", "Create", "System.Security.AccessControl.DirectorySecurity"},
                new List<string>(){"UnityEngine.MonoBehaviour", "runInEditMode"},
                new List<string>(){"UnityEngine.UI.Text", "OnRebuildRequested"},
        new List<string>(){"System.Type", "IsSZArray"},
        new List<string>(){"Firebase.Analytics.FirebaseAnalytics", "GetAnalyticsInstanceIdAsync"},

        new List<string>(){"UnityEngine.MeshRenderer", "receiveGI"},
        new List<string>(){"UnityEngine.MeshRenderer", "scaleInLightmap"},
        new List<string>(){"UnityEngine.MeshRenderer", "stitchLightmapSeams"},
        new List<string>(){"UnityEngine.Input", "IsJoystickPreconfigured","System.String"},
        new List<string>(){"UnityEngine.Input", "location","UnityEngine.LocationService"},

        new List<string>(){"UnityEngine.QualitySettings", "IsPlatformIncluded","System.String","System.Int32"},
        new List<string>(){"UnityEngine.QualitySettings", "TryIncludePlatformAt","System.String","System.Int32", "System.Exception&"},

        new List<string>(){"UnityEngine.QualitySettings", "TryExcludePlatformAt","System.String","System.Int32", "System.Exception&"},

        new List<string>(){"UnityEngine.QualitySettings", "GetActiveQualityLevelsForPlatform","System.String"},
        new List<string>(){"UnityEngine.QualitySettings", "GetActiveQualityLevelsForPlatformCount","System.String"},
        new List<string>(){"UnityEngine.QualitySettings", "GetAllRenderPipelineAssetsForPlatform","System.String","System.Collections.Generic.List`1[UnityEngine.Rendering.RenderPipelineAsset]&"},

        new List<string>(){"UnityEngine.Material", "IsChildOf","UnityEngine.Material"},
        new List<string>(){"UnityEngine.Material", "RevertAllPropertyOverrides"},

        new List<string>(){"UnityEngine.Material", "IsPropertyOverriden","System.Int32"},
        new List<string>(){"UnityEngine.Material", "IsPropertyLocked","System.Int32"},
        new List<string>(){"UnityEngine.Material", "IsPropertyLockedByAncestor","System.Int32"},
        new List<string>(){"UnityEngine.Material", "IsPropertyLockedByAncestor","System.String"},

        new List<string>(){"UnityEngine.Material", "ApplyPropertyOverride","UnityEngine.Material","System.Int32","System.Boolean"},
        new List<string>(){"UnityEngine.Material", "RevertPropertyOverride","System.Int32"},
        new List<string>(){"UnityEngine.Material", "IsPropertyOverriden","System.String"},
        new List<string>(){"UnityEngine.Material", "IsPropertyLocked","System.String"},
        new List<string>(){"UnityEngine.Material", "SetPropertyLock","System.String" ,"System.Boolean"},
        new List<string>(){"UnityEngine.Material", "SetPropertyLock","System.Int32","System.Boolean"},
        new List<string>(){"UnityEngine.Material", "ApplyPropertyOverride","UnityEngine.Material","System.Int32"},
        new List<string>(){"UnityEngine.Material", "ApplyPropertyOverride","UnityEngine.Material","System.String"},
        new List<string>(){"UnityEngine.Material", "ApplyPropertyOverride","UnityEngine.Material","System.String","System.Boolean"},
        new List<string>(){"UnityEngine.Material", "RevertPropertyOverride","System.String"},
        new List<string>(){"UnityEngine.Material", "parent"},
        new List<string>(){"UnityEngine.Material", "isVariant"},


        new List<string>(){"UnityEngine.SystemInfo", "supportsAnisotropicFilter"},
        new List<string>(){"UnityEngine.SystemInfo", "maxAnisotropyLevel"},


        new List<string>(){"UnityEngine.Object", "InstantiateAsync" , "UnityEngine.Object" , "UnityEngine.InstantiateParameters"},
        new List<string>(){"UnityEngine.Object", "InstantiateAsync" , "UnityEngine.Object" ,"System.Int32", "UnityEngine.InstantiateParameters"},
        new List<string>(){"UnityEngine.Object", "InstantiateAsync" , "UnityEngine.Object" ,"UnityEngine.Vector3", " UnityEngine.Quaternion","UnityEngine.InstantiateParameters"},
        new List<string>(){"UnityEngine.Object", "InstantiateAsync" , "UnityEngine.Object" ,"UnityEngine.Vector3", " UnityEngine.Quaternion","UnityEngine.InstantiateParameters"},
        new List<string>(){"UnityEngine.Object", "InstantiateAsync" , "UnityEngine.Object"  ,"System.Int32" ,"UnityEngine.Vector3", " UnityEngine.Quaternion","UnityEngine.InstantiateParameters"},
        new List<string>(){"UnityEngine.Object", "InstantiateAsync" , "UnityEngine.Object"  ,"System.Int32" ,"UnityEngine.Vector3", " UnityEngine.Quaternion","UnityEngine.InstantiateParameters"},


        new List<string>(){"UnityEngine.Object", "Instantiate" , "UnityEngine.Object" , "UnityEngine.InstantiateParameters"},
        new List<string>(){"UnityEngine.Object", "Instantiate" , "UnityEngine.Object" ,"System.Int32", "UnityEngine.InstantiateParameters"},
        new List<string>(){"UnityEngine.Object", "Instantiate" , "UnityEngine.Object" ,"UnityEngine.Vector3", " UnityEngine.Quaternion","UnityEngine.InstantiateParameters"},
        new List<string>(){"UnityEngine.Object", "Instantiate" , "UnityEngine.Object" ,"UnityEngine.Vector3", " UnityEngine.Quaternion","UnityEngine.InstantiateParameters"},
        new List<string>(){"UnityEngine.Object", "Instantiate" , "UnityEngine.Object"  ,"System.Int32" ,"UnityEngine.Vector3", " UnityEngine.Quaternion","UnityEngine.InstantiateParameters"},
        new List<string>(){"UnityEngine.Object", "Instantiate" , "UnityEngine.Object"  ,"System.Int32" ,"UnityEngine.Vector3", " UnityEngine.Quaternion","UnityEngine.InstantiateParameters"},


        };
}
