﻿#if USE_UNI_LUA
using LuaAPI = UniLua.Lua;
using RealStatePtr = UniLua.ILuaState;
using LuaCSFunction = UniLua.CSharpFunctionDelegate;
#else
using LuaAPI = XLua.LuaDLL.Lua;
using RealStatePtr = System.IntPtr;
using LuaCSFunction = XLua.LuaDLL.lua_CSFunction;
#endif

using XLua;
using System.Collections.Generic;


namespace XLua.CSObjectWrap
{
    using Utils = XLua.Utils;
    public class GameHelperWrap 
    {
        public static void __Register(RealStatePtr L)
        {
			ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			System.Type type = typeof(GameHelper);
			Utils.BeginObjectRegister(type, L, translator, 0, 0, 0, 0);
			
			
			
			
			
			
			Utils.EndObjectRegister(type, L, translator, null, null,
			    null, null, null);

		    Utils.BeginClassRegister(type, L, __CreateInstance, 126, 4, 3);
			Utils.RegisterFunc(L, Utils.CLS_IDX, "IsWebGL", _m_IsWebGL_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "Init", _m_Init_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetMaterialCache", _m_SetMaterialCache_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "GetMaterial", _m_GetMaterial_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "Instantiate", _m_Instantiate_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "InstantiateAllWithPos", _m_InstantiateAllWithPos_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SearchChild", _m_SearchChild_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SearchParent", _m_SearchParent_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SearchParentGo", _m_SearchParentGo_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetSprite", _m_SetSprite_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "AddColliderBox", _m_AddColliderBox_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetColliderBoxEnable", _m_SetColliderBoxEnable_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetColliderPolyEnable", _m_SetColliderPolyEnable_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetLayer", _m_SetLayer_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetOrder", _m_SetOrder_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetOrderScript", _m_SetOrderScript_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetSpineMaterial", _m_SetSpineMaterial_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetSpineColor", _m_SetSpineColor_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetSpineGray", _m_SetSpineGray_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetShaderProperty", _m_SetShaderProperty_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetShaderSpriteProperty", _m_SetShaderSpriteProperty_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetShaderSpriteAllProperty", _m_SetShaderSpriteAllProperty_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetColorSpriteRenderer", _m_SetColorSpriteRenderer_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetColorSpriteRendererAll", _m_SetColorSpriteRendererAll_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetSpriteRendererSMaterial", _m_SetSpriteRendererSMaterial_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetSpriteRendererSMaterialAll", _m_SetSpriteRendererSMaterialAll_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "AddRawImageMaskBlur", _m_AddRawImageMaskBlur_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "DOLocalMove", _m_DOLocalMove_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "DOLocalMoveBase", _m_DOLocalMoveBase_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "DOMove", _m_DOMove_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "DOMoveBase", _m_DOMoveBase_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "DOLocalRotate", _m_DOLocalRotate_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "DOLocalRotateBase", _m_DOLocalRotateBase_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "DORotate", _m_DORotate_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "DORotateBase", _m_DORotateBase_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "DOScale", _m_DOScale_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "DOScaleBase", _m_DOScaleBase_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "DOColorSpriteRendererTrans", _m_DOColorSpriteRendererTrans_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "DOFadeSpriteRendererTrans", _m_DOFadeSpriteRendererTrans_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "DOColorSpriteRendererBaseTrans", _m_DOColorSpriteRendererBaseTrans_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "DOTweenSetup", _m_DOTweenSetup_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "DOKillAll", _m_DOKillAll_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "DOKillSpriteRenderer", _m_DOKillSpriteRenderer_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "DOKillAllSpriteRenderer", _m_DOKillAllSpriteRenderer_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "DOAction", _m_DOAction_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "DOActionAll", _m_DOActionAll_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "DoActionAll", _m_DoActionAll_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "GetRectTransformWithOffsetAndAnchor", _m_GetRectTransformWithOffsetAndAnchor_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "GetRectTransform", _m_GetRectTransform_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "GetGameObjectRect", _m_GetGameObjectRect_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "GetGameObjectSize", _m_GetGameObjectSize_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetRTPosition", _m_SetRTPosition_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetRTPositionX", _m_SetRTPositionX_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetRTPositionY", _m_SetRTPositionY_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetPosition", _m_SetPosition_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetLocalPositionAll", _m_SetLocalPositionAll_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetLocalPositionY", _m_SetLocalPositionY_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetLocalPosition", _m_SetLocalPosition_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetGridLayoutGroup", _m_SetGridLayoutGroup_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "ForceRebuildLayoutImmediate", _m_ForceRebuildLayoutImmediate_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetRotation", _m_SetRotation_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetRotationX", _m_SetRotationX_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetRotationY", _m_SetRotationY_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetRotationZ", _m_SetRotationZ_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetLocalRotation", _m_SetLocalRotation_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetLocalRotationX", _m_SetLocalRotationX_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetLocalRotationY", _m_SetLocalRotationY_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetLocalRotationZ", _m_SetLocalRotationZ_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetLocalRotationGo", _m_SetLocalRotationGo_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetLocalRotationXGo", _m_SetLocalRotationXGo_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetLocalRotationYGo", _m_SetLocalRotationYGo_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetLocalRotationZGo", _m_SetLocalRotationZGo_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "GetScreenPos", _m_GetScreenPos_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetLocalScale", _m_SetLocalScale_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetLocalScaleX", _m_SetLocalScaleX_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetLocalScaleY", _m_SetLocalScaleY_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetLocalScaleZ", _m_SetLocalScaleZ_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetLocalScaleGo", _m_SetLocalScaleGo_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetLocalScaleXGo", _m_SetLocalScaleXGo_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetLocalScaleYGo", _m_SetLocalScaleYGo_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetLocalScaleZGo", _m_SetLocalScaleZGo_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "IsPointerOverGameObject", _m_IsPointerOverGameObject_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "GetPointerOverGameObject", _m_GetPointerOverGameObject_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetActive", _m_SetActive_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetActiveAll", _m_SetActiveAll_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetRecycleAll", _m_SetRecycleAll_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "UpdateBoxColliderSize", _m_UpdateBoxColliderSize_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "ColliderIntersects", _m_ColliderIntersects_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "ColliderContains", _m_ColliderContains_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "PointInbounds", _m_PointInbounds_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "RotateVectorWithDegree", _m_RotateVectorWithDegree_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "GetMonoUserData", _m_GetMonoUserData_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "GetMonoUserDataInt", _m_GetMonoUserDataInt_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "GetMonoUserDataDouble", _m_GetMonoUserDataDouble_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "GetMonoUserDataString", _m_GetMonoUserDataString_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetMonoUserDataParam", _m_SetMonoUserDataParam_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetAnchor", _m_SetAnchor_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "AddClick", _m_AddClick_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "GenerateColors", _m_GenerateColors_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "GenerateColorsW", _m_GenerateColorsW_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "GetTimeStamp", _m_GetTimeStamp_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "RayTouchedWithoutUI", _m_RayTouchedWithoutUI_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "IsRayTouchedUIView", _m_IsRayTouchedUIView_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "RayTouched3D", _m_RayTouched3D_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "RayTouched2D", _m_RayTouched2D_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "RayWorldUGUI", _m_RayWorldUGUI_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "RayTouched3DUGUI", _m_RayTouched3DUGUI_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "MD5String", _m_MD5String_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "Base64ToString", _m_Base64ToString_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "GetXOR", _m_GetXOR_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "EmailIsMatch", _m_EmailIsMatch_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "QuitGame", _m_QuitGame_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "AddToManagerTrans", _m_AddToManagerTrans_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "ToLiteralStr", _m_ToLiteralStr_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "ResetAnimation", _m_ResetAnimation_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "PlayAnimationState", _m_PlayAnimationState_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "GetWorldCornersByTrans", _m_GetWorldCornersByTrans_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "LoadSpriteAsync", _m_LoadSpriteAsync_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "LoadSpriteAsyncByUrl", _m_LoadSpriteAsyncByUrl_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "HMACSHA256Encrypt", _m_HMACSHA256Encrypt_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "AesEncrypt", _m_AesEncrypt_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "AesDecrypt", _m_AesDecrypt_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "BytesToString", _m_BytesToString_xlua_st_);
            Utils.RegisterFunc(L, Utils.CLS_IDX, "SetParticleSystemStartSpeed", _m_SetParticleSystemStartSpeed_xlua_st_);
            
			
            Utils.RegisterObject(L, translator, Utils.CLS_IDX, "ms_RijndaelManager", GameHelper.ms_RijndaelManager);
            
			Utils.RegisterFunc(L, Utils.CLS_GETTER_IDX, "persistentDataPath", _g_get_persistentDataPath);
            Utils.RegisterFunc(L, Utils.CLS_GETTER_IDX, "s_SharedMatBlock", _g_get_s_SharedMatBlock);
            Utils.RegisterFunc(L, Utils.CLS_GETTER_IDX, "hitResult", _g_get_hitResult);
            Utils.RegisterFunc(L, Utils.CLS_GETTER_IDX, "grahicList", _g_get_grahicList);
            
			Utils.RegisterFunc(L, Utils.CLS_SETTER_IDX, "s_SharedMatBlock", _s_set_s_SharedMatBlock);
            Utils.RegisterFunc(L, Utils.CLS_SETTER_IDX, "hitResult", _s_set_hitResult);
            Utils.RegisterFunc(L, Utils.CLS_SETTER_IDX, "grahicList", _s_set_grahicList);
            
			
			Utils.EndClassRegister(type, L, translator);
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int __CreateInstance(RealStatePtr L)
        {
            
			try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
				if(LuaAPI.lua_gettop(L) == 1)
				{
					
					GameHelper gen_ret = new GameHelper();
					translator.Push(L, gen_ret);
                    
					return 1;
				}
				
			}
			catch(System.Exception gen_e) {
				return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
			}
            return LuaAPI.luaL_error(L, "invalid arguments to GameHelper constructor!");
            
        }
        
		
        
		
        
        
        
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_IsWebGL_xlua_st_(RealStatePtr L)
        {
		    try {
            
            
            
                
                {
                    
                        bool gen_ret = GameHelper.IsWebGL(  );

                        LuaAPI.lua_pushboolean(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_Init_xlua_st_(RealStatePtr L)
        {
		    try {
            
            
            
                
                {
                    
                    GameHelper.Init(  );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetMaterialCache_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    string _saveKey = LuaAPI.lua_tostring(L, 1);
                    UnityEngine.Material _mat = (UnityEngine.Material)translator.GetObject(L, 2, typeof(UnityEngine.Material));
                    
                    GameHelper.SetMaterialCache( _saveKey, _mat );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetMaterial_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    string _shaderName = LuaAPI.lua_tostring(L, 1);
                    string _txtName = LuaAPI.lua_tostring(L, 2);
                    
                        UnityEngine.Material gen_ret = GameHelper.GetMaterial( _shaderName, _txtName );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_Instantiate_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _prefab = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    UnityEngine.GameObject _go;
                    UnityEngine.Transform _trans;
                    
                    GameHelper.Instantiate( _prefab, out _go, out _trans );

                    translator.Push(L, _go);
                        
                    translator.Push(L, _trans);
                        
                    
                    
                    
                    return 2;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_InstantiateAllWithPos_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _prefab = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    UnityEngine.Transform _pa = (UnityEngine.Transform)translator.GetObject(L, 2, typeof(UnityEngine.Transform));
                    System.Collections.Generic.List<System.Collections.Generic.List<float>> _tList = (System.Collections.Generic.List<System.Collections.Generic.List<float>>)translator.GetObject(L, 3, typeof(System.Collections.Generic.List<System.Collections.Generic.List<float>>));
                    
                    GameHelper.InstantiateAllWithPos( _prefab, _pa, _tList );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SearchChild_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    string _name = LuaAPI.lua_tostring(L, 2);
                    System.Type _t = (System.Type)translator.GetObject(L, 3, typeof(System.Type));
                    
                        UnityEngine.Component gen_ret = GameHelper.SearchChild( _trans, _name, _t );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SearchParent_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    int _level = LuaAPI.xlua_tointeger(L, 2);
                    
                        UnityEngine.Transform gen_ret = GameHelper.SearchParent( _trans, _level );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SearchParentGo_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    int _level = LuaAPI.xlua_tointeger(L, 2);
                    
                        UnityEngine.Transform gen_ret = GameHelper.SearchParentGo( _go, _level );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetSprite_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.SpriteRenderer _renderer = (UnityEngine.SpriteRenderer)translator.GetObject(L, 1, typeof(UnityEngine.SpriteRenderer));
                    UnityEngine.Sprite _sp = (UnityEngine.Sprite)translator.GetObject(L, 2, typeof(UnityEngine.Sprite));
                    
                    GameHelper.SetSprite( _renderer, _sp );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_AddColliderBox_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    float _x = (float)LuaAPI.lua_tonumber(L, 2);
                    float _y = (float)LuaAPI.lua_tonumber(L, 3);
                    
                    GameHelper.AddColliderBox( _go, _x, _y );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetColliderBoxEnable_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
			    int gen_param_count = LuaAPI.lua_gettop(L);
            
                if(gen_param_count == 6&& translator.Assignable<UnityEngine.GameObject>(L, 1)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 2)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 3)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 4)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 5)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 6)) 
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    int _isOpen = LuaAPI.xlua_tointeger(L, 2);
                    float _ox = (float)LuaAPI.lua_tonumber(L, 3);
                    float _oy = (float)LuaAPI.lua_tonumber(L, 4);
                    float _x = (float)LuaAPI.lua_tonumber(L, 5);
                    float _y = (float)LuaAPI.lua_tonumber(L, 6);
                    
                    GameHelper.SetColliderBoxEnable( _go, _isOpen, _ox, _oy, _x, _y );

                    
                    
                    
                    return 0;
                }
                if(gen_param_count == 5&& translator.Assignable<UnityEngine.GameObject>(L, 1)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 2)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 3)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 4)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 5)) 
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    int _isOpen = LuaAPI.xlua_tointeger(L, 2);
                    float _ox = (float)LuaAPI.lua_tonumber(L, 3);
                    float _oy = (float)LuaAPI.lua_tonumber(L, 4);
                    float _x = (float)LuaAPI.lua_tonumber(L, 5);
                    
                    GameHelper.SetColliderBoxEnable( _go, _isOpen, _ox, _oy, _x );

                    
                    
                    
                    return 0;
                }
                if(gen_param_count == 4&& translator.Assignable<UnityEngine.GameObject>(L, 1)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 2)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 3)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 4)) 
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    int _isOpen = LuaAPI.xlua_tointeger(L, 2);
                    float _ox = (float)LuaAPI.lua_tonumber(L, 3);
                    float _oy = (float)LuaAPI.lua_tonumber(L, 4);
                    
                    GameHelper.SetColliderBoxEnable( _go, _isOpen, _ox, _oy );

                    
                    
                    
                    return 0;
                }
                if(gen_param_count == 3&& translator.Assignable<UnityEngine.GameObject>(L, 1)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 2)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 3)) 
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    int _isOpen = LuaAPI.xlua_tointeger(L, 2);
                    float _ox = (float)LuaAPI.lua_tonumber(L, 3);
                    
                    GameHelper.SetColliderBoxEnable( _go, _isOpen, _ox );

                    
                    
                    
                    return 0;
                }
                if(gen_param_count == 2&& translator.Assignable<UnityEngine.GameObject>(L, 1)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 2)) 
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    int _isOpen = LuaAPI.xlua_tointeger(L, 2);
                    
                    GameHelper.SetColliderBoxEnable( _go, _isOpen );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
            return LuaAPI.luaL_error(L, "invalid arguments to GameHelper.SetColliderBoxEnable!");
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetColliderPolyEnable_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
			    int gen_param_count = LuaAPI.lua_gettop(L);
            
                if(gen_param_count == 3&& translator.Assignable<UnityEngine.GameObject>(L, 1)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 2)&& translator.Assignable<System.Collections.Generic.List<UnityEngine.Vector2>>(L, 3)) 
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    int _isOpen = LuaAPI.xlua_tointeger(L, 2);
                    System.Collections.Generic.List<UnityEngine.Vector2> _tVtList = (System.Collections.Generic.List<UnityEngine.Vector2>)translator.GetObject(L, 3, typeof(System.Collections.Generic.List<UnityEngine.Vector2>));
                    
                    GameHelper.SetColliderPolyEnable( _go, _isOpen, _tVtList );

                    
                    
                    
                    return 0;
                }
                if(gen_param_count == 2&& translator.Assignable<UnityEngine.GameObject>(L, 1)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 2)) 
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    int _isOpen = LuaAPI.xlua_tointeger(L, 2);
                    
                    GameHelper.SetColliderPolyEnable( _go, _isOpen );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
            return LuaAPI.luaL_error(L, "invalid arguments to GameHelper.SetColliderPolyEnable!");
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetLayer_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    int _layerId = LuaAPI.xlua_tointeger(L, 2);
                    int _allChild = LuaAPI.xlua_tointeger(L, 3);
                    
                    GameHelper.SetLayer( _go, _layerId, _allChild );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetOrder_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Renderer _render = (UnityEngine.Renderer)translator.GetObject(L, 1, typeof(UnityEngine.Renderer));
                    int _layerId = LuaAPI.xlua_tointeger(L, 2);
                    int _orderInLayer = LuaAPI.xlua_tointeger(L, 3);
                    
                    GameHelper.SetOrder( _render, _layerId, _orderInLayer );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetOrderScript_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    int _type = LuaAPI.xlua_tointeger(L, 2);
                    int _flag = LuaAPI.xlua_tointeger(L, 3);
                    int _layerId = LuaAPI.xlua_tointeger(L, 4);
                    int _order = LuaAPI.xlua_tointeger(L, 5);
                    
                        SortingLayerMono gen_ret = GameHelper.SetOrderScript( _go, _type, _flag, _layerId, _order );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetSpineMaterial_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _gameObject = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    string _msg = LuaAPI.lua_tostring(L, 2);
                    
                    GameHelper.SetSpineMaterial( _gameObject, _msg );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetSpineColor_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    float _r = (float)LuaAPI.lua_tonumber(L, 2);
                    float _g = (float)LuaAPI.lua_tonumber(L, 3);
                    float _b = (float)LuaAPI.lua_tonumber(L, 4);
                    float _a = (float)LuaAPI.lua_tonumber(L, 5);
                    float _phase = (float)LuaAPI.lua_tonumber(L, 6);
                    
                    GameHelper.SetSpineColor( _go, _r, _g, _b, _a, _phase );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetSpineGray_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    int _isGray = LuaAPI.xlua_tointeger(L, 2);
                    float _phase = (float)LuaAPI.lua_tonumber(L, 3);
                    
                    GameHelper.SetSpineGray( _go, _isGray, _phase );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetShaderProperty_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    string _property = LuaAPI.lua_tostring(L, 2);
                    int _value = LuaAPI.xlua_tointeger(L, 3);
                    
                    GameHelper.SetShaderProperty( _go, _property, _value );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetShaderSpriteProperty_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    string _property = LuaAPI.lua_tostring(L, 2);
                    int _value = LuaAPI.xlua_tointeger(L, 3);
                    
                    GameHelper.SetShaderSpriteProperty( _go, _property, _value );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetShaderSpriteAllProperty_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _tParent = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    string _property = LuaAPI.lua_tostring(L, 2);
                    int _value = LuaAPI.xlua_tointeger(L, 3);
                    
                    GameHelper.SetShaderSpriteAllProperty( _tParent, _property, _value );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetColorSpriteRenderer_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.SpriteRenderer _renderer = (UnityEngine.SpriteRenderer)translator.GetObject(L, 1, typeof(UnityEngine.SpriteRenderer));
                    float _r = (float)LuaAPI.lua_tonumber(L, 2);
                    float _g = (float)LuaAPI.lua_tonumber(L, 3);
                    float _b = (float)LuaAPI.lua_tonumber(L, 4);
                    float _a = (float)LuaAPI.lua_tonumber(L, 5);
                    
                    GameHelper.SetColorSpriteRenderer( _renderer, _r, _g, _b, _a );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetColorSpriteRendererAll_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _tParent = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _r = (float)LuaAPI.lua_tonumber(L, 2);
                    float _g = (float)LuaAPI.lua_tonumber(L, 3);
                    float _b = (float)LuaAPI.lua_tonumber(L, 4);
                    float _a = (float)LuaAPI.lua_tonumber(L, 5);
                    
                    GameHelper.SetColorSpriteRendererAll( _tParent, _r, _g, _b, _a );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetSpriteRendererSMaterial_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.SpriteRenderer _render = (UnityEngine.SpriteRenderer)translator.GetObject(L, 1, typeof(UnityEngine.SpriteRenderer));
                    string _name = LuaAPI.lua_tostring(L, 2);
                    
                    GameHelper.SetSpriteRendererSMaterial( _render, _name );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetSpriteRendererSMaterialAll_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _tParent = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    string _name = LuaAPI.lua_tostring(L, 2);
                    
                    GameHelper.SetSpriteRendererSMaterialAll( _tParent, _name );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_AddRawImageMaskBlur_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    System.Action<int> _tFun = translator.GetDelegate<System.Action<int>>(L, 2);
                    
                    GameHelper.AddRawImageMaskBlur( _go, _tFun );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_DOLocalMove_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _sx = (float)LuaAPI.lua_tonumber(L, 2);
                    float _sy = (float)LuaAPI.lua_tonumber(L, 3);
                    float _sz = (float)LuaAPI.lua_tonumber(L, 4);
                    float _dx = (float)LuaAPI.lua_tonumber(L, 5);
                    float _dy = (float)LuaAPI.lua_tonumber(L, 6);
                    float _dz = (float)LuaAPI.lua_tonumber(L, 7);
                    float _duration = (float)LuaAPI.lua_tonumber(L, 8);
                    int _isKillBefore = LuaAPI.xlua_tointeger(L, 9);
                    
                        DG.Tweening.Tweener gen_ret = GameHelper.DOLocalMove( _trans, _sx, _sy, _sz, _dx, _dy, _dz, _duration, _isKillBefore );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_DOLocalMoveBase_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _dx = (float)LuaAPI.lua_tonumber(L, 2);
                    float _dy = (float)LuaAPI.lua_tonumber(L, 3);
                    float _dz = (float)LuaAPI.lua_tonumber(L, 4);
                    float _duration = (float)LuaAPI.lua_tonumber(L, 5);
                    int _isKillBefore = LuaAPI.xlua_tointeger(L, 6);
                    
                        DG.Tweening.Tweener gen_ret = GameHelper.DOLocalMoveBase( _trans, _dx, _dy, _dz, _duration, _isKillBefore );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_DOMove_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _sx = (float)LuaAPI.lua_tonumber(L, 2);
                    float _sy = (float)LuaAPI.lua_tonumber(L, 3);
                    float _sz = (float)LuaAPI.lua_tonumber(L, 4);
                    float _dx = (float)LuaAPI.lua_tonumber(L, 5);
                    float _dy = (float)LuaAPI.lua_tonumber(L, 6);
                    float _dz = (float)LuaAPI.lua_tonumber(L, 7);
                    float _duration = (float)LuaAPI.lua_tonumber(L, 8);
                    int _isKillBefore = LuaAPI.xlua_tointeger(L, 9);
                    
                        DG.Tweening.Tweener gen_ret = GameHelper.DOMove( _trans, _sx, _sy, _sz, _dx, _dy, _dz, _duration, _isKillBefore );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_DOMoveBase_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _dx = (float)LuaAPI.lua_tonumber(L, 2);
                    float _dy = (float)LuaAPI.lua_tonumber(L, 3);
                    float _dz = (float)LuaAPI.lua_tonumber(L, 4);
                    float _duration = (float)LuaAPI.lua_tonumber(L, 5);
                    int _isKillBefore = LuaAPI.xlua_tointeger(L, 6);
                    
                        DG.Tweening.Tweener gen_ret = GameHelper.DOMoveBase( _trans, _dx, _dy, _dz, _duration, _isKillBefore );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_DOLocalRotate_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _sx = (float)LuaAPI.lua_tonumber(L, 2);
                    float _sy = (float)LuaAPI.lua_tonumber(L, 3);
                    float _sz = (float)LuaAPI.lua_tonumber(L, 4);
                    float _dx = (float)LuaAPI.lua_tonumber(L, 5);
                    float _dy = (float)LuaAPI.lua_tonumber(L, 6);
                    float _dz = (float)LuaAPI.lua_tonumber(L, 7);
                    float _duration = (float)LuaAPI.lua_tonumber(L, 8);
                    int _isKillBefore = LuaAPI.xlua_tointeger(L, 9);
                    
                        DG.Tweening.Tweener gen_ret = GameHelper.DOLocalRotate( _trans, _sx, _sy, _sz, _dx, _dy, _dz, _duration, _isKillBefore );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_DOLocalRotateBase_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _dx = (float)LuaAPI.lua_tonumber(L, 2);
                    float _dy = (float)LuaAPI.lua_tonumber(L, 3);
                    float _dz = (float)LuaAPI.lua_tonumber(L, 4);
                    float _duration = (float)LuaAPI.lua_tonumber(L, 5);
                    int _isKillBefore = LuaAPI.xlua_tointeger(L, 6);
                    
                        DG.Tweening.Tweener gen_ret = GameHelper.DOLocalRotateBase( _trans, _dx, _dy, _dz, _duration, _isKillBefore );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_DORotate_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _sx = (float)LuaAPI.lua_tonumber(L, 2);
                    float _sy = (float)LuaAPI.lua_tonumber(L, 3);
                    float _sz = (float)LuaAPI.lua_tonumber(L, 4);
                    float _dx = (float)LuaAPI.lua_tonumber(L, 5);
                    float _dy = (float)LuaAPI.lua_tonumber(L, 6);
                    float _dz = (float)LuaAPI.lua_tonumber(L, 7);
                    float _duration = (float)LuaAPI.lua_tonumber(L, 8);
                    int _isKillBefore = LuaAPI.xlua_tointeger(L, 9);
                    
                        DG.Tweening.Tweener gen_ret = GameHelper.DORotate( _trans, _sx, _sy, _sz, _dx, _dy, _dz, _duration, _isKillBefore );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_DORotateBase_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _dx = (float)LuaAPI.lua_tonumber(L, 2);
                    float _dy = (float)LuaAPI.lua_tonumber(L, 3);
                    float _dz = (float)LuaAPI.lua_tonumber(L, 4);
                    float _duration = (float)LuaAPI.lua_tonumber(L, 5);
                    int _isKillBefore = LuaAPI.xlua_tointeger(L, 6);
                    
                        DG.Tweening.Tweener gen_ret = GameHelper.DORotateBase( _trans, _dx, _dy, _dz, _duration, _isKillBefore );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_DOScale_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _sx = (float)LuaAPI.lua_tonumber(L, 2);
                    float _sy = (float)LuaAPI.lua_tonumber(L, 3);
                    float _sz = (float)LuaAPI.lua_tonumber(L, 4);
                    float _dx = (float)LuaAPI.lua_tonumber(L, 5);
                    float _dy = (float)LuaAPI.lua_tonumber(L, 6);
                    float _dz = (float)LuaAPI.lua_tonumber(L, 7);
                    float _duration = (float)LuaAPI.lua_tonumber(L, 8);
                    int _isKillBefore = LuaAPI.xlua_tointeger(L, 9);
                    
                        DG.Tweening.Tweener gen_ret = GameHelper.DOScale( _trans, _sx, _sy, _sz, _dx, _dy, _dz, _duration, _isKillBefore );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_DOScaleBase_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _dx = (float)LuaAPI.lua_tonumber(L, 2);
                    float _dy = (float)LuaAPI.lua_tonumber(L, 3);
                    float _dz = (float)LuaAPI.lua_tonumber(L, 4);
                    float _duration = (float)LuaAPI.lua_tonumber(L, 5);
                    int _isKillBefore = LuaAPI.xlua_tointeger(L, 6);
                    
                        DG.Tweening.Tweener gen_ret = GameHelper.DOScaleBase( _trans, _dx, _dy, _dz, _duration, _isKillBefore );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_DOColorSpriteRendererTrans_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _sr = (float)LuaAPI.lua_tonumber(L, 2);
                    float _sg = (float)LuaAPI.lua_tonumber(L, 3);
                    float _sb = (float)LuaAPI.lua_tonumber(L, 4);
                    float _sa = (float)LuaAPI.lua_tonumber(L, 5);
                    float _dr = (float)LuaAPI.lua_tonumber(L, 6);
                    float _dg = (float)LuaAPI.lua_tonumber(L, 7);
                    float _db = (float)LuaAPI.lua_tonumber(L, 8);
                    float _da = (float)LuaAPI.lua_tonumber(L, 9);
                    float _duration = (float)LuaAPI.lua_tonumber(L, 10);
                    int _isKillBefore = LuaAPI.xlua_tointeger(L, 11);
                    
                        DG.Tweening.Tweener gen_ret = GameHelper.DOColorSpriteRendererTrans( _trans, _sr, _sg, _sb, _sa, _dr, _dg, _db, _da, _duration, _isKillBefore );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_DOFadeSpriteRendererTrans_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _from = (float)LuaAPI.lua_tonumber(L, 2);
                    float _to = (float)LuaAPI.lua_tonumber(L, 3);
                    float _duration = (float)LuaAPI.lua_tonumber(L, 4);
                    int _isKillBefore = LuaAPI.xlua_tointeger(L, 5);
                    
                        DG.Tweening.Tweener gen_ret = GameHelper.DOFadeSpriteRendererTrans( _trans, _from, _to, _duration, _isKillBefore );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_DOColorSpriteRendererBaseTrans_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _r = (float)LuaAPI.lua_tonumber(L, 2);
                    float _g = (float)LuaAPI.lua_tonumber(L, 3);
                    float _b = (float)LuaAPI.lua_tonumber(L, 4);
                    float _a = (float)LuaAPI.lua_tonumber(L, 5);
                    float _duration = (float)LuaAPI.lua_tonumber(L, 6);
                    int _isKillBefore = LuaAPI.xlua_tointeger(L, 7);
                    
                        DG.Tweening.Tweener gen_ret = GameHelper.DOColorSpriteRendererBaseTrans( _trans, _r, _g, _b, _a, _duration, _isKillBefore );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_DOTweenSetup_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    DG.Tweening.Tweener _t = (DG.Tweening.Tweener)translator.GetObject(L, 1, typeof(DG.Tweening.Tweener));
                    float _delay = (float)LuaAPI.lua_tonumber(L, 2);
                    int _loopTimes = LuaAPI.xlua_tointeger(L, 3);
                    int _easeType = LuaAPI.xlua_tointeger(L, 4);
                    DG.Tweening.TweenCallback _onFinished = translator.GetDelegate<DG.Tweening.TweenCallback>(L, 5);
                    
                    GameHelper.DOTweenSetup( _t, _delay, _loopTimes, _easeType, _onFinished );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_DOKillAll_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
			    int gen_param_count = LuaAPI.lua_gettop(L);
            
                if(gen_param_count == 1&& translator.Assignable<System.Collections.Generic.List<UnityEngine.Transform>>(L, 1)) 
                {
                    System.Collections.Generic.List<UnityEngine.Transform> _tList = (System.Collections.Generic.List<UnityEngine.Transform>)translator.GetObject(L, 1, typeof(System.Collections.Generic.List<UnityEngine.Transform>));
                    
                    GameHelper.DOKillAll( _tList );

                    
                    
                    
                    return 0;
                }
                if(gen_param_count == 1&& translator.Assignable<UnityEngine.Transform>(L, 1)) 
                {
                    UnityEngine.Transform _tParent = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    
                    GameHelper.DOKillAll( _tParent );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
            return LuaAPI.luaL_error(L, "invalid arguments to GameHelper.DOKillAll!");
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_DOKillSpriteRenderer_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.SpriteRenderer _render = (UnityEngine.SpriteRenderer)translator.GetObject(L, 1, typeof(UnityEngine.SpriteRenderer));
                    
                    GameHelper.DOKillSpriteRenderer( _render );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_DOKillAllSpriteRenderer_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _tParent = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    
                    GameHelper.DOKillAllSpriteRenderer( _tParent );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_DOAction_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    int _actType = LuaAPI.xlua_tointeger(L, 1);
                    float _duration = (float)LuaAPI.lua_tonumber(L, 2);
                    float _delay = (float)LuaAPI.lua_tonumber(L, 3);
                    int _loopTimes = LuaAPI.xlua_tointeger(L, 4);
                    int _easeType = LuaAPI.xlua_tointeger(L, 5);
                    int _isKillBefore = LuaAPI.xlua_tointeger(L, 6);
                    DG.Tweening.TweenCallback _onFinished = translator.GetDelegate<DG.Tweening.TweenCallback>(L, 7);
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 8, typeof(UnityEngine.Transform));
                    System.Collections.Generic.List<float> _tParam = (System.Collections.Generic.List<float>)translator.GetObject(L, 9, typeof(System.Collections.Generic.List<float>));
                    
                    GameHelper.DOAction( _actType, _duration, _delay, _loopTimes, _easeType, _isKillBefore, _onFinished, _trans, _tParam );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_DOActionAll_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    int _actId = LuaAPI.xlua_tointeger(L, 1);
                    int _actType = LuaAPI.xlua_tointeger(L, 2);
                    int _paramType = LuaAPI.xlua_tointeger(L, 3);
                    float _duration = (float)LuaAPI.lua_tonumber(L, 4);
                    float _delay = (float)LuaAPI.lua_tonumber(L, 5);
                    int _loopTimes = LuaAPI.xlua_tointeger(L, 6);
                    int _easeType = LuaAPI.xlua_tointeger(L, 7);
                    int _isKillBefore = LuaAPI.xlua_tointeger(L, 8);
                    DG.Tweening.TweenCallback _onFinished = translator.GetDelegate<DG.Tweening.TweenCallback>(L, 9);
                    System.Collections.Generic.List<UnityEngine.Transform> _tList = (System.Collections.Generic.List<UnityEngine.Transform>)translator.GetObject(L, 10, typeof(System.Collections.Generic.List<UnityEngine.Transform>));
                    System.Collections.Generic.List<float> _paramStart = (System.Collections.Generic.List<float>)translator.GetObject(L, 11, typeof(System.Collections.Generic.List<float>));
                    System.Collections.Generic.List<System.Collections.Generic.List<float>> _paramOne = (System.Collections.Generic.List<System.Collections.Generic.List<float>>)translator.GetObject(L, 12, typeof(System.Collections.Generic.List<System.Collections.Generic.List<float>>));
                    
                    GameHelper.DOActionAll( _actId, _actType, _paramType, _duration, _delay, _loopTimes, _easeType, _isKillBefore, _onFinished, _tList, _paramStart, _paramOne );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_DoActionAll_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    int _actId = LuaAPI.xlua_tointeger(L, 1);
                    int _actType = LuaAPI.xlua_tointeger(L, 2);
                    int _paramType = LuaAPI.xlua_tointeger(L, 3);
                    float _duration = (float)LuaAPI.lua_tonumber(L, 4);
                    float _delay = (float)LuaAPI.lua_tonumber(L, 5);
                    int _loopTimes = LuaAPI.xlua_tointeger(L, 6);
                    int _easeType = LuaAPI.xlua_tointeger(L, 7);
                    int _isKillBefore = LuaAPI.xlua_tointeger(L, 8);
                    DG.Tweening.TweenCallback _onFinished = translator.GetDelegate<DG.Tweening.TweenCallback>(L, 9);
                    UnityEngine.Transform _parent = (UnityEngine.Transform)translator.GetObject(L, 10, typeof(UnityEngine.Transform));
                    System.Collections.Generic.List<float> _paramStart = (System.Collections.Generic.List<float>)translator.GetObject(L, 11, typeof(System.Collections.Generic.List<float>));
                    System.Collections.Generic.List<System.Collections.Generic.List<float>> _paramOne = (System.Collections.Generic.List<System.Collections.Generic.List<float>>)translator.GetObject(L, 12, typeof(System.Collections.Generic.List<System.Collections.Generic.List<float>>));
                    
                    GameHelper.DoActionAll( _actId, _actType, _paramType, _duration, _delay, _loopTimes, _easeType, _isKillBefore, _onFinished, _parent, _paramStart, _paramOne );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetRectTransformWithOffsetAndAnchor_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    float _x = (float)LuaAPI.lua_tonumber(L, 2);
                    float _y = (float)LuaAPI.lua_tonumber(L, 3);
                    float _z = (float)LuaAPI.lua_tonumber(L, 4);
                    float _scaleX = (float)LuaAPI.lua_tonumber(L, 5);
                    float _scaleY = (float)LuaAPI.lua_tonumber(L, 6);
                    float _scaleZ = (float)LuaAPI.lua_tonumber(L, 7);
                    float _offsetMinX = (float)LuaAPI.lua_tonumber(L, 8);
                    float _offsetMinY = (float)LuaAPI.lua_tonumber(L, 9);
                    float _offsetMaxX = (float)LuaAPI.lua_tonumber(L, 10);
                    float _offsetMaxY = (float)LuaAPI.lua_tonumber(L, 11);
                    float _anchorMinX = (float)LuaAPI.lua_tonumber(L, 12);
                    float _anchorMinY = (float)LuaAPI.lua_tonumber(L, 13);
                    float _anchorMaxX = (float)LuaAPI.lua_tonumber(L, 14);
                    float _anchorMaxY = (float)LuaAPI.lua_tonumber(L, 15);
                    
                        UnityEngine.RectTransform gen_ret = GameHelper.GetRectTransformWithOffsetAndAnchor( _go, _x, _y, _z, _scaleX, _scaleY, _scaleZ, _offsetMinX, _offsetMinY, _offsetMaxX, _offsetMaxY, _anchorMinX, _anchorMinY, _anchorMaxX, _anchorMaxY );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetRectTransform_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    
                        UnityEngine.RectTransform gen_ret = GameHelper.GetRectTransform( _go );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetGameObjectRect_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    
                        UnityEngine.Rect gen_ret = GameHelper.GetGameObjectRect( _go );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetGameObjectSize_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    
                        UnityEngine.Vector2 gen_ret = GameHelper.GetGameObjectSize( _go );

                        translator.PushUnityEngineVector2(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetRTPosition_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.RectTransform _rectTrans = (UnityEngine.RectTransform)translator.GetObject(L, 1, typeof(UnityEngine.RectTransform));
                    float _x = (float)LuaAPI.lua_tonumber(L, 2);
                    float _y = (float)LuaAPI.lua_tonumber(L, 3);
                    
                    GameHelper.SetRTPosition( _rectTrans, _x, _y );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetRTPositionX_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.RectTransform _rectTrans = (UnityEngine.RectTransform)translator.GetObject(L, 1, typeof(UnityEngine.RectTransform));
                    float _x = (float)LuaAPI.lua_tonumber(L, 2);
                    
                    GameHelper.SetRTPositionX( _rectTrans, _x );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetRTPositionY_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.RectTransform _rectTrans = (UnityEngine.RectTransform)translator.GetObject(L, 1, typeof(UnityEngine.RectTransform));
                    float _y = (float)LuaAPI.lua_tonumber(L, 2);
                    
                    GameHelper.SetRTPositionY( _rectTrans, _y );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetPosition_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _x = (float)LuaAPI.lua_tonumber(L, 2);
                    float _y = (float)LuaAPI.lua_tonumber(L, 3);
                    float _z = (float)LuaAPI.lua_tonumber(L, 4);
                    
                    GameHelper.SetPosition( _trans, _x, _y, _z );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetLocalPositionAll_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    System.Collections.Generic.List<UnityEngine.Transform> _tList = (System.Collections.Generic.List<UnityEngine.Transform>)translator.GetObject(L, 1, typeof(System.Collections.Generic.List<UnityEngine.Transform>));
                    System.Collections.Generic.List<System.Collections.Generic.List<float>> _tListPos = (System.Collections.Generic.List<System.Collections.Generic.List<float>>)translator.GetObject(L, 2, typeof(System.Collections.Generic.List<System.Collections.Generic.List<float>>));
                    int _specialId = LuaAPI.xlua_tointeger(L, 3);
                    
                    GameHelper.SetLocalPositionAll( _tList, _tListPos, _specialId );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetLocalPositionY_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _y = (float)LuaAPI.lua_tonumber(L, 2);
                    
                    GameHelper.SetLocalPositionY( _trans, _y );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetLocalPosition_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _x = (float)LuaAPI.lua_tonumber(L, 2);
                    float _y = (float)LuaAPI.lua_tonumber(L, 3);
                    float _z = (float)LuaAPI.lua_tonumber(L, 4);
                    
                    GameHelper.SetLocalPosition( _trans, _x, _y, _z );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetGridLayoutGroup_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.RectTransform _trans = (UnityEngine.RectTransform)translator.GetObject(L, 1, typeof(UnityEngine.RectTransform));
                    int _row = LuaAPI.xlua_tointeger(L, 2);
                    
                    GameHelper.SetGridLayoutGroup( _trans, _row );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_ForceRebuildLayoutImmediate_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.RectTransform _trans = (UnityEngine.RectTransform)translator.GetObject(L, 1, typeof(UnityEngine.RectTransform));
                    
                    GameHelper.ForceRebuildLayoutImmediate( _trans );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetRotation_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _x = (float)LuaAPI.lua_tonumber(L, 2);
                    float _y = (float)LuaAPI.lua_tonumber(L, 3);
                    float _z = (float)LuaAPI.lua_tonumber(L, 4);
                    
                    GameHelper.SetRotation( _trans, _x, _y, _z );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetRotationX_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _x = (float)LuaAPI.lua_tonumber(L, 2);
                    
                    GameHelper.SetRotationX( _trans, _x );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetRotationY_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _y = (float)LuaAPI.lua_tonumber(L, 2);
                    
                    GameHelper.SetRotationY( _trans, _y );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetRotationZ_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _z = (float)LuaAPI.lua_tonumber(L, 2);
                    
                    GameHelper.SetRotationZ( _trans, _z );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetLocalRotation_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _x = (float)LuaAPI.lua_tonumber(L, 2);
                    float _y = (float)LuaAPI.lua_tonumber(L, 3);
                    float _z = (float)LuaAPI.lua_tonumber(L, 4);
                    
                    GameHelper.SetLocalRotation( _trans, _x, _y, _z );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetLocalRotationX_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _x = (float)LuaAPI.lua_tonumber(L, 2);
                    
                    GameHelper.SetLocalRotationX( _trans, _x );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetLocalRotationY_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _y = (float)LuaAPI.lua_tonumber(L, 2);
                    
                    GameHelper.SetLocalRotationY( _trans, _y );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetLocalRotationZ_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _z = (float)LuaAPI.lua_tonumber(L, 2);
                    
                    GameHelper.SetLocalRotationZ( _trans, _z );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetLocalRotationGo_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    float _x = (float)LuaAPI.lua_tonumber(L, 2);
                    float _y = (float)LuaAPI.lua_tonumber(L, 3);
                    float _z = (float)LuaAPI.lua_tonumber(L, 4);
                    
                    GameHelper.SetLocalRotationGo( _go, _x, _y, _z );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetLocalRotationXGo_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    float _x = (float)LuaAPI.lua_tonumber(L, 2);
                    
                    GameHelper.SetLocalRotationXGo( _go, _x );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetLocalRotationYGo_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    float _y = (float)LuaAPI.lua_tonumber(L, 2);
                    
                    GameHelper.SetLocalRotationYGo( _go, _y );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetLocalRotationZGo_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    float _z = (float)LuaAPI.lua_tonumber(L, 2);
                    
                    GameHelper.SetLocalRotationZGo( _go, _z );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetScreenPos_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    UnityEngine.Camera _uiCamera = (UnityEngine.Camera)translator.GetObject(L, 2, typeof(UnityEngine.Camera));
                    UnityEngine.RectTransform _uiCanvasRect = (UnityEngine.RectTransform)translator.GetObject(L, 3, typeof(UnityEngine.RectTransform));
                    
                        UnityEngine.Vector3 gen_ret = GameHelper.GetScreenPos( _go, _uiCamera, _uiCanvasRect );

                        translator.PushUnityEngineVector3(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetLocalScale_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _x = (float)LuaAPI.lua_tonumber(L, 2);
                    float _y = (float)LuaAPI.lua_tonumber(L, 3);
                    float _z = (float)LuaAPI.lua_tonumber(L, 4);
                    
                    GameHelper.SetLocalScale( _trans, _x, _y, _z );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetLocalScaleX_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _x = (float)LuaAPI.lua_tonumber(L, 2);
                    
                    GameHelper.SetLocalScaleX( _trans, _x );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetLocalScaleY_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _y = (float)LuaAPI.lua_tonumber(L, 2);
                    
                    GameHelper.SetLocalScaleY( _trans, _y );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetLocalScaleZ_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    float _z = (float)LuaAPI.lua_tonumber(L, 2);
                    
                    GameHelper.SetLocalScaleZ( _trans, _z );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetLocalScaleGo_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    float _x = (float)LuaAPI.lua_tonumber(L, 2);
                    float _y = (float)LuaAPI.lua_tonumber(L, 3);
                    float _z = (float)LuaAPI.lua_tonumber(L, 4);
                    
                    GameHelper.SetLocalScaleGo( _go, _x, _y, _z );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetLocalScaleXGo_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    float _x = (float)LuaAPI.lua_tonumber(L, 2);
                    
                    GameHelper.SetLocalScaleXGo( _go, _x );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetLocalScaleYGo_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    float _y = (float)LuaAPI.lua_tonumber(L, 2);
                    
                    GameHelper.SetLocalScaleYGo( _go, _y );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetLocalScaleZGo_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    float _z = (float)LuaAPI.lua_tonumber(L, 2);
                    
                    GameHelper.SetLocalScaleZGo( _go, _z );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_IsPointerOverGameObject_xlua_st_(RealStatePtr L)
        {
		    try {
            
            
            
                
                {
                    
                        bool gen_ret = GameHelper.IsPointerOverGameObject(  );

                        LuaAPI.lua_pushboolean(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetPointerOverGameObject_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _pressObject;
                    
                        bool gen_ret = GameHelper.GetPointerOverGameObject( out _pressObject );

                        LuaAPI.lua_pushboolean(L, gen_ret);
                    translator.Push(L, _pressObject);
                        
                    
                    
                    
                    return 2;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetActive_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    bool _isActive = LuaAPI.lua_toboolean(L, 2);
                    
                    GameHelper.SetActive( _go, _isActive );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetActiveAll_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    System.Collections.Generic.List<UnityEngine.GameObject> _tList = (System.Collections.Generic.List<UnityEngine.GameObject>)translator.GetObject(L, 1, typeof(System.Collections.Generic.List<UnityEngine.GameObject>));
                    bool _isActive = LuaAPI.lua_toboolean(L, 2);
                    
                    GameHelper.SetActiveAll( _tList, _isActive );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetRecycleAll_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    System.Collections.Generic.List<UnityEngine.Transform> _tList = (System.Collections.Generic.List<UnityEngine.Transform>)translator.GetObject(L, 1, typeof(System.Collections.Generic.List<UnityEngine.Transform>));
                    
                    GameHelper.SetRecycleAll( _tList );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_UpdateBoxColliderSize_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    
                    GameHelper.UpdateBoxColliderSize( _go );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_ColliderIntersects_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go1 = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    UnityEngine.GameObject _go2 = (UnityEngine.GameObject)translator.GetObject(L, 2, typeof(UnityEngine.GameObject));
                    
                        bool gen_ret = GameHelper.ColliderIntersects( _go1, _go2 );

                        LuaAPI.lua_pushboolean(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_ColliderContains_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    UnityEngine.Vector3 _point;translator.Get(L, 2, out _point);
                    
                        bool gen_ret = GameHelper.ColliderContains( _go, _point );

                        LuaAPI.lua_pushboolean(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_PointInbounds_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    UnityEngine.Vector3 _tPos;translator.Get(L, 2, out _tPos);
                    
                        bool gen_ret = GameHelper.PointInbounds( _go, _tPos );

                        LuaAPI.lua_pushboolean(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_RotateVectorWithDegree_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    float _angle = (float)LuaAPI.lua_tonumber(L, 1);
                    UnityEngine.Vector3 _target;translator.Get(L, 2, out _target);
                    UnityEngine.Vector3 _axis;translator.Get(L, 3, out _axis);
                    
                        UnityEngine.Vector3 gen_ret = GameHelper.RotateVectorWithDegree( _angle, _target, _axis );

                        translator.PushUnityEngineVector3(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetMonoUserData_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    
                        UserDataMono gen_ret = GameHelper.GetMonoUserData( _go );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetMonoUserDataInt_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    
                        int gen_ret = GameHelper.GetMonoUserDataInt( _go );

                        LuaAPI.xlua_pushinteger(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetMonoUserDataDouble_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    
                        double gen_ret = GameHelper.GetMonoUserDataDouble( _go );

                        LuaAPI.lua_pushnumber(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetMonoUserDataString_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    
                        string gen_ret = GameHelper.GetMonoUserDataString( _go );

                        LuaAPI.lua_pushstring(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetMonoUserDataParam_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    int _flag = LuaAPI.xlua_tointeger(L, 2);
                    object _obj = translator.GetObject(L, 3, typeof(object));
                    
                    GameHelper.SetMonoUserDataParam( _go, _flag, _obj );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetAnchor_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
			    int gen_param_count = LuaAPI.lua_gettop(L);
            
                if(gen_param_count == 4&& translator.Assignable<UnityEngine.GameObject>(L, 1)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 2)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 3)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 4)) 
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    int _allign = LuaAPI.xlua_tointeger(L, 2);
                    int _offsetX = LuaAPI.xlua_tointeger(L, 3);
                    int _offsetY = LuaAPI.xlua_tointeger(L, 4);
                    
                    GameHelper.SetAnchor( _go, _allign, _offsetX, _offsetY );

                    
                    
                    
                    return 0;
                }
                if(gen_param_count == 3&& translator.Assignable<UnityEngine.GameObject>(L, 1)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 2)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 3)) 
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    int _allign = LuaAPI.xlua_tointeger(L, 2);
                    int _offsetX = LuaAPI.xlua_tointeger(L, 3);
                    
                    GameHelper.SetAnchor( _go, _allign, _offsetX );

                    
                    
                    
                    return 0;
                }
                if(gen_param_count == 2&& translator.Assignable<UnityEngine.GameObject>(L, 1)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 2)) 
                {
                    UnityEngine.GameObject _go = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    int _allign = LuaAPI.xlua_tointeger(L, 2);
                    
                    GameHelper.SetAnchor( _go, _allign );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
            return LuaAPI.luaL_error(L, "invalid arguments to GameHelper.SetAnchor!");
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_AddClick_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Component[] _coms = (UnityEngine.Component[])translator.GetObject(L, 1, typeof(UnityEngine.Component[]));
                    UnityEngine.Events.UnityAction<UnityEngine.GameObject, object> _call = translator.GetDelegate<UnityEngine.Events.UnityAction<UnityEngine.GameObject, object>>(L, 2);
                    
                    GameHelper.AddClick( _coms, _call );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GenerateColors_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
			    int gen_param_count = LuaAPI.lua_gettop(L);
            
                if(gen_param_count == 2&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 1)&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 2)) 
                {
                    int _listNum = LuaAPI.xlua_tointeger(L, 1);
                    int _Offset = LuaAPI.xlua_tointeger(L, 2);
                    
                        System.Collections.Generic.List<UnityEngine.Color> gen_ret = GameHelper.GenerateColors( _listNum, _Offset );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                if(gen_param_count == 1&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 1)) 
                {
                    int _listNum = LuaAPI.xlua_tointeger(L, 1);
                    
                        System.Collections.Generic.List<UnityEngine.Color> gen_ret = GameHelper.GenerateColors( _listNum );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
            return LuaAPI.luaL_error(L, "invalid arguments to GameHelper.GenerateColors!");
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GenerateColorsW_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    
                        System.Collections.Generic.List<UnityEngine.Color> gen_ret = GameHelper.GenerateColorsW(  );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetTimeStamp_xlua_st_(RealStatePtr L)
        {
		    try {
            
            
            
                
                {
                    
                        long gen_ret = GameHelper.GetTimeStamp(  );

                        LuaAPI.lua_pushint64(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_RayTouchedWithoutUI_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Vector3 _screenPos;translator.Get(L, 1, out _screenPos);
                    UnityEngine.Camera _tCamera = (UnityEngine.Camera)translator.GetObject(L, 2, typeof(UnityEngine.Camera));
                    
                        UnityEngine.GameObject gen_ret = GameHelper.RayTouchedWithoutUI( _screenPos, _tCamera );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_IsRayTouchedUIView_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Vector2 _screenPos;translator.Get(L, 1, out _screenPos);
                    UnityEngine.Camera _tCamera = (UnityEngine.Camera)translator.GetObject(L, 2, typeof(UnityEngine.Camera));
                    
                        bool gen_ret = GameHelper.IsRayTouchedUIView( _screenPos, _tCamera );

                        LuaAPI.lua_pushboolean(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_RayTouched3D_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Vector2 _screenPos;translator.Get(L, 1, out _screenPos);
                    UnityEngine.Camera _tCamera = (UnityEngine.Camera)translator.GetObject(L, 2, typeof(UnityEngine.Camera));
                    
                        UnityEngine.GameObject gen_ret = GameHelper.RayTouched3D( _screenPos, _tCamera );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_RayTouched2D_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Vector2 _screenPos;translator.Get(L, 1, out _screenPos);
                    UnityEngine.Camera _tCamera = (UnityEngine.Camera)translator.GetObject(L, 2, typeof(UnityEngine.Camera));
                    
                        UnityEngine.GameObject gen_ret = GameHelper.RayTouched2D( _screenPos, _tCamera );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_RayWorldUGUI_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Vector3 _worldPosition;translator.Get(L, 1, out _worldPosition);
                    UnityEngine.Camera _tCamera = (UnityEngine.Camera)translator.GetObject(L, 2, typeof(UnityEngine.Camera));
                    UnityEngine.UI.GraphicRaycaster _targetGraphic = (UnityEngine.UI.GraphicRaycaster)translator.GetObject(L, 3, typeof(UnityEngine.UI.GraphicRaycaster));
                    
                        System.Collections.Generic.List<UnityEngine.GameObject> gen_ret = GameHelper.RayWorldUGUI( _worldPosition, _tCamera, _targetGraphic );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_RayTouched3DUGUI_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Vector2 _worldPosition;translator.Get(L, 1, out _worldPosition);
                    UnityEngine.Camera _tCamera = (UnityEngine.Camera)translator.GetObject(L, 2, typeof(UnityEngine.Camera));
                    
                        UnityEngine.GameObject gen_ret = GameHelper.RayTouched3DUGUI( _worldPosition, _tCamera );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_MD5String_xlua_st_(RealStatePtr L)
        {
		    try {
            
            
            
                
                {
                    string _str = LuaAPI.lua_tostring(L, 1);
                    
                        string gen_ret = GameHelper.MD5String( _str );

                        LuaAPI.lua_pushstring(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_Base64ToString_xlua_st_(RealStatePtr L)
        {
		    try {
            
            
            
                
                {
                    string _strData = LuaAPI.lua_tostring(L, 1);
                    
                        string gen_ret = GameHelper.Base64ToString( _strData );

                        LuaAPI.lua_pushstring(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetXOR_xlua_st_(RealStatePtr L)
        {
		    try {
            
            
            
                
                {
                    string _strKey = LuaAPI.lua_tostring(L, 1);
                    string _strContent = LuaAPI.lua_tostring(L, 2);
                    
                        string gen_ret = GameHelper.GetXOR( _strKey, _strContent );

                        LuaAPI.lua_pushstring(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_EmailIsMatch_xlua_st_(RealStatePtr L)
        {
		    try {
            
            
            
                
                {
                    string _emailString = LuaAPI.lua_tostring(L, 1);
                    
                        bool gen_ret = GameHelper.EmailIsMatch( _emailString );

                        LuaAPI.lua_pushboolean(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_QuitGame_xlua_st_(RealStatePtr L)
        {
		    try {
            
            
            
                
                {
                    
                    GameHelper.QuitGame(  );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_AddToManagerTrans_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Transform _trans = (UnityEngine.Transform)translator.GetObject(L, 1, typeof(UnityEngine.Transform));
                    
                    GameHelper.AddToManagerTrans( _trans );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_ToLiteralStr_xlua_st_(RealStatePtr L)
        {
		    try {
            
            
            
                
                {
                    string _str = LuaAPI.lua_tostring(L, 1);
                    
                        string gen_ret = GameHelper.ToLiteralStr( _str );

                        LuaAPI.lua_pushstring(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_ResetAnimation_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Animation _anim = (UnityEngine.Animation)translator.GetObject(L, 1, typeof(UnityEngine.Animation));
                    string _animName = LuaAPI.lua_tostring(L, 2);
                    
                    GameHelper.ResetAnimation( _anim, _animName );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_PlayAnimationState_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.Animation _anim = (UnityEngine.Animation)translator.GetObject(L, 1, typeof(UnityEngine.Animation));
                    string _animName = LuaAPI.lua_tostring(L, 2);
                    
                    GameHelper.PlayAnimationState( _anim, _animName );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetWorldCornersByTrans_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.GameObject _obj = (UnityEngine.GameObject)translator.GetObject(L, 1, typeof(UnityEngine.GameObject));
                    
                        UnityEngine.Vector4 gen_ret = GameHelper.GetWorldCornersByTrans( _obj );

                        translator.PushUnityEngineVector4(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_LoadSpriteAsync_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    string _url = LuaAPI.lua_tostring(L, 1);
                    System.Action<UnityEngine.Sprite> _callBack = translator.GetDelegate<System.Action<UnityEngine.Sprite>>(L, 2);
                    
                        System.Collections.IEnumerator gen_ret = GameHelper.LoadSpriteAsync( _url, _callBack );

                        translator.PushAny(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_LoadSpriteAsyncByUrl_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    string _url = LuaAPI.lua_tostring(L, 1);
                    System.Action<UnityEngine.Sprite> _callBack = translator.GetDelegate<System.Action<UnityEngine.Sprite>>(L, 2);
                    
                    GameHelper.LoadSpriteAsyncByUrl( _url, _callBack );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_HMACSHA256Encrypt_xlua_st_(RealStatePtr L)
        {
		    try {
            
            
            
                
                {
                    string _key = LuaAPI.lua_tostring(L, 1);
                    string _value = LuaAPI.lua_tostring(L, 2);
                    
                        string gen_ret = GameHelper.HMACSHA256Encrypt( _key, _value );

                        LuaAPI.lua_pushstring(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_AesEncrypt_xlua_st_(RealStatePtr L)
        {
		    try {
            
            
            
                
                {
                    string _content = LuaAPI.lua_tostring(L, 1);
                    string _key = LuaAPI.lua_tostring(L, 2);
                    string _iv = LuaAPI.lua_tostring(L, 3);
                    
                        string gen_ret = GameHelper.AesEncrypt( _content, _key, _iv );

                        LuaAPI.lua_pushstring(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_AesDecrypt_xlua_st_(RealStatePtr L)
        {
		    try {
            
            
            
                
                {
                    string _content = LuaAPI.lua_tostring(L, 1);
                    string _key = LuaAPI.lua_tostring(L, 2);
                    string _iv = LuaAPI.lua_tostring(L, 3);
                    
                        string gen_ret = GameHelper.AesDecrypt( _content, _key, _iv );

                        LuaAPI.lua_pushstring(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_BytesToString_xlua_st_(RealStatePtr L)
        {
		    try {
            
            
            
                
                {
                    byte[] _data = LuaAPI.lua_tobytes(L, 1);
                    
                        string gen_ret = GameHelper.BytesToString( _data );

                        LuaAPI.lua_pushstring(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetParticleSystemStartSpeed_xlua_st_(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
            
                
                {
                    UnityEngine.ParticleSystem _ps = (UnityEngine.ParticleSystem)translator.GetObject(L, 1, typeof(UnityEngine.ParticleSystem));
                    float _startSpeedMin = (float)LuaAPI.lua_tonumber(L, 2);
                    float _startSpeedMax = (float)LuaAPI.lua_tonumber(L, 3);
                    
                    GameHelper.SetParticleSystemStartSpeed( _ps, _startSpeedMin, _startSpeedMax );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        
        
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _g_get_persistentDataPath(RealStatePtr L)
        {
		    try {
            
			    LuaAPI.lua_pushstring(L, GameHelper.persistentDataPath);
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 1;
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _g_get_s_SharedMatBlock(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			    translator.Push(L, GameHelper.s_SharedMatBlock);
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 1;
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _g_get_hitResult(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			    translator.Push(L, GameHelper.hitResult);
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 1;
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _g_get_grahicList(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			    translator.Push(L, GameHelper.grahicList);
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 1;
        }
        
        
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _s_set_s_SharedMatBlock(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			    GameHelper.s_SharedMatBlock = (UnityEngine.MaterialPropertyBlock)translator.GetObject(L, 1, typeof(UnityEngine.MaterialPropertyBlock));
            
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 0;
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _s_set_hitResult(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			    GameHelper.hitResult = (UnityEngine.RaycastHit2D[])translator.GetObject(L, 1, typeof(UnityEngine.RaycastHit2D[]));
            
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 0;
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _s_set_grahicList(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			    GameHelper.grahicList = (System.Collections.Generic.List<UnityEngine.GameObject>)translator.GetObject(L, 1, typeof(System.Collections.Generic.List<UnityEngine.GameObject>));
            
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 0;
        }
        
		
		
		
		
    }
}
