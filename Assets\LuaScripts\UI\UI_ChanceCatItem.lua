local UI_ChanceCatItem = Class(BaseView)
local ItemBase = require("UI.Common.BaseSlideItem")
local catItem = Class(ItemBase)
local catList = Class(ItemBase)
local maxCount
local selfSlider
local PlayVerAnim = false
local catPerfab
local CatItemShowNums = 2
local oneLineCatMax = 4
--local totalShowCatIndex = 0
local curShowCatListIndex = 1
local curShowCatListPos = 1
local colorEffList = {"lv", "zi", "lan", "huang"};
local Scroll
function UI_ChanceCatItem:OnInit()
	
end

function UI_ChanceCatItem:OnCreate(ispush,param)
	NetKeepCatData:CheckKeepCatRankPush()
	self.data = param
	self.isPush = ispush
	
	self.listData = self:SplitIntoGroups(self.data,3)
	catPerfab = self.m_goItemCat
	maxCount = math.ceil(table.count(self.data)/3)
	Scroll = self.ui.m_goRankScroll
	local transform = self.uiGameObject.transform
	self.animIndex = 1
	self.scroll = transform:Find("mid/m_goRankScroll/ViewPort"):GetComponent(typeof(UEUI.ScrollRect))
	--self.horGridLayout = self.scroll.content:GetComponent(typeof(UEUI.HorizontalLayoutGroup))
	if maxCount <= CatItemShowNums then
		--self.scroll.enabled = false
		--self.horGridLayout.enabled = true
		SetActive(self.ui.m_btnSkip.gameObject,false)
	else
		SetActive(self.ui.m_btnSkip.gameObject,true)
		
	end
	PlayVerAnim = true
	
	selfSlider = require("UI.Common.SlideRect").new()
	selfSlider:Init(self.scroll,2)

	self.catlist = {}
	CollectCardManager:SetMaskSize(self.ui.m_goListCat, self.ui.m_goRankScroll.transform)
	for i = 1, 5 do
		self.catlist[i] = catList.new()
		self.catlist[i]:Init(UEGO.Instantiate(self.ui.m_goListCat.transform))
	end
	selfSlider:SetItems(self.catlist,0,Vector2.New(0,0))
	selfSlider:SetData(self.listData)
	
	if PlayVerAnim then
		SetActive(self.ui.m_imgMask, true);
		self.scroll.vertical = false --horizontal
		TimeMgr:CreateTimer(self, function() self:PlayAnimNext() end, 1, 1)

	end
	--Log.Error()
end

-- 分组函数
function UI_ChanceCatItem:SplitIntoGroups(t, groupSize)
	local groups = {}
	local currentGroup = {}

	for i, v in ipairs(t) do
		table.insert(currentGroup, v)

		if #currentGroup == groupSize then
			table.insert(groups, currentGroup)
			currentGroup = {}
		end
	end

	-- 添加最后一个不完整的组（如果有剩余元素）
	if #currentGroup > 0 then
		table.insert(groups, currentGroup)
	end

	return groups
end

function UI_ChanceCatItem:PlayAnimNext(_index)
	local index = _index or 1
	--for k, v in pairs(self.catlist) do
		--Log.Error("index",v.index,index)
		--if v.index == index then
			
			--return
		--end
	--end
	for k, v in pairs(self.catlist) do
		if v.index == index then
			v:PlayAniNode()
		end
	
		--return
		--end
	end
	--if self.catlist[index] then
		--self.catlist[index]:PlayAniNode()
	--end
end

function UI_ChanceCatItem:OnRefresh(param)
    if param == 1 then
		self:PlayAnimNext(curShowCatListIndex)
	elseif param == 2 then --结束抽奖
		PlayVerAnim = false
		self.scroll.vertical = true
		SetActive(self.ui.m_imgMask, false);
		self.scroll.enabled = true
		SetActive(self.ui.m_btnClose.gameObject,true);
		SetActive(self.ui.m_btnSkip.gameObject,false)
	end
end

function UI_ChanceCatItem:FlyCat(catId,flyNum,onCompleted)
	local oldPos =  Vector3(0,0,0)
	local _,active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.KeepCat)
	local endPos = LimitActivityController:GetActiveGo(active.info.activeId)
	EffectConfig:CreateEffect(136,-80,0,0,endPos.transform)
	endPos = UIMgr:GetUIPosByWorld(endPos.transform.position)
	local flyId = catId
	local num = flyNum
	MapController:FlyUIAnim(oldPos.x,oldPos.y,flyId,num,endPos.x, endPos.y,
		nil,nil,nil,0.7,nil,function ()
			if onCompleted then
				onCompleted()
			end
		end)
end

function UI_ChanceCatItem:onDestroy()
	local catData = NetKeepCatData.data.catList
	local hasNew = false
	for k, v in pairs(catData) do
		if v.new_num > 0 then
			hasNew = true
			self:FlyCat(k,v.new_num)
		end
	end
	if hasNew then
		local _,active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.KeepCat)
		UIMgr:Refresh(UIMgr:GetNowMainUI(), 9, { id = active.info.activeId, state = 2 })
	end
	if self.isPush then
		NetPushViewData:RemoveViewByIndex(PushDefine.UI_ChanceCatItem)
		NetPushViewData:CheckOtherView(true,true)
	end
	KeepCatManager:CheckCatChange()
	--totalShowCatIndex = 0
	curShowCatListIndex = 1
	curShowCatListPos = 1
	Tween.Kill("AutoMoveFunc")
	TimeMgr:DeleteTimer(self)
	selfSlider = nil
end

function UI_ChanceCatItem:onUIEventClick(go,param)
    local name = go.name
	if name == "m_btnClose" then
		self:Close()
	elseif name == "m_btnNext" then
		self:PlayAnimNext(curShowCatListIndex)
	elseif name == "m_btnSkip" then
		Tween.Kill("AutoMoveFunc")
		TimeMgr:DeleteTimer(self)
		for k, v in pairs(self.catlist) do
			for k1, v1 in pairs(v.catList) do
				--v1:Stop()
				if nil ~= v1.data then
					SetActive(v1.trans,true)
				end		
			end
		end
		selfSlider:MoveToIndex(maxCount)
		UI_UPDATE(UIDefine.UI_ChanceCatItem,2)
	end
end

-------------

function catList:PlayAniNode()
	if curShowCatListIndex > maxCount then		
		return
	end
	
	local pos = curShowCatListPos
	if self.data[pos] then
		DOLocalMoveY(self.listCat.transform,0,0.3,function ()
				if not PlayVerAnim then
					return 
				end
				SetActive(self.catList[pos].trans,true) 
				self.catList[pos]:Play()
				self.catList[pos]:SubIndex(self.data[pos])
		end,Ease.InOutSine)	
	else
		UI_UPDATE(UIDefine.UI_ChanceCatItem,2)	
	end
	--DOLocalMoveY(self.aniNode.transform,600,0,function ()
			--SetActive(self.aniNode,true)
			--DOLocalMoveY(self.aniNode.transform,0,0.3,function ()
					--self:SubIndex()
				--end,Ease.InOutSine)
		--end,Ease.InOutSine)
	
	--DOScale(self.aniNode.transform,1.2,0.3,function ()
			--DOScale(self.aniNode.transform,1,0.3,function ()
					--self:SubIndex()
				--end,Ease.InQuad)
		--end,Ease.InQuad)
end
	
function catItem:SubIndex(data)
	local isMove = false
	local catId = data.catId
	local isNew = data.isNew
	local haogandu = data.score

	curShowCatListPos = curShowCatListPos + 1
	if curShowCatListPos > 3 then
		curShowCatListIndex = curShowCatListIndex + 1
		curShowCatListPos = 1
		if curShowCatListIndex > 2 then
			isMove = true
		end
	end
	--totalShowCatIndex = totalShowCatIndex + 1
	--Log.Error("totalShowCatIndex",totalShowCatIndex)
	
	if curShowCatListIndex > maxCount then
		UI_UPDATE(UIDefine.UI_ChanceCatItem,2)
	end
	

	
	if isMove then
		--翻页
		selfSlider:MoveToIndex(curShowCatListIndex-1,0.3,function ()
				if isNew then
					--STOP
					UI_SHOW(UIDefine.UI_NewCat,catId,haogandu)
					--Log.Error("isNew",self.catId)
					return
				else
					UI_UPDATE(UIDefine.UI_ChanceCatItem,1)
				end			
			end)
	else	
		if isNew then
			--STOP
			UI_SHOW(UIDefine.UI_NewCat,catId,haogandu)
			--Log.Error("isNew",self.catId)
			return
		else
			UI_UPDATE(UIDefine.UI_ChanceCatItem,1)
		end		
	end		
end
--------------New--------------------
function catList:OnInit(transform)
	local obj = transform
	self.firstHide = false
	self.trans				= transform
	self.listCat			= GetChild(obj, "listCat")
	self.catList = {}
	for i = 1, 3 do
		local catItemObj = GetChild(self.listCat,"catItem"..i)
		--CollectCardManager:SetMaskSize(catItemObj, Scroll.transform)
		local t = catItem.new()
		t:OnInit(catItemObj)
		SetActive(catItemObj,false)
		table.insert(self.catList,t)
	end
end

function catList:UpdateData(data,index)
	--if self.firstHide then
		--SetActive(self.listCat,false)
	--end
	self.index = index
	self.data = data
	for k, v in pairs(self.catList) do
		if PlayVerAnim then
			SetActive(v.trans,false)
		else
			SetActive(v.trans,true)
		end
		if data[k] then
			v:ItemUpdateData(data[k],k)
			--SetActive(v.trans,true)
		else
			v.data = nil
			SetActive(v.trans,false)
		end
	end
end

function catList:UpdatePosition(vec)
	self.rectTrans.anchoredPosition = vec
end

function catList:GetAnchoredPositon()
	return self.rectTrans.anchoredPosition
end

---------------catItem
-------------
function catItem:OnInit(transform)
	local obj = transform
	self.firstHide = false
	self.trans				= transform
	self.aniNode			= GetChild(obj, "aniNode")
	self.bg 				= GetChild(obj, "aniNode/bg", UEUI.Image)
	self.txt_name			= GetChild(obj, "aniNode/txt_name", UEUI.Text)
	self.catIcon 			= GetChild(obj, "aniNode/catIcon", UEUI.Image)
	self.objNew				= GetChild(obj, "aniNode/new")
	self.qualityTxt			= GetChild(obj, "aniNode/qualityTxt", UEUI.Text)
	self.likeOutline		= GetChild(obj, "aniNode/m_txtAuto90200121", UEUI.Outline)
	self.score				= GetChild(obj, "aniNode/score", UEUI.Text)
	self.effectObj 			= GetChild(obj, "aniNode/effectObj");
end

function catItem:ItemUpdateData(data,index)
	--if not data then return end
	self.data = data
	self.catId = data.catId
	self.haogandu = data.score
	local catConfig = ConfigMgr:GetDataByKey(ConfigDefine.ID.cat_illustrated, "item",v2n(self.catId))
	SetUIImage(self.bg, catConfig.cat_reward_quality, false)
	SetUIImage(self.catIcon, catConfig.cat_reward, false)

	self.txt_name.text = ItemConfig:GetLangByID(catConfig.item);
	self.txt_name.color = GetColorByHex(catConfig.name_color);
	UnifyOutline(self.txt_name.gameObject, catConfig.name_color_edge);

	self.qualityTxt.text = LangMgr:GetLang(90200114 + catConfig.quality);
	self.qualityTxt.color = GetColorByHex(catConfig.quality_color);

	self.likeOutline.effectColor = GetColorByHex(catConfig.favorability_color_edge);

	self.score.text = "+" .. data.score;

	self.isNew = data.isNew
	SetActive(self.objNew,self.isNew)

	local childCount = self.effectObj.transform.childCount;
	local chilid;
	for i = 1, childCount do
		chilid = self.effectObj.transform:GetChild(i - 1);
		SetActive(chilid, catConfig.quality == i);
		--SetActive(chilid, false);
	end
	--self.index = index
	--if not self.firstHide then
		--SetActive(self.aniNode,false)
		--self.firstHide = true
		----SetActive(self.aniNode,false)
	--end
	--if PlayVerAnim then
		--if self.index > curShowCatListIndex then
			--SetActive(self.aniNode,false)
		--end
	--end
end

function catItem:Play()
	local animation = GetComponent(self.aniNode,UE.Animation)
	if animation then
		animation:Play("chancecat")
	end
end

function catItem:Stop()
	--local animation = GetComponent(self.aniNode,UE.Animation)
	--if animation then
		--Log.Error(animation)
		--animation:Stop()
		--animation["chancecat"].speed = 1
	--end
end
return UI_ChanceCatItem