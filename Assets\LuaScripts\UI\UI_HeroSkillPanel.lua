local UI_HeroSkillPanel = Class(BaseView)

function UI_HeroSkillPanel:OnInit()
    EventMgr:Add(EventID.HERO_WINDOW_CLOSE, self.Close, self);
    EventMgr:Add(EventID.UPDATE_HERO_INFO, self.OnUpdatePanel, self);
    EventMgr:Add(EventID.BAG_CHANGE, self.OnUpdateCost, self);
end

function UI_HeroSkillPanel:OnCreate(param)
    local rightBg = GetChild(self.uiGameObject, "rightBg");
    if UIHeight > 1920 then
        SetUIAnchors(rightBg, 0.5, 0, 0.5, 0);
        SetUIPos(rightBg, 0, 750);
        SetUIAnchors(self.ui.m_goTipTarget, 0.5, 0, 0.5, 0);
        SetUIPos(self.ui.m_goTipTarget, -330.2, 845.4);
    else
        SetUIAnchors(rightBg, 0.5, 0.5, 0.5, 0.5);
        SetUIPos(rightBg, 0, -210);
        SetUIAnchors(self.ui.m_goTipTarget, 0.5, 0.5, 0.5, 0.5);
        SetUIPos(self.ui.m_goTipTarget, -330.2, -114.6);
    end
    
    self.heroId = param;
    self.selectIndex = 0;
    self.starCondList = {};
    self:OnUpdatePanel();
end

function UI_HeroSkillPanel:OnRefresh(_type, param)
    if _type == 1 then
        self.heroId = param;
        self.selectIndex = 0;
        self:OnUpdatePanel();
    end
end

function UI_HeroSkillPanel:onDestroy()
    EventMgr:Remove(EventID.HERO_WINDOW_CLOSE, self.Close, self);
    EventMgr:Remove(EventID.UPDATE_HERO_INFO, self.OnUpdatePanel, self);
    EventMgr:Remove(EventID.BAG_CHANGE, self.OnUpdateCost, self);
end

function UI_HeroSkillPanel:onUIEventClick(go,param)
    local name = go.name
    if name == "m_txtType" then
        local showStr = HeroManager:GetSlgGlobalLang(self.skillConfig.skilltype + 3);
        self:OnShowTip(go, showStr)
    elseif name == "m_goTipMask" then
        SetActive(self.ui.m_goTipMask, false);
    elseif name == "skillBg1" or name == "skillBg2" or name == "skillBg3" or name == "skillBg4" then
        local clickType = v2n(string.gsub(name, "skillBg", ""));
        if clickType then
            self:OnSelectSkill(clickType);
        end
    elseif name == "m_btnUpgrade" then
        if not self.heroVo:GetHeroActive() then
            return;
        end
        
        local isUpgrade = self.skillLv < self.heroVo:GetHeroSkillCurMaxLv(self.selectIndex);
        if isUpgrade then
            if self.costId and self.costNum then
                local hasCount = BagManager:GetBagItemCount(self.costId);
                local remainNum = self.costNum - hasCount;
                if remainNum <= 0 then
                    local itemList = {{self.costId, self.costNum}};
                    HeroManager:OnRequestHeroSkillUp(self.heroVo.heroId, self.skillConfig.skill_group, itemList, function(respData)
                        self:OnShowUpLvEff();
                        AudioMgr:Play(122);
                    end);
                else
                    UI_SHOW(UIDefine.UI_SlgGetWay, {{id = self.costId, needNum = self.costNum}});
                end
            end
        else
            local unlockDic = self.heroVo:GetHeroSkillUnlockDic(self.selectIndex);
            if unlockDic and self.heroVo.level < unlockDic[1] then
                UIMgr:Refresh(UIDefine.UI_HeroDevelopWindow, 1, 1);
            else 
                UIMgr:Refresh(UIDefine.UI_HeroDevelopWindow, 1, 3);
            end
        end
    end
    UIMgr:Refresh(UIDefine.UI_HeroDevelopWindow, 3);
end

function UI_HeroSkillPanel:OnUpdatePanel()
    self.heroVo = HeroManager:GetHeroVoById(self.heroId);
    if not self.heroVo then return end

    local isActive = self.heroVo:GetHeroActive();
    local skillObj;
    local skillConfig;
    for i = 1, 4 do
        skillObj = GetChild(self.ui.m_goSkill, "skillBg" .. i);
        skillConfig = isActive and self.heroVo:GetHeroSkillConfig(i) or self.heroVo:GetHeroMaxSkillConfig(i);
        if skillConfig then
            local selectBg = GetChild(skillObj, "selectBg");
            local skillSp = GetChild(skillObj, "skillSp", UEUI.Image);
            local levelTxt = GetChild(skillObj, "levelBg/levelTxt", UEUI.Text);
            local starObj = GetChild(skillObj, "starObj");
            local lockBg = GetChild(skillObj, "lockBg");
            local redDot = GetChild(skillObj, "redDot");
            
            if skillConfig.icon then
                SetUIImage(skillSp, skillConfig.icon, false, function()
                    SetActive(skillSp, true);
                end);
            end
            
            local unLockState = self.heroVo:GetHeroSkillUnlockState(i);
            if isActive then
                local level = self.heroVo:GetHeroSkillLv(i);
                levelTxt.text = level < self.heroVo:GetHeroSkillMaxLv(i) and level or LangMgr:GetLang(58006030);
            else
                levelTxt.text = LangMgr:GetLang(58006030);
            end
            
            local num = self.heroVo:GetHeroSkillCurStar(i);
            for i = 1, 5 do
                local starSp = GetChild(starObj, "star" .. i, UEUI.Image);
                local isShow = isActive and unLockState and i <= num;
                SetActive(starSp, isShow);
            end
            SetActive(lockBg, isActive and not unLockState);
            SetActive(selectBg, self.selectIndex == i);
            SetActive(redDot, isActive and self.heroVo:GetHeroSingleSkillRed(i));
        end
        SetActive(skillObj, skillConfig ~= nil);
    end
    
    if self.selectIndex == 0 then
        self:OnSelectSkill(1);
    else
        self:OnUpdateSkill();
    end
end

function UI_HeroSkillPanel:OnSelectSkill(index)
    if self.selectIndex == index then return end;
    
    local selectBg;
    if self.selectIndex ~= 0 then
        selectBg = GetChild(self.ui.m_goSkill, "skillBg" .. self.selectIndex .. "/selectBg");
        SetActive(selectBg, false);
    end
    if index ~= 0 then
        selectBg = GetChild(self.ui.m_goSkill, "skillBg" .. index .. "/selectBg");
        SetActive(selectBg, true);
    end
    self.selectIndex = index;

    self:OnUpdateSkill();
    self:OnUpdateCost();
end

function UI_HeroSkillPanel:OnUpdateSkill()
    SetActive(self.ui.m_goUpgradeRed, false);
    
    local isActive = self.heroVo:GetHeroActive();
    if isActive then
        self.skillConfig = self.heroVo:GetHeroSkillConfig(self.selectIndex);
        self.skillLv = self.heroVo:GetHeroSkillLv(self.selectIndex);
    else
        self.skillConfig = self.heroVo:GetHeroMaxSkillConfig(self.selectIndex);
        self.skillLv = self.heroVo:GetHeroSkillMaxLv(self.selectIndex);
    end
    
    local unLockState = self.heroVo:GetHeroSkillUnlockState(self.selectIndex);
    if isActive and not unLockState then
        self.ui.m_txtName.text = LangMgr:GetLangFormat(70000148, LangMgr:GetLang(self.skillConfig.name));
        self.ui.m_txtLevel.text = "";
        self.ui.m_txtLimit.text = "";
    else
        self.ui.m_txtName.text = LangMgr:GetLang(self.skillConfig.name);
        self.ui.m_txtLevel.text = LangMgr:GetLang(154) .. self.skillLv;

        local lvLimit = isActive and self.heroVo:GetHeroSkillCurMaxLv(self.selectIndex) or self.skillLv;
        self.ui.m_txtLimit.text = "/" .. lvLimit;
    end
    
    local contentSizeFitter = GetComponent(self.ui.m_txtName, UEUI.ContentSizeFitter);
    if contentSizeFitter then
        local width = self.ui.m_txtName.preferredWidth;
        contentSizeFitter.enabled = width <= 252;
        if width > 252 then
            SetUISize(self.ui.m_txtName, 252, 60);
        end
    end
    
    local cd = self.skillConfig.bout;
    if cd then
        self.ui.m_txtSkillCD.text = LangMgr:GetLangFormat(70000243, GetStrRichColor(cd, "95ff46"));
    else
        self.ui.m_txtSkillCD.text = "";
    end
    self.ui.m_txtType.text = HeroManager:GetSlgGlobalLang(self.skillConfig.skilltype);
    
    local list = self.heroVo:GetHeroSkillExtraList(self.selectIndex);
    local count = #list;
    local num = #self.starCondList;
    local len = count > num and count or num;
    local item;
    for i = 1, len do
        item = self.starCondList[i];
        if not item then
            item = CreateGameObjectWithParent(self.ui.m_goCond, self.ui.m_scrollview.content);
            table.insert(self.starCondList, item);
        end

        if i <= count then
            local starImg = GetChild(item, "starImg", UEUI.Image);
            local lockTxt = GetChild(item, "lockTxt", UEUI.Text);
            
            local data = list[i];
            if self.heroVo.starLv >= data.starLv and isActive and unLockState then
                SetUIImage(starImg, "Sprite/ui_slg_jueseyangcheng/yangcheng_1_s_star3.png", false);
                lockTxt.color = GetColorByHex("95ff46");
            else
                SetUIImage(starImg, "Sprite/ui_slg_jueseyangcheng/yangcheng_1_s_star4.png", false);
                lockTxt.color = GetColorByHex("ffffff");
            end

            if data.langId then
                local strList = string.split(data.showList, "|");
                lockTxt.text = LangMgr:GetLangFormat(data.langId, strList[1], strList[2], strList[3]);
            else
                lockTxt.text = "";
            end
        end
        SetActive(item, i <= count);
    end
    
    local skillCfg = isActive and self.skillConfig or self.heroVo:GetHeroSkillConfig(self.selectIndex);
    self.ui.m_txtSkillDes.text = HeroManager:GetHeroSkillDesc(skillCfg, self.skillLv);
    
    UIRefreshLayout(self.ui.m_goDes);
    UIRefreshLayout(self.ui.m_scrollview.content);
    
    self:OnUpdateCost();
end

function UI_HeroSkillPanel:OnUpdateCost()
    local isActive = self.heroVo:GetHeroActive();
    if isActive then
        local unLockState = self.heroVo:GetHeroSkillUnlockState(self.selectIndex);
        if unLockState then
            local isUpgrade = self.skillLv < self.heroVo:GetHeroSkillCurMaxLv(self.selectIndex);
            if isUpgrade then
                local costDic = self.heroVo:GetHeroSkillLvCost(self.selectIndex);
                if costDic then
                    self.costId = v2n(costDic[1]);
                    self.costNum = v2n(costDic[2]);
                    SetUIImage(self.ui.m_imgCost, ItemConfig:GetIcon(self.costId), false);

                    local hasCount = BagManager:GetBagItemCount(self.costId);
                    local isEnough = hasCount >= self.costNum;
                    if isEnough then
                        self.ui.m_txtCost.text = GetStrRichColor(NumToGameString(hasCount), "95ff46") .. "/" .. NumToGameString(self.costNum);
                    else
                        self.ui.m_txtCost.text = GetStrRichColor(NumToGameString(hasCount), "ff3535") .. "/" .. NumToGameString(self.costNum);
                    end
                    self.ui.m_txtTip.text = "";
                    self.ui.m_txtUpgrade.text = LangMgr:GetLang(58006027);
                    SetUIBtnGrayAndEnable(self.ui.m_btnUpgrade, true, "005DAB");
                    SetActive(self.ui.m_goUpgradeRed, isEnough and self.heroVo:IsShowHeroRed());
                end
            else
                local isMax = self.skillLv >= self.heroVo:GetHeroSkillMaxLv(self.selectIndex);
                if isMax then
                    self.ui.m_txtTip.text = LangMgr:GetLang(58006024); -- 已满级
                    self.ui.m_txtUpgrade.text = LangMgr:GetLang(58006027);
                else
                    -- 提升技能星级可增加等级上限
                    local show_star, skill_maxlv = self.heroVo:GetHeroSkillUpLimit(self.skillLv);
                    self.ui.m_txtTip.text = LangMgr:GetLangFormat(70000250, show_star, skill_maxlv);
                    self.ui.m_txtUpgrade.text = LangMgr:GetLang(58006021);
                end
                SetUIBtnGrayAndEnable(self.ui.m_btnUpgrade, not isMax, "005DAB");
            end
            SetActive(self.ui.m_txtCost, isUpgrade);
        else
            local unlockDic = self.heroVo:GetHeroSkillUnlockDic(self.selectIndex);
            if unlockDic[1] > 0 then
                self.ui.m_txtTip.text = LangMgr:GetLangFormat(58006029, unlockDic[1], unlockDic[2]);
            else
                local showStar = math.floor(unlockDic[2] / 5);
                self.ui.m_txtTip.text = LangMgr:GetLangFormat(70000251, showStar);
            end
            self.ui.m_txtUpgrade.text = LangMgr:GetLang(58006021);
            SetUIBtnGrayAndEnable(self.ui.m_btnUpgrade, true, "005DAB");
            SetActive(self.ui.m_txtCost, false);
        end
    end
    SetActive(self.ui.m_goTip, isActive);
    SetActive(self.ui.m_btnUpgrade, isActive);
end

function UI_HeroSkillPanel:OnShowUpLvEff()
    if self.ui then
        SetActive(self.ui.m_goUpLevel, false);
        SetActive(self.ui.m_goUpLevel, true);
    end
end

function UI_HeroSkillPanel:OnShowTip(go, str)
    local pos = self.uiGameObject.transform:InverseTransformPoint(go.transform.position);
    local halfWidth = self.ui.m_txtType.preferredWidth / 2;
    self.ui.m_goTipTarget.transform.localPosition = Vector3(pos.x + halfWidth, pos.y, 0);
    self.ui.m_txtTipShow.text = str;
    SetActive(self.ui.m_goTipMask, true);
    
    local tipBg = GetChild(self.ui.m_goTipTarget, "tipBg");
    UIRefreshLayout(tipBg);
end

return UI_HeroSkillPanel