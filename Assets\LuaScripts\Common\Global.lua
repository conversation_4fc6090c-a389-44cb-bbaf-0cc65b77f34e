Layer = { ["Default"] = 0, ["TransparentFX"] = 1, ["Ignore Raycast"] = 2, ["Water"] = 4, ["UI"] = 5, ["UI_3D"] = 8 }

UE = CS.UnityEngine
UEGO = UE.GameObject
UEUI = UE.UI
Debug = UE.Debug
LOGMAN = CS.LogMan

LinkImageText = CS.LinkImageText
UIShadow = CS.Coffee.UIEffects.UIShadow

ButtonTrans = UEUI.Selectable.Transition

ScreenWidth = UE.Screen.width
ScreenHeight = UE.Screen.height
UIWidth = CS.StartGame.Instance.Resolution.x
UIHeight = CS.StartGame.Instance.Resolution.y

Tweening = CS.DG.Tweening
TweenAnim = Tweening.DOTweenAnimation
Tween = Tweening.DOTween
TweenPath = Tweening.DOTweenPath

GameUtil = CS.GameHelper
if GameUtil.IsWebGL() then
	-- NetEventMgr = CS.NetworkWebSocketEventMgr
	NetEventMgr = CS.NetworkWebTCPEventMgr
else
	NetEventMgr = CS.NetworkEventMgr
end
SpriteRenderer = UE.SpriteRenderer
Animator = UE.Animator
Animation = UE.Animation
ColorUtility = UE.ColorUtility

GameAniManager = CS.GameAniManager.Instance

--socket_core = require('socket.core')

-- 加载全局模块
require "Common.ToLuaUtil.import"
require "Common.ToLuaUtil.Utils.LuaUtil"
require "Common.ToLuaUtil.Utils.TableUtil"
require "Common.ToLuaUtil.Utils.StringUtil"

--Base
require "Base.Class"
require "Base.Function"
require "Base.GoTools"
require "Base.MathTools"
require "Base.TweenAnimation"
require "Base.TweenEnum"
Log = require "Base.Log"

require "Base.MixTable"

--Common
BaseComponent = require "Common.UI.BaseComponent"
BaseLayer = require "Common.UI.BaseLayer"
BaseScene = require "Common.UI.BaseScene"
BaseView = require "Common.UI.BaseView"
BaseHelpView = require("Common.UI.BaseHelpView")
BaseMapComponent = require "Game.MapLogic.Component.BaseMapComponent"

Mathf = require "Common.Unity.Mathf"
Vector2 = require "Common.Unity.Vector2"
Vector3 = require "Common.Unity.Vector3"
Vector4 = require "Common.Unity.Vector4"
Quaternion = require "Common.Unity.Quaternion"
Color = require "Common.Unity.Color"
Ray = require "Common.Unity.Ray"
Bounds = require "Common.Unity.Bounds"
RaycastHit = require "Common.Unity.RaycastHit"
Touch = require "Common.Unity.Touch"
LayerMask = require "Common.Unity.LayerMask"
Plane = require "Common.Unity.Plane"
Time = require "Common.Unity.Time"
--Serpent = require "Common.serpent"

--Time = require "Common.Unity.Time"

Util = require "Common.XLua.util"
YieldReturn = require "Common.XLua.cs_coroutine".yield_return

rapidjson = require "rapidjson"
Json = require "Common.dkjson"
--Global = require "Common.Global"
PlayerPrefs = require "Common.PlayerPrefs".new()

--Define
AssetDefine = require "Define.AssetDefine"
ConfigDefine = require "Define.ConfigDefine"
EventID = require "Define.EventID"
GameDefine = require "Define.GameDefine"
GameStuffDefine = require "Define.GameStuffDefine"
NetDataDefine = require "Define.NetDataDefine"
PlayerPrefsKey = require "Define.PlayerPrefsKey"
UIDefine = require "Define.UIDefine"
UILayerType = require "Define.UILayerType"
UISceneDefine = require "Define.UISceneDefine"
UIType = require "Define.UIType"
PlayerDefine = require "Define.PlayerDefine"

--require Manager
TouchMonoMgr = require "Manager.TouchMonoManager".new()
EventMgr = require "Manager.EventManager".new()
UIMgr = require "Manager.UIManager".new()
ResMgr = require "Manager.ResourceManager".new()
StorageMgr = require "Manager.StorageManager".new()
UIDMgr = require "Manager.UIDManager".new()
LangMgr = require "Manager.LanguageManager".new()
ConfigMgr = require "Manager.ConfigManager".new()
SceneMgr = require "Manager.SceneManager".new()
TinyGameMgr = require "Manager.TinyGameManager".new()

TimeZoneMgr = require "Manager.TimeZoneManager".new()
TimeMgr = require "Manager.TimeManager".new()
TweenMgr = require "Manager.TweenManager".new()
CurvesMgr = require "Manager.CurvesManager".new()

-- GameMgr = require "Manager.GameManager".new()
SensitiveWordsMgr = require "Manager.SensitiveWordsManager".new()
AudioMgr = require "Manager.AudioManager".new()
SocketMgr = require "Manager.SocketManager".new()
Socket2Mgr = require "Manager.Socket2Manager".new()
MarketMgr = require "Manager.MarketManager".new()
FriendManager = require "Manager.FriendManager".new()
LeagueManager = require "Manager.LeagueManager".new()
LeagueChatManager = require "Manager.LeagueChatManager".new()
BowlingBattleManager = require "Manager.BowlingBattleManager".new()
IntelligentWorkerManager = require "Manager.IntelligentWorkerManager".new()
BattleSceneManager = require "Manager.BattleSceneManager".new()
MailManager = require "Manager.MailManager".new()
HalloweenOreManager = require "Manager.HalloweenOreManager".new()
MonthlySeasonManager = require("Manager.MonthlySeasonManager").new()
SkiingMatchManager = require "Manager.SkiingMatchManager".new()
RedDiamondManager = require("Manager.RedDiamondManager").new()
DailyTargetManager = require("Manager.DailyTargetManager").new()
ThinkingManager = require "Manager.ThinkingManager".new()
RollDiceManager = require "Manager.RollDiceManager".new()
RelicManager = require "Manager.RelicManager".new()
ChangeMapManager = require "Manager.ChangeMapManager".new()
ContinuousRechargeManager = require("Manager.ContinuousRechargeManager").new()
LuckOreManager = require "Manager.LuckOreManager".new()
tgSwitchIDBoxMgr = require "TinyGame.Manager.tgSwitchIDBoxManager".new()
SevenDayManager = require "Manager.SevenDayManager".new()
OneToOneManager = require "Manager.OneToOneManager".new()
LimitActGiftManager = require "Manager.LimitActGiftManager".new()
AthleticTalentManager = require "Manager.AthleticTalentManager".new()
AnnounceManager = require "Manager.AnnounceManager".new()
KeepCatManager = require "Manager.KeepCatManager".new()
NewSkiingManager = require "Manager.NewSkiingManager".new()
CollectCardManager = require "Manager.CollectCardManager".new()
FixedBugManager = require "Manager.FixedBugManager".new()
PolluteManager = require "Manager.PolluteManager".new()
BagManager = require "Manager.BagManager".new()
WenJuanDiaochaMgr = require "Manager.WenJuanDiaochaManager".new()
ChatManager = require "Manager.ChatManager".new()
HeroManager = require "Manager.HeroManager".new()
EquipmentManager = require "Manager.EquipmentManager".new()
DungeonManager = require "Manager.DungeonManager".new()
LotteryManager = require "Manager.LotteryManager".new()
DecorateManager = require "Manager.DecorateManager".new()
TowerManager = require "Manager.TowerManager".new()
TradeWagonsManager = require "Manager.TradeWagonsManager".new()
JJcManager = require "Manager.JJcManager".new()
WorldBossManager = require "Manager.WorldBossManager".new()
BattlePassManager = require "Manager.BattlePassManager".new()
StrongGiftManager = require "Manager.StrongGiftManager".new()
PurchaseManager = require "Manager.PurchaseManager".new()
TopFightManager = require "Manager.TopFightManager".new()
--Module Define
LoginModule = require "Module.LoginModule".new()
EnergyModule = require "Module.EnergyModule".new()
EnergyLotteryModule = require "Module.EnergyLotteryModule".new()
MarketModule = require "Module.MarketModule".new()
CheatCodeModule = require "Module.CheatCodeModule".new()
ADMovieModule = require "Module.ADMovieModule".new()
WhetherModule = require "Module.WhetherModule".new()
GiftPackModule = require "Module.GiftPackModule".new()

--Network
HttpClient = require "Network.HttpClient".new()
HttpServer = require "Network.HttpServer".new()

--require Excel

--other define
SdkHelper = require "Sdk.SdkHelper".new()
--PaymentMgr = require "Sdk.PaymentManager".new()
SeasonIslandMgr = require "Manager.SeasonIslandManager".new()
CombinePopMgr = require "Manager.CombinePopManager".new()
FriendModule = require "Module.FriendModule"
LeagueModule = require "Module.LeagueModule"
HeroModule = require "Module.HeroModule"
BagItemModule = require "Module.BagItemModule"
EquipmentModule = require "Module.EquipmentModule"

--proto net
ProtocolTab = {}
require "Proto.protocol".init(ProtocolTab)
NetMessageEvent = require "Network.NetMessageEvent".new()
ProtocManager = require "Network.ProtocManager".new()
NetPushManager = require "Proto.Handler.NetPushManager".new()
ServerPushManager = require "Manager.ServerPushManager".new()

--RedPoint
RedID = require "Define.RedID"
RedPointMgr = require "Manager.RedPoint.RedPointMgr".new()

ServerDefine = require "Proto.Handler.Generated.Define"

Enable_Guide = true
Enable_GroupAnim = true

ConditionBoxLocking = false