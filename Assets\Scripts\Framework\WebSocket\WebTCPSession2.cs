using System;
using System.IO;
using System.Text;
using Marge.Topia.Gate;
using Network.Message;
using UnityEngine;
using UnityWebSocket;
using WeChatWASM;
using ErrorEventArgs = UnityWebSocket.ErrorEventArgs;

public class WebTCPSession2 : WebTCPSession
{

    
    
    public WebTCPSession2(long id, string host,int port, Action<int, long, int, byte[]> luaCallback):base(id, host,port, luaCallback)
    {
        
    }
    
    public override void SendMessage(int iProtoID, byte[] msg)
    {
        Debug.LogError(m_ConnID + " Web_TCP_SendMessage:" + iProtoID + "_" + msg.Length);
        // m_Socket.Write(msg);
        uint uLen = (uint)msg.Length;

        m_sendbuf.clear();
        m_sendbuf.push_uint(uLen);
        m_sendbuf.push_bytes(msg);
        m_Socket?.Write(m_sendbuf.getBytes());
        // m_sendbuf.erase(0, m_sendbuf.size());
    }

    protected override void Socket_OnMessage(TCPSocketOnMessageListenerResult result)
    {
        Debug.LogError(m_ConnID + " Web_TCP_OnMessage:" + result.message.Length);
        // m_LuaCallback?.Invoke(8, m_ConnID, 0, result.message);
        
        m_recvbuf.clear();
        m_recvbuf.insert(0, result.message, 0, result.message.Length);

        if (m_recvbuf.size() >= 4)
        {
            uint uLen = m_recvbuf.pull_uint();
            
            int iPacketLen = (int)uLen; 
            if (m_recvbuf.size() >= iPacketLen+4)
            {
                byte[] msg = m_recvbuf.pull_bytes(iPacketLen);
                m_recvbuf.erase(0, iPacketLen+4);
                m_recvbuf.reset_pos();

                var cb = CallBack.Descriptor.Parser.ParseFrom(msg) as CallBack;
                Debug.LogError(m_ConnID + " Web_TCP_OnMessage: A_"+(int)cb.Protocol);
                
                m_LuaCallback?.Invoke(8, m_ConnID, 0, msg);
                
            }
            else
            {
                Debug.LogError(m_ConnID + " Web_TCP_OnMessage: ERRORA");
            }
        }
        else
        {
            Debug.LogError(m_ConnID + " Web_TCP_OnMessage: ERRORB");
        }
        
        
        
        
        // uint uLen = m_recvbuf.pull_uint();
        //
        // Debug.LogError(m_ConnID + " Web_TCP_OnMessage: A " + uLen);
        //
        // int iPacketLen = (int)uLen;
        // byte[] msg = m_recvbuf.pull_bytes(iPacketLen);
        //
        // string hex = BitConverter.ToString(msg);
        // Debug.LogError(m_ConnID + " Web_TCP_OnMessage: B " + hex);
        //
        // m_LuaCallback?.Invoke(8, m_ConnID, 0, msg);
        
    }

}
