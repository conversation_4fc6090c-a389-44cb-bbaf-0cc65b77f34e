local UI_HeroDevelopWindowModel = {}

UI_HeroDevelopWindowModel.config = {["name"] = "UI_HeroDevelopWindow", ["layer"] = UILayerType.Normal, ["type"] = UIType.Normal, ["isAutoClose"] = false, ["anim"] = 0,["background"] = 1, ["onEscape"] = false, ["tinyGamePath"] = nil}

function UI_HeroDevelopWindowModel:Init(c)
    c.ui = {}    
    c.ui.m_goShow1 = GetChild(c.uiGameObject,"m_goShow1")
    c.ui.m_goShow2 = GetChild(c.uiGameObject,"m_goShow2")
    c.ui.m_goFight = GetChild(c.uiGameObject,"m_goShow2/m_goFight")
    c.ui.m_txtFight = GetChild(c.uiGameObject,"m_goShow2/m_goFight/m_txtFight",UEUI.Text)
    c.ui.m_imgFight = GetChild(c.uiGameObject,"m_goShow2/m_goFight/m_txtFight/m_imgFight",UEUI.Image)
    c.ui.m_goFightEff = GetChild(c.uiGameObject,"m_goShow2/m_goFight/m_goFightEff")
    c.ui.m_imgHero = GetChild(c.uiGameObject,"m_goShow2/Canvas/heroBg/m_imgHero",UEUI.Image)
    c.ui.m_imgQuality = GetChild(c.uiGameObject,"m_goShow2/Canvas/bg/m_imgQuality",UEUI.Image)
    c.ui.m_txtName = GetChild(c.uiGameObject,"m_goShow2/Canvas/bg/m_txtName",UEUI.Text)
    c.ui.m_imgKind = GetChild(c.uiGameObject,"m_goShow2/Canvas/bg/m_imgKind",UEUI.Image)
    c.ui.m_imgCareer = GetChild(c.uiGameObject,"m_goShow2/Canvas/bg/m_imgCareer",UEUI.Image)
    c.ui.m_btnChangeSkin = GetChild(c.uiGameObject,"m_goShow2/Canvas/m_btnChangeSkin",UEUI.Button)
    c.ui.m_goTogList = GetChild(c.uiGameObject,"m_goTogList")
    c.ui.m_tog1 = GetChild(c.uiGameObject,"m_goTogList/m_tog1",UEUI.Toggle)
    c.ui.m_tog2 = GetChild(c.uiGameObject,"m_goTogList/m_tog2",UEUI.Toggle)
    c.ui.m_tog3 = GetChild(c.uiGameObject,"m_goTogList/m_tog3",UEUI.Toggle)
    c.ui.m_tog4 = GetChild(c.uiGameObject,"m_goTogList/m_tog4",UEUI.Toggle)
    c.ui.m_btnLeft = GetChild(c.uiGameObject,"Canvas/m_btnLeft",UEUI.Button)
    c.ui.m_btnRight = GetChild(c.uiGameObject,"Canvas/m_btnRight",UEUI.Button)
    c.ui.m_goTipMask = GetChild(c.uiGameObject,"Canvas/m_goTipMask")
    c.ui.m_goTipTarget = GetChild(c.uiGameObject,"Canvas/m_goTipMask/m_goTipTarget")
    c.ui.m_txtTipShow = GetChild(c.uiGameObject,"Canvas/m_goTipMask/m_goTipTarget/tipBg/m_txtTipShow",UEUI.Text)
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_HeroDevelopWindowModel