local UI_TradeWagonsCarriage = Class(BaseView)
local GoTradeTrain = require("UI.GoTradeTrain")
local DOTween = CS.DG.Tweening.DOTween

function UI_TradeWagonsCarriage:OnInit()
    SetActive(self.ui.m_goPrefab,false)

    for i = 1, 2 do
        local transform = self.ui["m_goConductorHead"..i].transform
        local headNode = GetChild(transform, "headNode")
        CreateCommonHead(headNode.transform,0.48)
    end

    self.bubbleTime = 0
    self:ShowRefreshCost()
    --self:ShuffleTipList()
    self:ShowMyTip()

    self:SetIsUpdateTick(true)
end

function UI_TradeWagonsCarriage:OnCreate(trainData, page)
    self.trainData = trainData

    if not self.trainData then
        return
    end

    if trainData then
        self.departTime = trainData.depart_timestamp or 0
    else
        self.departTime = 0
    end
    self.isShowingChatBubble = false
    self.passegerHeadItemList = {}

    self.chatBubbleIntervalTime = v2n(TradeWagonsManager:GetTradeSettingConfig(40))
    self.chatBubbleCurTime = self.chatBubbleIntervalTime

    self.closingTime = v2n(TradeWagonsManager:GetTradeSettingConfig(32))
    self.isCloseCarriage = false

    SetActive(self.ui.m_goChatBubble, false)
    SetActive(self.ui.m_goCarriageMsg, false)

    self:InitPageView(1)

    if page then
        self:SwitchPage(page)
        self.ui.m_PageView:pageTo(page, false)
    end

    if not self.goTradeTrain then
        self.goTradeTrain = GoTradeTrain:Create(self.ui.m_goTradeTrain)
    end

    if not self.goTradeTrain2 then
        self.goTradeTrain2 = GoTradeTrain:Create(self.ui.m_goTradeTrain2)
    end

    -- 等待两个火车实例都加载完成
    local function setupTrains()
        local myPlayerInfo = NetUpdatePlayerData:GetPlayerInfo()
        local myCaptain = {
            name = myPlayerInfo.name,
            icon = myPlayerInfo.head,
            border = myPlayerInfo.headBorder
        }

        if self.trainData.captain.id == myPlayerInfo.id then
            self.goTradeTrain:SafeCall("SetConductorInfo", myCaptain)
            self.goTradeTrain2:SafeCall("SetConductorInfo", myCaptain)
            SetActive(self.ui.m_btnEditTeam, true)
        else
            self.goTradeTrain:SafeCall("SetConductorInfo", self.trainData.captain)
            self.goTradeTrain2:SafeCall("SetConductorInfo", self.trainData.captain)
            SetActive(self.ui.m_btnEditTeam, false)
        end

        self.goTradeTrain:SafeCall("CheckGold", self.trainData)
        local isGold = self.goTradeTrain2:SafeCall("CheckGold", self.trainData)
        if isGold then
            SetUISize(self.ui.m_goTradeTrain2, 350, 174)
        else
            SetUISize(self.ui.m_goTradeTrain2, 290, 174)
        end
    end

    -- 等待第一个火车加载完成
    self.goTradeTrain:WaitForLoad(function(trainInstance1)
        -- 等待第二个火车加载完成
        self.goTradeTrain2:WaitForLoad(function(trainInstance2)
            setupTrains()
        end)
    end)

    local tradeIndex = self.trainData.trade_index
    if tradeIndex == 0 then
        tradeIndex = 1
    end
    local typeList = HeroManager:GetBattleTypeListByTrainIndex(tradeIndex)
    HeroManager:RequestPlayerBattleTeamsInfoByType(self.trainData.captain.id, typeList, function (isSuccess, respData)
        if isSuccess then
            if IsTableNotEmpty(respData.teams) then
                -- 按照队伍类型排序
                table.sort(respData.teams, function (a, b)
                    if a.team_type ~= b.team_type then
                        return a.team_type < b.team_type
                    end
                    return false
                end)
                local captainTeamData = {
                    [1] = respData.teams[1].heroes,
                    [2] = respData.teams[2].heroes,
                    [3] = respData.teams[3].heroes,
                }
                self:RefreshTeamInfo(captainTeamData)
            else
                self:RefreshTeamInfo({})
            end
        end
    end)

    SetUIPos(self.goTradeTrain2.go, -455, -9)
    SetUIPos(self.goTradeTrain2.goGold, -503, -9)

    -- SetActive(self.goTradeTrain.border, false)

    local quality = {}
    for _, value in pairs(self.trainData.train_quality) do
        if value.part > 0 then
            quality[value.part] = value.quality
        end
    end
    self.goTradeTrain:SafeCall("SetBodyIconByQualityList", quality)

    self:ShowConductorReward()

    self:ShowConductorInfo(self.ui.m_goConductorHead1)
    self:ShowConductorInfo(self.ui.m_goConductorHead2)

    -- 创建头像
    local head = GetChild(self.ui.m_goChatBubble, "head")
    self.headNode = CreateCommonHead(head, 0.49)
    SetMyHeadAndBorderByGo(self.headNode)

    self:RefreshCarriageInfo(nil, true)

    local remain = self.departTime - TimeMgr:GetServerTime()
    if 0 <= remain and remain <= self.closingTime then
        if not self.isCloseCarriage then
            self.isCloseCarriage = true
            if IsTableNotEmpty(self.btnGetOnList) then
                for _, value in ipairs(self.btnGetOnList) do
                    SetActive(value, false)
                end
            end

            if IsTableNotEmpty(self.waitStatusList) then
                for _, value in ipairs(self.waitStatusList) do
                    SetActive(value.waitObj, false)
                    SetActive(value.readyObj, true)
                end
            end
        end
    end
    EventMgr:Add(EventID.BAG_CHANGE, self.BagChange, self)
    EventMgr:Add(EventID.BATTLE_MANUAL_SAVE, self.BattleManualSave, self)
end

function UI_TradeWagonsCarriage:OnRefresh(type)
    -- 火车出站
    if type == 1 then
        self:Close()
    -- 刷新车厢乘客
    elseif type == 2 then
        self:RefreshCarriageInfo(nil, true)
    elseif type == 3 then
        --队伍阵容变更刷新

    elseif type == 4 then
        --刷新券刷新
        
    end
end

function UI_TradeWagonsCarriage:onDestroy()
    self:SetIsUpdateTick(false)
    if self.sequence then
        self.sequence:Pause()
        self.sequence:Kill()
        self.sequence = nil
    end
    self.curCarriageIndex = nil
    self.goTradeTrain = nil
    self.goTradeTrain2 = nil
    EventMgr:Remove(EventID.BAG_CHANGE, self.BagChange, self)
    EventMgr:Remove(EventID.BATTLE_MANUAL_SAVE, self.BattleManualSave, self)
end

function UI_TradeWagonsCarriage:onUIEventClick(go)
    local name = go.name
    -- 关闭按钮
    if name == "m_btnClose" then
        self:Close()
    -- 帮助按钮
    elseif name == "m_btnHelp" then
        UI_SHOW(UIDefine.UI_TradeWagonsHelp, 70000519)
    -- 刷新火车品质按钮
    elseif name == "m_btnRefresh" then
        self:RefreshTrainQuality()
    -- 编辑队伍按钮
    elseif name == "m_btnEditTeam" then
        local captain = self.trainData.captain
        local myPlayerInfo = NetUpdatePlayerData:GetPlayerInfo()
        if myPlayerInfo.id == captain.id then
            local tradeIndex = self.trainData.trade_index
            if tradeIndex == 0 then
                tradeIndex = 1
            end
            local typeList = HeroManager:GetBattleTypeListByTrainIndex(tradeIndex)
            BattleSceneManager:SetTeamType(typeList[1])
            BattleSceneManager:SetTradeTrainDefendData({["trainIndex"] = tradeIndex})
            BattleSceneManager:ChangeSceneType(BATTLE_SCENE_TYPE.CHOOSE)
        end
    -- 查看车头按钮
    elseif name == "m_btnCheckCarHead" then
        self.ui.m_PageView:pageToNext()
    -- 查看车厢按钮
    elseif name == "m_btnCheckCarCenter" then
        self.ui.m_PageView:pageToLast()
    end
end

function UI_TradeWagonsCarriage:TickUI(deltaTime)
    if not self.departTime then
        return
    end
    local remain = self.departTime - TimeMgr:GetServerTime()
    -- 停止检票 隐藏上车按钮
    if 0 <= remain and remain <= self.closingTime then
        if not self.isCloseCarriage then
            self.isCloseCarriage = true
            if IsTableNotEmpty(self.btnGetOnList) then
                for _, value in ipairs(self.btnGetOnList) do
                    SetActive(value, false)
                end
            end
            TradeWagonsManager:RequestTrainInfo(self.trainData.record_id, function (data)
                self.trainData.passegers_list = data.train_info.passegers_list
                self:RefreshCarriageInfo(nil, true)
            end)
        end
    end

    if remain >= 0 then
        self.ui.m_txtTimer.text = TimeMgr:ConverSecondToString(remain)
    else
        self.ui.m_txtTimer.text = TimeMgr:ConverSecondToString(0)
    end

    if self.chatBubbleCurTime <= 0 then
        self.chatBubbleCurTime = self.chatBubbleIntervalTime

        if self.isCloseCarriage then
            return
        end

        if #self.passegerHeadItemList > 0 then
            local random = math.random(1, #self.passegerHeadItemList)
            local player = self.passegerHeadItemList[random]
            self:ShowChatBubble(player.item, player.data, false)
        end
    else
        self.chatBubbleCurTime = self.chatBubbleCurTime - deltaTime
    end
end

function UI_TradeWagonsCarriage:BagChange(data)
    self:ShowRefreshCost()
end

function UI_TradeWagonsCarriage:BattleManualSave()
    local tradeIndex = self.trainData.trade_index
    if tradeIndex == 0 then
        tradeIndex = 1
    end
    local typeList = HeroManager:GetBattleTypeListByTrainIndex(tradeIndex)
    HeroManager:RequestPlayerBattleTeamsInfoByType(self.trainData.captain.id, typeList, function (isSuccess, respData)
        if isSuccess then
            if IsTableNotEmpty(respData.teams) then
                -- 按照队伍类型排序
                table.sort(respData.teams, function (a, b)
                    if a.team_type ~= b.team_type then
                        return a.team_type < b.team_type
                    end
                    return false
                end)
                local captainTeamData = {
                    [1] = respData.teams[1].heroes,
                    [2] = respData.teams[2].heroes,
                    [3] = respData.teams[3].heroes,
                }
                self:RefreshTeamInfo(captainTeamData)
            else
                self:RefreshTeamInfo({})
            end
        end
    end)
end

-----------------------滑动翻页逻辑-----------------------
--- 初始化翻页列表
--- @param showIndex number 初始页签
function UI_TradeWagonsCarriage:InitPageView(showIndex)
    local view = self.ui.m_PageView
    local maxCount = 2
    local scroll = GetComponent(self.ui.m_PageView.gameObject, UEUI.ScrollRect)
    if maxCount == 0 then
        return
    end
    scroll.enabled = maxCount ~= 1
    view.OnUpdatePageViewCell =  function(index)

    end
    view:CreatePageView(maxCount)
    --页签发生改变
    view.OnPageChanged = function(index)
        self:SwitchPage(index)
    end
end

--滑动切换界面逻辑
function UI_TradeWagonsCarriage:SwitchPage(index)
    if index == 0 then
        --"分配车厢"
        self.ui.m_txtName.text = LangMgr:GetLang(70000490)
    else
        --"列车护卫队"
        self.ui.m_txtName.text = LangMgr:GetLang(70000495)
    end
end

--设置奖励item信息
function UI_TradeWagonsCarriage:SetRewardItemInfo(obj,info)
    local bg = GetChild(obj,"quality",UEUI.Image)
    local icon = GetChild(obj,"icon",UEUI.Image)
    local count = GetChild(obj,"txt",UEUI.Text)
    local btn = GetChild(obj,"btn",UEUI.Button)
    local itemId = info.reward.code
    local itemCount = info.reward.amount

    self:SetItemIcon(itemId,icon)
    self:SetItemQuality(itemId,bg)

    count.text = "x" .. NumToGameString(itemCount)
    RemoveUIComponentEventCallback(btn,UEUI.Button)
    AddUIComponentEventCallback(btn,UEUI.Button,function(arg1,arg2)
        UI_SHOW(UIDefine.UI_ItemTips,itemId)
    end)
end

--- 刷新车厢信息
function UI_TradeWagonsCarriage:RefreshCarriageInfo(notRefreshPlayer, notShowChatBubble)
    self.passegerHeadItemList = {}

    -- 四个位置节点
    self.msgPos = {}
    for i = 1, 4 do
        self.msgPos[i] = GetChild(self.ui.m_goMsgPos, "pos" .. i)
    end

    -- 奖励信息
    local reward = {}
    for _, value in ipairs(self.trainData.rewards_list) do
        if value.train_part > 0 then
            local index = value.train_part
            if not reward[index] then
                reward[index] = value.rewards
            end
        end
    end

    -- 品质信息
    local qualityList = {}
    for _, value in ipairs(self.trainData.train_quality) do
        if value.part > 0 then
            local index = value.part
            if not qualityList[index] then
                qualityList[index] = value.quality
            end
        end
    end

    -- 上车按钮列表
    self.btnGetOnList = {}
    self.waitStatusList = {}

    for k, v in ipairs(self.msgPos) do
        local obj
        -- 有可用的 item 直接获取
        if v.transform.childCount > 0 then
            obj = v.transform:GetChild(0)
        -- item 不够用，创建新的
        else
            obj = CreateGameObjectWithParent(self.ui.m_goCarriageMsg, v.transform)
        end
        obj.transform.localPosition = Vector3.zero
        SetActive(obj, true)

        -- 候车状态
        local waitObj = GetChild(obj, "border/waiting")
        local readyObj = GetChild(obj, "border/ready")

        SetActive(waitObj, not self.isCloseCarriage)
        SetActive(readyObj, self.isCloseCarriage)

        table.insert(self.waitStatusList, {
            waitObj = waitObj,
            readyObj = readyObj
        })

        -- 乘客列表
        local curPeopleNum = 0
        local maxPeopleNum = v2n(TradeWagonsManager:GetTradeSettingConfig(36))
        local passegers
        local playerInfo = NetUpdatePlayerData:GetPlayerInfo()
        local isMeWaiting = false
        for _, value in ipairs(self.trainData.passegers_list) do
            if value.train_part == k then
                curPeopleNum = #value.passegers
                passegers = value.passegers

                for _, passeger in ipairs(value.passegers) do
                    if passeger.id == playerInfo.id then
                        self.curCarriageIndex = value.train_part
                        isMeWaiting = true
                    end
                end
            end
        end

        local count = curPeopleNum
        local readyCount = math.min(curPeopleNum, maxPeopleNum)

        if not notRefreshPlayer then
            -- 设置候车状态
            self:SetWaitStatus(obj, count, isMeWaiting, passegers, notShowChatBubble)
            self:SetReadyStatus(obj, passegers)
        end

        local btnPassenger = GetChild(obj, "btns/peopleNum", UEUI.Button)
        btnPassenger.onClick:RemoveAllListeners()
        btnPassenger.onClick:AddListener(function ()
            UI_SHOW(UIDefine.UI_TradeWagonsTrainPassenger, passegers)
        end)

        -- 乘客数量
        local numTxt = GetChild(obj, "btns/peopleNum/num", UEUI.Text)
        numTxt.text = string.format("%s/%s", curPeopleNum, maxPeopleNum)

        -- 上车按钮
        local btnGetOn = GetChild(obj, "btns/btn", UEUI.Button)
        local imgGetOn = GetChild(obj, "btns/btn", UEUI.Image)

        -- 排队后隐藏加号按钮
        imgGetOn.enabled = true
        if isMeWaiting then
            imgGetOn.enabled = false
        end

        btnGetOn.onClick:RemoveAllListeners()
        btnGetOn.onClick:AddListener(function ()

            if self.trainData.captain.id == playerInfo.id then
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000497))
                return
            end

            if self.curCarriageIndex and self.curCarriageIndex == k then
                UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000510))
                return
            end

            if self.curCarriageIndex then
                local params = {
                    title = 8121,
                    content = 70000510,
                    confirmCallBack = function ()
                        TradeWagonsManager:RequestTrainQueueUp(self.trainData.record_id, k, function (data)
                            self.trainData = TradeWagonsManager:GetTrainDataByID(self.trainData.record_id)
                            self.curCarriageIndex = k
                            self:RefreshCarriageInfo()
                        end)
                    end,
                }
                UI_SHOW(UIDefine.UI_TradeWagonsShareConfirm, 3, params)
            else
                TradeWagonsManager:RequestTrainQueueUp(self.trainData.record_id, k, function (data)
                    self.trainData = TradeWagonsManager:GetTrainDataByID(self.trainData.record_id)
                    self.curCarriageIndex = k
                    self:RefreshCarriageInfo()
                end)
            end
        end)

        table.insert(self.btnGetOnList, btnGetOn)

        local quality = qualityList[k]
        local rewardBg = GetChild(obj, "reward/bg", UEUI.Image)
        local rewardArrow = GetChild(obj, "reward/arrow", UEUI.Image)
        SetUIImage(rewardBg, TradeWagonsManager:GetRewardBgIcon(quality), false)
        SetUIImage(rewardArrow, TradeWagonsManager:GetRewardArrowIcon(quality), false)

        -- 车厢奖励
        local parent = GetChild(obj, "reward/Scroll View/Viewport/Content", UE.Transform)

        if reward[k] then
            local rewardCount = parent.transform.childCount
            -- 先全部隐藏
            for i = 1, rewardCount, 1 do
                local item = parent.transform:GetChild(i - 1)
                SetActive(item, false)
            end

            for index, n in ipairs(reward[k]) do
                local item
                -- 有可用的 item 直接获取
                if index <= rewardCount then
                    item = parent.transform:GetChild(index - 1)
                -- item 不够用，创建新的
                else
                    item = CreateGameObjectWithParent(self.ui.m_goRewardItem, parent)
                end
                self:SetRewardItemInfo(item, n)
                SetActive(item, true)
            end
        end
    end
end

--- 设置候车室等人状态
--- @param obj any 节点
--- @param count number 数量
function UI_TradeWagonsCarriage:SetWaitStatus(obj, count, isMeWaiting, passegers, notShowChatBubble)
    local root = GetChild(obj, "border/waiting/group", UE.Transform)
    local content = root
    local headItemCount = content.transform.childCount
    -- 先全部隐藏
    for i = 1, headItemCount, 1 do
        local item = content.transform:GetChild(i - 1)
        SetActive(item, false)
    end

    local myPlayerInfo = NetUpdatePlayerData:GetPlayerInfo()
    local myItem

    for index, value in ipairs(passegers) do
        local item
        -- 有可用的 item 直接获取
        if index <= headItemCount then
            item = content.transform:GetChild(index - 1)
        -- item 不够用，创建新的
        else
            item = CreateCommonHead(root, 0.3)
        end

        SetActive(item, true)

        if myPlayerInfo.id == value.id then
            myItem = item
            SetHeadAndBorderByGo(item, myPlayerInfo.head, myPlayerInfo.headBorder)
        else
            SetHeadAndBorderByGo(item, value.icon, value.border)

            table.insert(self.passegerHeadItemList, {
                item = item,
                data = value
            })
        end
    end

    headItemCount = content.transform.childCount

    local hasActive = false
    for i = 1, headItemCount, 1 do
        local item = content.transform:GetChild(i - 1)
        local itemActive = item.gameObject.activeSelf
        if itemActive then
            hasActive = true
        end
    end
    local txtTip = GetChild(obj, "border/waiting/tip", UEUI.Text)
    local none = GetChild(obj, "border/waiting/none")
    -- 候车中
    if hasActive then
        -- 我候车中
        if isMeWaiting then
            txtTip.text = LangMgr:GetLang(70000492) .. LangMgr:GetLang(70000493)
            txtTip.color = Color.HexToRGB("D33C00")
            if myItem and not notShowChatBubble then
                self:ShowChatBubble(myItem, nil, true)
            end
        else
            txtTip.text = LangMgr:GetLang(70000492)
            txtTip.color = Color.HexToRGB("0056CA")
        end
        SetActive(none, false)
    -- 无人排队
    else
        txtTip.text = LangMgr:GetLang(70000491)
        txtTip.color = Color.HexToRGB("555555")
        SetActive(none, true)
    end
end

--- 设置候车室准备开车状态
--- @param obj any 节点
--- @param passegers table 乘客列表
function UI_TradeWagonsCarriage:SetReadyStatus(obj, passegers)
    local root = GetChild(obj, "border/ready/group", UE.Transform)
    local content = root
    local headItemCount = content.transform.childCount
    -- 先全部隐藏
    for i = 1, headItemCount, 1 do
        local item = content.transform:GetChild(i - 1)
        SetActive(item, false)
    end

    -- 无人排队
    if passegers == nil or (passegers and #passegers == 0) then
        local txtTip = GetChild(obj, "border/ready/tip", UEUI.Text)
        txtTip.text = LangMgr:GetLang(70000491)
        txtTip.color = Color.HexToRGB("555555")
        local none = GetChild(obj, "border/ready/none")
        SetActive(txtTip, true)
        SetActive(none, true)
        SetActive(root, false)
        return
    end

    if not passegers then return end

    local myPlayerInfo = NetUpdatePlayerData:GetPlayerInfo()
    for index, value in ipairs(passegers) do
        local item
        -- 有可用的 item 直接获取
        if index <= headItemCount then
            item = content.transform:GetChild(index - 1)
        -- item 不够用，创建新的
        else
            item = CreateCommonHead(root, 0.3)
        end

        if myPlayerInfo.id == value.id then
            SetHeadAndBorderByGo(item, myPlayerInfo.head, myPlayerInfo.headBorder)
        else
            SetHeadAndBorderByGo(item, value.icon, value.border)
        end
        SetActive(item, true)
    end
    local txtTip = GetChild(obj, "border/ready/tip", UEUI.Text)
    local none = GetChild(obj, "border/ready/none")
    SetActive(txtTip, false)
    SetActive(none, false)
    SetActive(root, true)
end

--- 显示聊天气泡
--- @param item any 节点
function UI_TradeWagonsCarriage:ShowChatBubble(item, data, isMe)
    if isMe then
        TimeMgr:DestroyTimer(UIDefine.UI_TradeWagonsCarriage, self.chatBubbleTimer)
        self.isShowingChatBubble = false
    end

    if self.isShowingChatBubble then return end

    self.isShowingChatBubble = true

    local pos = UIRectPosFit(item)
    SetUIPos(self.ui.m_goChatBubble, pos[1], pos[2] + 225)
    SetActive(self.ui.m_goChatBubble, true)

    local langID
    if isMe then
        langID = v2n(TradeWagonsManager:GetTradeSettingConfig(39))
    else
        local bubbleStr = TradeWagonsManager:GetTradeSettingConfig(38)
        local bubbleList = string.split(bubbleStr, "|")
        local random = math.random(1, #bubbleList)
        langID = bubbleList[random]
    end

    local txtTip = GetChild(self.ui.m_goChatBubble, "tip", UEUI.Text)
    txtTip.text = LangMgr:GetLang(langID)

    local myPlayerInfo = NetUpdatePlayerData:GetPlayerInfo()
    local txtName = GetChild(self.ui.m_goChatBubble, "name", UEUI.Text)

    local head = GetChild(self.ui.m_goChatBubble, "head")
    local headItem
    -- 有可用的 item 直接获取
    if head.transform.childCount > 0 then
        headItem = head.transform:GetChild(0)
    -- item 不够用，创建新的
    else
        headItem = CreateCommonHead(head, 0.5)
    end

    if isMe then
        txtName.text = myPlayerInfo.name
        SetHeadAndBorderByGo(headItem, myPlayerInfo.head, myPlayerInfo.headBorder)
    else
        if data then
            txtName.text = data.name
            SetHeadAndBorderByGo(headItem, data.icon, data.border)
        end
    end

    self.chatBubbleTimer = TimeMgr:CreateTimer(UIDefine.UI_TradeWagonsCarriage, function ()
        if not self.ui then return end
        SetActive(self.ui.m_goChatBubble, false)
        self.isShowingChatBubble = false
    end, 2, 1)
end

-----------------------车厢奖励-----------------------

--设置道具图标
function UI_TradeWagonsCarriage:SetItemIcon(itemId,icon)
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.item,itemId)
    if config then
        SetUIImage(icon, config.icon_b, false)
    end
end

--设置道具品质底图
function UI_TradeWagonsCarriage:SetItemQuality(itemID, icon)
    local borderIconPath = TradeWagonsManager:GetRewardQualityIcon(ItemConfig:GetSlgQuality(itemID))
    SetUIImage(icon, borderIconPath, false)
end

-----------------------列车长奖励-----------------------
--展示列车长奖励
function UI_TradeWagonsCarriage:ShowConductorReward()
    local parent = GetChild(self.ui.m_goSpecialReward,"Scroll View/Viewport/Content",UE.Transform)
    local reward = {}
    for _, value in ipairs(self.trainData.rewards_list) do
        if value.train_part == TRADE_TRAIN_CARRIAGE.Head then
            reward = value.rewards
        end
    end

    self:DestroyAllChild(parent)
    for _, n in ipairs(reward) do
        local rewardObj = UEGO.Instantiate(self.ui.m_goRewardItem, parent)
        self:SetRewardItemInfo(rewardObj, n)
        SetActive(rewardObj, true)
    end
end

--- 刷新队伍信息
function UI_TradeWagonsCarriage:RefreshTeamInfo(captainTeamData)
    local tradeIndex = self.trainData.trade_index
    if tradeIndex == 0 then
        tradeIndex = 1
    end
    local teamList1, teamList2, teamList3 = HeroManager:GetBattleTeamByTrainIndex(tradeIndex)
    local teamData = { teamList1, teamList2, teamList3 }

    if captainTeamData then
        teamData = captainTeamData
    end

    self.teamPos = {}
    for i = 1, 3 do
        self.teamPos[i] = GetChild(self.ui.m_goSpecialPos, "pos" .. i)
    end

    local captainTotalPower = 0

    for k, v in ipairs(self.teamPos) do
        local obj
        -- 有可用的 item 直接获取
        if v.transform.childCount > 0 then
            obj = v.transform:GetChild(0)
        -- item 不够用，创建新的
        else
            obj = CreateGameObjectWithParent(self.ui.m_goTeamItem, v.transform)
        end
        obj.transform.localPosition = Vector3.zero
        SetActive(obj, true)

        -- 编号
        local index = GetChild(obj, "index", UEUI.Text)
        index.text = k

        if teamData and teamData[k] then
            local teamSingle = teamData[k]
            local totalPower = 0
            for i = 1, 5 do
                local posObj = GetChild(obj, "pos" .. i)
                local imgPos = GetComponent(posObj, UEUI.Image)

                local teamObj
                -- 有可用的 item 直接获取
                if posObj.transform.childCount > 0 then
                    teamObj = posObj.transform:GetChild(0)
                -- item 不够用，创建新的
                else
                    teamObj = CreateGameObjectWithParent(self.ui.m_goTeamSingle, posObj.transform)
                end
                teamObj.transform.localPosition = Vector3.zero
                SetActive(teamObj, true)

                local icon = GetChild(teamObj, "icon", UEUI.Image)
                local txtLevel = GetChild(teamObj, "txt", UEUI.Text)
                local starObj = GetChild(teamObj,"starobj")

                local heroData
                for _, value in ipairs(teamSingle) do
                    if value.pos == i then
                        heroData = value
                    end
                end

                if IsTableNotEmpty(heroData) then
                    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_hero, heroData.code)
                    if config then
                        local hero = HeroModule.new(heroData.code, config)
                        -- 图标
                        local iconPath = hero:GetHeroToyImg()
                        SetUIImage(icon, iconPath, false)
                        SetActive(icon, true)
                        -- 星级
                        local star, order = HeroManager:GetStarOrderByLv(heroData.star)
                        self:SetStar(starObj, star, order)
                        SetActive(starObj, true)
                        -- 等级
                        txtLevel.text = "Lv." .. heroData.level
                        SetActive(txtLevel, true)
                        -- 底框
                        local quality = hero:GetHeroQuality() + 1
                        local borderPath = TradeWagonsManager:GetTeamPosBorderIcon(quality)
                        SetUIImage(imgPos, borderPath, false)
                    end
                    totalPower = totalPower + heroData.power
                else
                    SetActive(icon, false)
                    SetActive(starObj, false)
                    SetActive(txtLevel, false)
                    local borderPath = TradeWagonsManager:GetTeamPosBorderIcon(1)
                    SetUIImage(imgPos, borderPath, false)
                end
            end
            local power = GetChild(obj, "power", UEUI.Text)
            totalPower = TradeWagonsManager:GetBattleListTotalPower(teamData[k])
            power.text = NumToGameString(totalPower)

            captainTotalPower = captainTotalPower + totalPower
        else
            local power = GetChild(obj, "power", UEUI.Text)
            power.text = NumToGameString(0)
        end
    end

    self:RefreshCaptainPower(self.ui.m_goConductorHead1, captainTotalPower)
    self:RefreshCaptainPower(self.ui.m_goConductorHead2, captainTotalPower)
end

--- 展示列车长头像信息
--- @param obj any 节点
function UI_TradeWagonsCarriage:ShowConductorInfo(obj)
    local txtTip = GetChild(obj, "info/tip", UEUI.Text)
    local level = GetChild(obj, "info/level", UEUI.Text)
    local name = GetChild(obj, "name", UEUI.Text)
    local power = GetChild(obj, "info/power", UEUI.Text)
    local headObj = GetChild(obj, "headNode/CustomHead")

    local captain = self.trainData.captain

    local headIcon = 1
    local borderIcon = 103

    local myPlayerInfo = NetUpdatePlayerData:GetPlayerInfo()
    local myLevel = NetUpdatePlayerData:GetLevel()
    if myPlayerInfo.id == captain.id then
        headIcon = myPlayerInfo.head
        borderIcon = myPlayerInfo.headBorder
        txtTip.text = myPlayerInfo.name
        name.text = myPlayerInfo.name
        level.text = "Lv." .. myLevel
        local totalFight = HeroManager.totalFight
        power.text = NumToGameString(totalFight)
    else
        txtTip.text = captain.name
        level.text = "Lv." .. captain.level
        power.text = NumToGameString(captain.power)
    end

    SetHeadAndBorderByGo(headObj, headIcon, borderIcon, function()
        if captain.id and not IsNilOrEmpty(captain.id) then
            FriendManager:ShowPlayerById(captain.id)
        end
    end)
end

--- 刷新列车长的战力
--- @param obj any 父节点
--- @param power number 战力
function UI_TradeWagonsCarriage:RefreshCaptainPower(obj, power)
    local txtPower = GetChild(obj, "info/power", UEUI.Text)
    txtPower.text = NumToGameString(power)
end

--设置星级信息
function UI_TradeWagonsCarriage:SetStar(obj, star, order)
    for i = 1, 5 do
        local img = GetChild(obj, "starSp" .. i, UEUI.Image);
        local index = 0
        if i <= star then
            index = 5
        elseif i == star + 1 then
            index = order
        else
            index = 0
        end
        SetUIImage(img, "Sprite/ui_slg_jueseyangcheng/yangcheng_star"..index..".png", false);
    end
end
-----------------------气泡-----------------------
--每打开一次界面，打乱配置横幅列表
function UI_TradeWagonsCarriage:ShuffleTipList()
    local tipConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_trade_setting,38)
    if not tipConfig then
        return
    end
    
    local configList = Split1(tipConfig.value,"|")
    local sumCount = table.count(configList)
    local list = {}
    for i = 1,sumCount do
        table.insert(list,i)
    end

    local count = #list
    while count > 0 do
        local index = Random(1,count)
        list[count], list[index] = list[index], list[count]
        count = count - 1
    end

    if self.sequence then
        self.sequence:Pause()
        self.sequence:Kill();
        self.sequence = nil;
    end

    local settingConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_trade_setting,40)
    local interval = v2n(settingConfig.value) or 10

    interval = 3
    self.sequence = DOTween.Sequence()
    for k,v in ipairs(list)do
        if k > 1 then
            self.sequence:AppendInterval(interval)
        end
        self.sequence:AppendCallback(function()
            self.ui.m_txtTip.text = LangMgr:GetLang(v2n(configList[v]))
        end)
    end
    self.sequence:SetLoops(-1,LoopType.Restart)
end

--我上车气泡
function UI_TradeWagonsCarriage:ShowMyTip()
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_trade_setting,39)
    if config then
        -- self.ui.m_txtTip.text = LangMgr:GetLang(v2n(config.value))
    end
end

--- 显示刷新品质的消耗数量
function UI_TradeWagonsCarriage:ShowRefreshCost()
    local costStr = TradeWagonsManager:GetTradeSettingConfig(35)
    local costTable = string.split(costStr, "|")

    local costID = v2n(costTable[1])
    local costCount = v2n(costTable[2])

    local ownCount = BagManager:GetBagItemCount(costID)
    local hex = ownCount < costCount and "#FF0000" or "#FFFFFF"
    local ownStr = "<color="..hex..">"..ownCount.."</color>"
    self.ui.m_txtCost.text = ownStr.."/"..costCount
end

--- 是否能刷新品质
--- @return boolean canRefresh 是否能刷新品质
function UI_TradeWagonsCarriage:CanRefreshQuality()
    local costStr = TradeWagonsManager:GetTradeSettingConfig(35)
    local costTable = string.split(costStr, "|")

    local costID = v2n(costTable[1])
    local costCount = v2n(costTable[2])

    local ownCount = BagManager:GetBagItemCount(costID)
    return ownCount >= costCount
end

--销毁指定节点下所有子物体
function UI_TradeWagonsCarriage:DestroyAllChild(trans)
    local childCount = trans.childCount
    if childCount <= 0 then
        return
    end

    for i = childCount-1,0,-1 do
        local child = trans:GetChild(i)
        if child then
            UEGO.Destroy(child.gameObject)
        end
    end
end

--- 刷新火车品质
function UI_TradeWagonsCarriage:RefreshTrainQuality()
    -- 只有列车长才能刷新品质
    local myPlayerInfo = NetUpdatePlayerData:GetPlayerInfo()
    if self.trainData.captain.id ~= myPlayerInfo.id then
        UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000496))
        return
    end

    -- 停止检票了
    if self.isCloseCarriage then
        local minute = math.floor(self.closingTime / 60)
        UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLangFormat(70001066, minute))
        return
    end

    if self:CanRefreshQuality() then
        TradeWagonsManager:RequestTrainQualityRefresh(self.trainData.record_id, function (data)
            self.trainData = data.record
            self:ShowConductorReward()
            self:RefreshCarriageInfo(true)
            local quality = {}
            for _, value in pairs(self.trainData.train_quality) do
                if value.part > 0 then
                    quality[value.part] = value.quality
                end
            end
            self.goTradeTrain:SafeCall("SetBodyIconByQualityList", quality)
            self.goTradeTrain:SafeCall("CheckGold", self.trainData)
            local isGold = self.goTradeTrain2:SafeCall("CheckGold", self.trainData)
            if isGold then
                SetUISize(self.ui.m_goTradeTrain2, 350, 174)
            else
                SetUISize(self.ui.m_goTradeTrain2, 290, 174)
            end
            self:ShowRefreshCost()
            UI_UPDATE(UIDefine.UI_TradeWagonsWindow, 2, self.trainData)
        end)
    else
        UI_SHOW(UIDefine.UI_SlgItemBuyTip, ItemID.TradeContract)
    end
end

return UI_TradeWagonsCarriage