local UI_MultipleDropViewModel = {}

UI_MultipleDropViewModel.config = {["name"] = "UI_MultipleDropView", ["layer"] = UILayerType.Normal, ["type"] = UIType.Normal, ["isAutoClose"] = true, ["anim"] = 1,["background"] = 1,["onEscape"] = false}

function UI_MultipleDropViewModel:Init(c)
    c.ui = {}    
    c.ui.m_btnHelp = GetChild(c.uiGameObject,"bg/m_txtAuto225/m_btnHelp",UEUI.Button)
    c.ui.m_btnClose = GetChild(c.uiGameObject,"bg/m_btnClose",UEUI.Button)
    c.ui.m_imgSelectMultipleBg = GetChild(c.uiGameObject,"bg/m_imgSelectMultipleBg",UEUI.Image)
    c.ui.m_imgDescImg1 = GetChild(c.uiGameObject,"bg/m_imgSelectMultipleBg/obj_left/m_imgDescImg1",UEUI.Image)
    c.ui.m_imgDescImg2 = GetChild(c.uiGameObject,"bg/m_imgSelectMultipleBg/obj_right/m_imgDescImg2",UEUI.Image)
    c.ui.m_goQipao1 = GetChild(c.uiGameObject,"bg/m_imgSelectMultipleBg/obj_right/m_goQipao1")
    c.ui.m_txtDescNum1 = GetChild(c.uiGameObject,"bg/m_imgSelectMultipleBg/obj_right/m_goQipao1/m_txtDescNum1",UEUI.Text)
    c.ui.m_goQipao2 = GetChild(c.uiGameObject,"bg/m_imgSelectMultipleBg/obj_right/m_goQipao2")
    c.ui.m_txtDescNum2 = GetChild(c.uiGameObject,"bg/m_imgSelectMultipleBg/obj_right/m_goQipao2/m_txtDescNum2",UEUI.Text)
    c.ui.m_goQipao3 = GetChild(c.uiGameObject,"bg/m_imgSelectMultipleBg/obj_right/m_goQipao3")
    c.ui.m_txtDescNum3 = GetChild(c.uiGameObject,"bg/m_imgSelectMultipleBg/obj_right/m_goQipao3/m_txtDescNum3",UEUI.Text)
    c.ui.m_goMultipleNum_1 = GetChild(c.uiGameObject,"bg/m_goMultipleNum_1")
    c.ui.m_goMultipleNum_3 = GetChild(c.uiGameObject,"bg/m_goMultipleNum_3")
    c.ui.m_goMultipleNum_9 = GetChild(c.uiGameObject,"bg/m_goMultipleNum_9")
    c.ui.m_goMultipleNum_27 = GetChild(c.uiGameObject,"bg/m_goMultipleNum_27")
    c.ui.m_imgSelectArrow = GetChild(c.uiGameObject,"bg/m_imgSelectArrow",UEUI.Image)
    c.ui.m_btnOne = GetChild(c.uiGameObject,"btns/m_btnOne",UEUI.Button)
    c.ui.m_imgOne = GetChild(c.uiGameObject,"btns/m_btnOne/m_imgOne",UEUI.Image)
    c.ui.m_txtOne = GetChild(c.uiGameObject,"btns/m_btnOne/m_txtOne",UEUI.Text)
    c.ui.m_imgMaskOne = GetChild(c.uiGameObject,"btns/m_btnOne/m_imgMaskOne",UEUI.Image)
    c.ui.m_btnThree = GetChild(c.uiGameObject,"btns/m_btnThree",UEUI.Button)
    c.ui.m_imgThree = GetChild(c.uiGameObject,"btns/m_btnThree/m_imgThree",UEUI.Image)
    c.ui.m_txtThree = GetChild(c.uiGameObject,"btns/m_btnThree/m_txtThree",UEUI.Text)
    c.ui.m_imgMaskThree = GetChild(c.uiGameObject,"btns/m_btnThree/m_imgMaskThree",UEUI.Image)
    c.ui.m_goTimeBg3 = GetChild(c.uiGameObject,"btns/m_btnThree/m_goTimeBg3")
    c.ui.m_txtTime3 = GetChild(c.uiGameObject,"btns/m_btnThree/m_goTimeBg3/m_txtTime3",UEUI.Text)
    c.ui.m_goMultipleThreePoint = GetChild(c.uiGameObject,"btns/m_btnThree/m_goMultipleThreePoint")
    c.ui.m_btnNine = GetChild(c.uiGameObject,"btns/m_btnNine",UEUI.Button)
    c.ui.m_imgNine = GetChild(c.uiGameObject,"btns/m_btnNine/m_imgNine",UEUI.Image)
    c.ui.m_txtNine = GetChild(c.uiGameObject,"btns/m_btnNine/m_txtNine",UEUI.Text)
    c.ui.m_imgMaskNine = GetChild(c.uiGameObject,"btns/m_btnNine/m_imgMaskNine",UEUI.Image)
    c.ui.m_goTimeBg9 = GetChild(c.uiGameObject,"btns/m_btnNine/m_goTimeBg9")
    c.ui.m_txtTime9 = GetChild(c.uiGameObject,"btns/m_btnNine/m_goTimeBg9/m_txtTime9",UEUI.Text)
    c.ui.m_goMultipleNinePoint = GetChild(c.uiGameObject,"btns/m_btnNine/m_goMultipleNinePoint")
    c.ui.m_btnTwentySeven = GetChild(c.uiGameObject,"btns/m_btnTwentySeven",UEUI.Button)
    c.ui.m_imgTwentySeven = GetChild(c.uiGameObject,"btns/m_btnTwentySeven/m_imgTwentySeven",UEUI.Image)
    c.ui.m_txtTwentySeven = GetChild(c.uiGameObject,"btns/m_btnTwentySeven/m_txtTwentySeven",UEUI.Text)
    c.ui.m_imgMaskTwentySeven = GetChild(c.uiGameObject,"btns/m_btnTwentySeven/m_imgMaskTwentySeven",UEUI.Image)
    c.ui.m_goTimeBg27 = GetChild(c.uiGameObject,"btns/m_btnTwentySeven/m_goTimeBg27")
    c.ui.m_txtTime27 = GetChild(c.uiGameObject,"btns/m_btnTwentySeven/m_goTimeBg27/m_txtTime27",UEUI.Text)
    c.ui.m_goMultipleTwentySevenPoint = GetChild(c.uiGameObject,"btns/m_btnTwentySeven/m_goMultipleTwentySevenPoint")
    InitTextLanguage(c.uiGameObject)
    AddUIComponentEvent(c)
end

return UI_MultipleDropViewModel