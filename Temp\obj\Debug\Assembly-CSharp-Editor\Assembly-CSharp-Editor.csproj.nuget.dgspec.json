{"format": 1, "restore": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp-Editor.csproj": {}}, "projects": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.csproj", "projectName": "AppleAuth", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\AppleAuth\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.Editor.csproj", "projectName": "AppleAuth.Editor", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\AppleAuth.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp-Editor-firstpass.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp-Editor-firstpass.csproj", "projectName": "Assembly-<PERSON><PERSON><PERSON>-Editor-first<PERSON>", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp-Editor-firstpass.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Assembly-CSharp-Editor-firstpass\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp-firstpass.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp-firstpass.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Runtime.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Runtime.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UIEffect.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UIEffect.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.Addressables.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.Addressables.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.DOTween.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.DOTween.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.Linq.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.Linq.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.TextMeshPro.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.TextMeshPro.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.2D.Sprite.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.2D.Sprite.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Updater.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Updater.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.PlasticSCM.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.PlasticSCM.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Rider.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Rider.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VisualStudio.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VisualStudio.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VSCode.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VSCode.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Runtime.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Runtime.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Wx.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Wx.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\WxEditor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\WxEditor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp-Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp-Editor.csproj", "projectName": "Assembly-CSharp-Editor", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp-Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Assembly-CSharp-Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp-Editor-firstpass.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp-Editor-firstpass.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp-firstpass.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp-firstpass.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Runtime.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Runtime.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UIEffect.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UIEffect.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.Addressables.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.Addressables.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.DOTween.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.DOTween.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.Linq.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.Linq.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.TextMeshPro.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.TextMeshPro.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.2D.Sprite.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.2D.Sprite.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Updater.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Updater.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.PlasticSCM.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.PlasticSCM.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Rider.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Rider.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VisualStudio.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VisualStudio.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VSCode.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VSCode.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Runtime.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Runtime.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Wx.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Wx.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\WxEditor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\WxEditor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp-firstpass.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp-firstpass.csproj", "projectName": "Assembly-CSharp-firstpass", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp-firstpass.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Assembly-CSharp-firstpass\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Runtime.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Runtime.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UIEffect.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UIEffect.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.Addressables.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.Addressables.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.DOTween.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.DOTween.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.Linq.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.Linq.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.TextMeshPro.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.TextMeshPro.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.2D.Sprite.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.2D.Sprite.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Updater.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Updater.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.PlasticSCM.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.PlasticSCM.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Rider.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Rider.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VisualStudio.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VisualStudio.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VSCode.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VSCode.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Runtime.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Runtime.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Wx.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Wx.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\WxEditor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\WxEditor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Assembly-CSharp\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\AppleAuth.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp-firstpass.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Assembly-CSharp-firstpass.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Runtime.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Runtime.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UIEffect.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UIEffect.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.Addressables.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.Addressables.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.DOTween.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.DOTween.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.Linq.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.Linq.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.TextMeshPro.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.TextMeshPro.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.2D.Sprite.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.2D.Sprite.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Updater.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Updater.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.PlasticSCM.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.PlasticSCM.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Rider.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Rider.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VisualStudio.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VisualStudio.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VSCode.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VSCode.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Runtime.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Runtime.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Wx.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Wx.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\WxEditor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\WxEditor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.csproj", "projectName": "CasualGame.Dreamteck.Splines", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\CasualGame.Dreamteck.Splines\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.Editor.csproj", "projectName": "CasualGame.Dreamteck.Splines.Editor", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\CasualGame.Dreamteck.Splines.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Splines.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.csproj", "projectName": "CasualGame.Dreamteck.Utilities", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\CasualGame.Dreamteck.Utilities\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.Editor.csproj", "projectName": "CasualGame.Dreamteck.Utilities.Editor", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\CasualGame.Dreamteck.Utilities.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\CasualGame.Dreamteck.Utilities.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.csproj", "projectName": "FancyScrollView", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\FancyScrollView\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.Editor.csproj", "projectName": "FancyScrollView.Editor", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\FancyScrollView.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\FancyScrollView.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Editor.csproj", "projectName": "IngameDebugConsole.Editor", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\IngameDebugConsole.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Runtime.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Runtime.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Runtime.csproj", "projectName": "IngameDebugConsole.Runtime", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\IngameDebugConsole.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\IngameDebugConsole.Runtime\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.csproj", "projectName": "Lofelt.NiceVibrations", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Lofelt.NiceVibrations\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.Editor.csproj", "projectName": "Lofelt.NiceVibrations.Editor", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Lofelt.NiceVibrations.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Lofelt.NiceVibrations.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.csproj", "projectName": "Sirenix.OdinInspector.CompatibilityLayer", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Sirenix.OdinInspector.CompatibilityLayer\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.Editor.csproj", "projectName": "Sirenix.OdinInspector.CompatibilityLayer.Editor", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Sirenix.OdinInspector.CompatibilityLayer.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.CompatibilityLayer.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj", "projectName": "Sirenix.OdinInspector.Modules.UnityMathematics", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Sirenix.OdinInspector.Modules.UnityMathematics\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UIEffect.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UIEffect.csproj", "projectName": "UIEffect", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UIEffect.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\UIEffect\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.Addressables.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.Addressables.csproj", "projectName": "UniTask.Addressables", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.Addressables.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\UniTask.Addressables\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.csproj", "projectName": "UniTask", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\UniTask\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.DOTween.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.DOTween.csproj", "projectName": "UniTask.DOTween", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.DOTween.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\UniTask.DOTween\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.Linq.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.Linq.csproj", "projectName": "UniTask.Linq", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.Linq.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\UniTask.Linq\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.TextMeshPro.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.TextMeshPro.csproj", "projectName": "UniTask.TextMeshPro", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.TextMeshPro.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\UniTask.TextMeshPro\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UniTask.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.2D.Sprite.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.2D.Sprite.Editor.csproj", "projectName": "Unity.2D.Sprite.Editor", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.2D.Sprite.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Unity.2D.Sprite.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.csproj", "projectName": "Unity.AI.Navigation", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Unity.AI.Navigation\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.ConversionSystem.csproj", "projectName": "Unity.AI.Navigation.Editor.ConversionSystem", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.ConversionSystem.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Unity.AI.Navigation.Editor.ConversionSystem\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.csproj", "projectName": "Unity.AI.Navigation.Editor", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Unity.AI.Navigation.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Updater.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Updater.csproj", "projectName": "Unity.AI.Navigation.Updater", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Updater.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Unity.AI.Navigation.Updater\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.ConversionSystem.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.ConversionSystem.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.AI.Navigation.Editor.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.csproj", "projectName": "Unity.Mathematics", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Unity.Mathematics\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.Editor.csproj", "projectName": "Unity.Mathematics.Editor", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Unity.Mathematics.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Mathematics.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.PlasticSCM.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.PlasticSCM.Editor.csproj", "projectName": "Unity.PlasticSCM.Editor", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.PlasticSCM.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Unity.PlasticSCM.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Rider.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Rider.Editor.csproj", "projectName": "Unity.Rider.Editor", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Rider.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Unity.Rider.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.csproj", "projectName": "Unity.TextMeshPro", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Unity.TextMeshPro\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.Editor.csproj", "projectName": "Unity.TextMeshPro.Editor", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Unity.TextMeshPro.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.TextMeshPro.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.csproj", "projectName": "Unity.Timeline", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Unity.Timeline\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.Editor.csproj", "projectName": "Unity.Timeline.Editor", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Unity.Timeline.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.Timeline.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VisualStudio.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VisualStudio.Editor.csproj", "projectName": "Unity.VisualStudio.Editor", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VisualStudio.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Unity.VisualStudio.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VSCode.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VSCode.Editor.csproj", "projectName": "Unity.VSCode.Editor", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Unity.VSCode.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Unity.VSCode.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj", "projectName": "UnityEditor.TestRunner", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\UnityEditor.TestRunner\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj", "projectName": "UnityEditor.UI", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\UnityEditor.UI\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj", "projectName": "UnityEngine.TestRunner", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\UnityEngine.TestRunner\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj", "projectName": "UnityEngine.UI", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\UnityEngine.UI\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Editor.csproj", "projectName": "UnityWebSocket.Editor", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\UnityWebSocket.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Runtime.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Runtime.csproj", "projectName": "UnityWebSocket.Runtime", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityWebSocket.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\UnityWebSocket.Runtime\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Wx.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Wx.csproj", "projectName": "Wx", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Wx.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\Wx\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\WxEditor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\WxEditor.csproj", "projectName": "WxEditor", "projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\WxEditor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Temp\\obj\\Debug\\WxEditor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEditor.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.TestRunner.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\UnityEngine.UI.csproj"}, "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Wx.csproj": {"projectPath": "D:\\Project_Merge_Minigame\\Client\\Project_PT\\Wx.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}}}