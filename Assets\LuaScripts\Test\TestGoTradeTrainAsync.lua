-- 测试 GoTradeTrain 异步加载的脚本
local TestGoTradeTrainAsync = {}

-- 模拟测试环境
local function mockEnvironment()
    -- 模拟必要的全局函数和变量
    _G.ResMgr = {
        LoadAssetWithCache = function(path, loadType, callback)
            -- 模拟异步加载，延迟执行回调
            TimeMgr:CreateTimer("MockLoad", function()
                local mockObj = { name = "MockPrefab" }
                callback(mockObj)
            end, 0.1, 1)
        end
    }
    
    _G.CreateGameObjectWithParent = function(obj, parent)
        return { 
            transform = { localPosition = { x = 0, y = 0, z = 0 } },
            name = obj.name .. "_Instance"
        }
    end
    
    _G.SetActive = function(obj, active) end
    _G.GetChild = function(parent, path, componentType) 
        return { text = "MockText" }
    end
    _G.GetComponent = function(obj, componentType)
        return { rect = { width = 100, height = 100 } }
    end
    
    _G.NetUpdatePlayerData = {
        GetPlayerInfo = function()
            return {
                name = "TestPlayer",
                head = 1,
                headBorder = 1
            }
        end
    }
    
    _G.CreateCommonHead = function(parent, scale)
        return { name = "MockHead" }
    end
    
    _G.SetMyHeadAndBorderByGo = function(headNode) end
    _G.SetHeadAndBorderByGo = function(headNode, icon, border) end
    _G.SetUIImage = function(image, sprite, nativeSize) end
    _G.TradeWagonsManager = {
        GetTrainCarriageIcon = function(quality)
            return "MockIcon"
        end
    }
    _G.SLG_QUALITY = { UR = 5 }
    _G.Vector3 = { zero = { x = 0, y = 0, z = 0 } }
    _G.UE = { Animation = "Animation" }
    _G.UEUI = { Image = "Image", Button = "Button", Text = "Text" }
    
    -- 模拟 TimeMgr
    _G.TimeMgr = {
        CreateTimer = function(name, callback, delay, count)
            -- 立即执行回调进行测试
            callback()
        end
    }
end

-- 测试 GoTradeTrain 的异步加载
function TestGoTradeTrainAsync:TestAsyncLoading()
    mockEnvironment()
    
    local GoTradeTrain = require("UI.GoTradeTrain")
    
    print("开始测试 GoTradeTrain 异步加载...")
    
    -- 创建 GoTradeTrain 实例
    local mockParent = { name = "MockParent" }
    local trainInstance = GoTradeTrain:Create(mockParent, {})
    
    -- 测试加载状态检查
    print("初始加载状态:", trainInstance:IsLoadFinished())
    assert(not trainInstance:IsLoadFinished(), "初始状态应该是未加载完成")
    
    -- 测试 WaitForLoad 方法
    local callbackExecuted = false
    trainInstance:WaitForLoad(function(instance)
        print("WaitForLoad 回调被执行")
        callbackExecuted = true
        assert(instance == trainInstance, "回调参数应该是 trainInstance")
        assert(instance:IsLoadFinished(), "回调执行时应该已经加载完成")
    end)
    
    -- 等待一段时间让异步加载完成
    TimeMgr:CreateTimer("TestWait", function()
        print("最终加载状态:", trainInstance:IsLoadFinished())
        assert(trainInstance:IsLoadFinished(), "应该已经加载完成")
        assert(callbackExecuted, "WaitForLoad 回调应该被执行")
        
        -- 测试 SafeCall 方法
        local result = trainInstance:SafeCall("CheckGold", { train_quality = {} })
        print("SafeCall 测试结果:", result)
        
        print("GoTradeTrain 异步加载测试通过!")
    end, 0.5, 1)
end

-- 测试多个回调的情况
function TestGoTradeTrainAsync:TestMultipleCallbacks()
    mockEnvironment()
    
    local GoTradeTrain = require("UI.GoTradeTrain")
    
    print("开始测试多个回调...")
    
    local mockParent = { name = "MockParent" }
    local trainInstance = GoTradeTrain:Create(mockParent, {})
    
    local callback1Executed = false
    local callback2Executed = false
    
    -- 添加第一个回调
    trainInstance:WaitForLoad(function(instance)
        print("第一个回调被执行")
        callback1Executed = true
    end)
    
    -- 添加第二个回调
    trainInstance:WaitForLoad(function(instance)
        print("第二个回调被执行")
        callback2Executed = true
    end)
    
    -- 等待加载完成
    TimeMgr:CreateTimer("TestMultipleWait", function()
        assert(callback1Executed, "第一个回调应该被执行")
        assert(callback2Executed, "第二个回调应该被执行")
        print("多个回调测试通过!")
    end, 0.5, 1)
end

-- 测试已加载完成后添加回调的情况
function TestGoTradeTrainAsync:TestCallbackAfterLoaded()
    mockEnvironment()
    
    local GoTradeTrain = require("UI.GoTradeTrain")
    
    print("开始测试加载完成后添加回调...")
    
    local mockParent = { name = "MockParent" }
    local trainInstance = GoTradeTrain:Create(mockParent, {})
    
    -- 等待加载完成
    TimeMgr:CreateTimer("TestAfterLoadedWait", function()
        -- 在加载完成后添加回调
        local callbackExecuted = false
        trainInstance:WaitForLoad(function(instance)
            print("加载完成后添加的回调被执行")
            callbackExecuted = true
        end)
        
        -- 应该立即执行
        assert(callbackExecuted, "加载完成后添加的回调应该立即执行")
        print("加载完成后添加回调测试通过!")
    end, 0.5, 1)
end

-- 运行所有测试
function TestGoTradeTrainAsync:RunAllTests()
    print("=== 开始 GoTradeTrain 异步加载测试 ===")
    
    self:TestAsyncLoading()
    
    TimeMgr:CreateTimer("TestDelay1", function()
        self:TestMultipleCallbacks()
    end, 1, 1)
    
    TimeMgr:CreateTimer("TestDelay2", function()
        self:TestCallbackAfterLoaded()
    end, 2, 1)
    
    TimeMgr:CreateTimer("TestComplete", function()
        print("=== 所有测试完成 ===")
    end, 3, 1)
end

return TestGoTradeTrainAsync
