local HttpClient = Class()
local HttpInstance = _G.CS.HttpMono.Instance
local GameHttpInstance = _G.CS.GameNetWork.Net.GameHttp.Instance
--local socketTime = require("socket")
local Server_Type = {
    Get_SDK = 1,
    Post_SDK = 2,
    Get_UC = 3,
    Post_GS = 4
}

local Unlock_Key = {
    "/test",
    "/other/timestamp",
    "/dot",
    "/other/cost",
    "/other/ad",
    "/sns/maillist",
}

local HttpServerInfo = {
    ["0"] = {
        ["1"] = {
            url = "http://192.168.1.3:8126",
            channel = "c05ee3cbebf30db3e1d2011181a921a2",
            secret = "51a8c058abc71353fa0088c96e013d14",
            sign = "",
            isPost = 0,
            isGameServer = 0,
        },
        ["2"] = {
            url = "http://192.168.1.3:8126",
            channel = "",
            secret = "",
            sign = "",
            isPost = 1,
            isGameServer = 0
        },
        ["3"] = {
            url = "https://192.168.1.3:8126",
            channel = "2b5aeca005e449963207ae7ae3a6b54a",
            secret = "b2af5572059f3524762a198485a03773",
            sign = "",
            isPost = 0,
            isGameServer = 0
        },
        ["4"] = {
            url = "https://192.168.1.3:8526",
            channel = "",
            secret = "",
            sign = "jiniu2022",
            isPost = 1,
            isGameServer = 1000
        },
        ["socket"] = {
            url = "192.168.1.3",
            port = "8626",
        },
        ["pay"] = {
            url = "/notify2.php"
        }
    },
	["1"] = {
		["1"] = {
			url = "http://42.193.143.131:8126",
			channel = "c05ee3cbebf30db3e1d2011181a921a2",
			secret = "51a8c058abc71353fa0088c96e013d14",
			sign = "",
			isPost = 0,
			isGameServer = 0,
		},
		["2"] = {
			url = "http://42.193.143.131:8126",
			channel = "",
			secret = "",
			sign = "",
			isPost = 1,
			isGameServer = 0
		},
		["3"] = {
			url = "http://42.193.143.131:8126",
			channel = "2b5aeca005e449963207ae7ae3a6b54a",
			secret = "b2af5572059f3524762a198485a03773",
			sign = "",
			isPost = 0,
			isGameServer = 0
		},
		["4"] = {
			url = "http://42.193.143.131:8526",
			channel = "",
			secret = "",
			sign = "jiniu2022",
			isPost = 1,
			isGameServer = 1000
		},
		["socket"] = {
			url = "192.168.1.3",
			port = "8626",
		},
		["pay"] = {
			url = "/notify2.php"
		}
	}
}

function HttpClient:ctor()
end

function HttpClient:Initialize()
    --there are change information
    HttpInstance = _G.CS.HttpMono.Instance
    GameHttpInstance = _G.CS.GameNetWork.Net.GameHttp.Instance
    local serverInf = Json.decode(GameHttpInstance.UrlConfig or "")

    if not serverInf then
        serverInf = DeepCopy(HttpServerInfo[tostring(Game.UrlSelector)])
    end

    self.m_ServerInfo = serverInf
    --C# setting is going to channelInfo.txt
    --for name, id in pairs(Server_Type) do
    --    local k = tostring(id)
    --    local v = serverInf[k]
    --GameHttpInstance:AddUrl(k, v.url, v.channel, v.secret, v.sign, v.isPost + v.isGameServer)
    --end

    self:SetGameVersion("1.0")
    self:SetGameToken("")
    --self:SetLang("10")
end

function HttpClient:GetSocketInf()
    -- if GameUtil.IsWebGL() then
    --     return self.m_ServerInfo.webSocket.url,0
    -- else
        return self.m_ServerInfo.socket.url, self.m_ServerInfo.socket.port
    -- end
end

function HttpClient:GetSocket2Inf()
    -- if GameUtil.IsWebGL() then
    --     if self.m_ServerInfo.webSocket2 then
    --         return self.m_ServerInfo.webSocket2.url,0
    --     end
    -- else
        if self.m_ServerInfo.socket2 then
            return self.m_ServerInfo.socket2.url, self.m_ServerInfo.socket2.port
        end
    -- end
    
    return nil
end

function HttpClient:SetGameToken(strToken)
    self.STR_TOKEN = strToken
    GameHttpInstance.Token = strToken
end

function HttpClient:GetGameToken()
    return self.STR_TOKEN
end

function HttpClient:SetGameVersion(strVersion)
    self.STR_GAMEVERSION = strVersion
    GameHttpInstance.Version = strVersion
end

function HttpClient:GetGameVersion()
    return self.STR_GAMEVERSION
end

function HttpClient:SetLang(lang)
    self.STR_LANG = lang
    GameHttpInstance.Lang = lang
end

function HttpClient:GetLang()
	if not self.STR_LANG then self.STR_LANG = "10" end --默认英语
    return self.STR_LANG
end

------------------------------------------------------------------------------------------------------------------------------

function HttpClient:ShowUIReConnect()
    local function onRetry()
        GameUtil:QuitGame()
    end
	SocketMgr:SetKickedSave(true)
    UI_SHOW(UIDefine.UI_TipsTop, 7062, onRetry, nil, 2)
    SocketMgr:ResetSocket(999)
	Socket2Mgr:ResetSocket(999)
end

function HttpClient:UnlockTouchByNet(strLockKey)
    if TableContainKey(Unlock_Key, strLockKey) then
        return
    end
    UIMgr:SetUILock(false, "net")
end

function HttpClient:LockTouchByNet(strLockKey)
    if TableContainKey(Unlock_Key, strLockKey) then
        return
    end
    UIMgr:SetUILock(true, "net")
end

function HttpClient:NetLog(strLog)
    Log.Info(strLog .. "\n\n\n")
end

function HttpClient:UpdateNetTime(resData)
    if nil == resData then
        return
    end
    if nil ~= resData["info"] then
        local timestamp = resData["info"]["time"]
        local zone = resData["info"]["tzoffset"]
        if timestamp and zone then
            NetDataDefine.SetTimestamp(tonumber(timestamp), tonumber(zone))
        end
    end
end

function HttpClient:HttpSignFields(fields, secretKey)
    local list = {}
    for key, value in pairs(fields) do
        table.insert(list, key)
    end
    table.sort(list)

    local result = ""
    for i = 1, #list do
        local key = list[i]
        local value = fields[key]
        result = string.format("%s%s=%s", result, key, value)
    end
    result = string.format("%s%s", result, secretKey)

    return self:GetMD5(result)
end

function HttpClient:TranslateParams(fields, isGet)
    local paramRes = ""
    local isNotFirst = false
    if isGet then
        for key, value in pairs(fields) do
            local strCombine = key .. "=" .. value
            if isNotFirst then
                strCombine	 = "&" .. strCombine
            end
            isNotFirst = true

            paramRes = paramRes .. strCombine
        end
    else
        paramRes = paramRes .. "{"
        for key, value in pairs(fields) do
            if isNotFirst then
                paramRes = paramRes .. ","
            end
            isNotFirst = true

            paramRes = paramRes .. '"' .. key .. '":"' .. value .. '"'
        end
        paramRes = paramRes .. "}"
    end
    return paramRes
end

------------------------------------------------------------------------------------------

function HttpClient:PopNeedLiteralStr(params)
    if self.m_MapLiteralStr then
        for k, v in pairs(self.m_MapLiteralStr) do
            params[k] = GameUtil.ToLiteralStr(params[k])
        end
        self.m_MapLiteralStr = nil
    end
end

function HttpClient:PushNeedLiteralStr(key)
    self.m_MapLiteralStr = self.m_MapLiteralStr or {}
    self.m_MapLiteralStr[key] = key
end

function HttpClient:HttpGet(serverType, urlName, params, funBack)

    if not Game.IsUseNet then
        funBack()
        return
    end

    if not params then
        params = {}
    end
    params["channel"] = self:GetServerChannel(serverType)
    params["timestamp"] = self:GetTimestamp()
    params["lang"] = self:GetLang()
    params["sig"] = self:HttpSignFields(params, self:GetServerSecret(serverType, false))

    self:PopNeedLiteralStr(params)

    local strParams = self:TranslateParams(params, true)
    local urlGet = self:GetServerUrl(serverType) .. urlName .. "?" .. strParams

    local tFunReqBack = function(errorState, reqCode, strResult)
        --Log.Info("====== BackUC:\t" .. urlName .. "\n" .. strResult)
        self:UnlockTouchByNet(urlName)

        if errorState == 1 then
            if funBack then
                funBack(nil, params)
            end
            Log.Error("###### NetErr : ", urlName)
            return
        end

        local resData = Json.decode(strResult)
        if resData == nil then
            if funBack then
                funBack(nil, params)
            end
            Log.Error("###### json err")
            return
        end
        if type(resData) == "table" then
            self:UpdateNetTime(resData)
        end
        if funBack then
            funBack(resData, params)
        end
    end

    self:LockTouchByNet(urlName)

    --Log.Info("++++++ HttpGet:\t" .. urlName .. "\n" .. urlGet)

    --C# http get
    HttpInstance:HTTPGet(urlGet, tFunReqBack)
end

function HttpClient:HttpPost(serverType, strUrlName, params, funBack,isLock)

    if not Game.IsUseNet then
        if funBack then
            funBack()
        end
        return
    end
	if not isLock then
    	self:LockTouchByNet(strUrlName)
	end

	--local str = NetUpdatePlayerData:GetPlayerID() .. "_map_1"
	--if strUrlName == "/player/updateplayer" then
		--if  Game.JsonMap  then
			--local a =  Game.JsonMap[str]
			--if a then
		 		--CS.FileUtility.SafeWriteAllText("Assets/Resources/Config/testdata2.txt",a);
			--end
		--end
	--end
	--local time = math.floor(socketTime.gettime() * 1000)
    local tFunReqBack = function(errorState, reqCode, strResult)
        self:UnlockTouchByNet(strUrlName)
		 --if strUrlName == "/player/updateplayer" then 
			--local a = Json.decode(strResult)
			--if a and a.data and a.data.playerData and a.data.playerData[str] then 
					--CS.FileUtility.SafeWriteAllText("Assets/Resources/Config/testdata.txt",a.data.playerData[str]);
			--end
	 	--end
        local resData = nil
        if errorState == 0 then
            --Log.Error("====== BackGS:\t" .. strUrlName .. "\n" .. strResult)
            resData = Json.decode(strResult)
            if resData == nil then
                if funBack then
                    funBack(nil, params)
                end
                UI_SHOW(UIDefine.UI_WidgetTip, "err -1000")
                return
            end
        elseif errorState == 1 then
            Log.Error("###### NetErr : ", strUrlName, reqCode, strResult)
            if funBack then
                funBack(nil, params)
            end
            return

        elseif errorState == -11111 then
            --Log.Info("====== BackGSLocal:\t" .. strUrlName .. "\n" .. strResult)
            resData = strResult
            if resData == nil then
                Log.Error("###### local data err")
                return
            end
        end
        if type(resData) == "table" then
            self:UpdateNetTime(resData)

            local info = resData["info"]
            local result = tonumber(info["result"])
            if result == 3 then
                self:ShowUIReConnect()
            end
        end

        if funBack then
			-- if ThinkingManager:CheckUrl(strUrlName) then
			-- 	local delay = math.floor(socketTime.gettime() * 1000) - time
			-- 	ThinkingManager:network_delay(delay,strUrlName)
			-- end
            funBack(resData, params)
        end
    end

    if not params then
        params = {}
    end
    params["timestamp"] = self:GetTimestamp()
    params["lang"] = self:GetLang()
    params["sig"] = self:HttpSignFields(params, self:GetServerSecret(serverType, true))

    self:PopNeedLiteralStr(params)

    local strParams = self:TranslateParams(params, false)
    --Log.Info("++++++ SendGS:\t" .. strUrlName .. "\n" .. strParams)

    local isZip = false
    if strUrlName == "/test" then
        isZip = true
    end

    local urlBase = self:GetServerUrl(serverType)
    local urlPost = urlBase .. strUrlName
    local headers = nil
    if self:IsGameServer(serverType) then
        headers = {}
        headers["X-HTTP-USER-AGENT"] = string.format("%s|%s|%s", self:GetGameVersion(), self:GetGameToken(), self:GetTimestamp())
        headers["Content-Type"] = "application/x-www-form-urlencoded"
    end

	--Log.Info("++++++ SendGSurlPost :\t" .. urlPost  )
	--if headers then 
		--Log.Info("++++++ SendGSurlPost :\t" .. table.dump( headers ))
	--end
	
    --C# http post
    HttpInstance:HTTPPost(urlPost, strParams, headers, tFunReqBack)
end

function HttpClient:SendToSDKGet(strUrlName, params, funBack)
    if not Game.IsUseTCPNet then
        funBack()
        return
    end

    self:HttpGet(1, strUrlName, params, funBack)
end

function HttpClient:SendToSDKPost(strUrlName, params, funBack)
    if not Game.IsUseTCPNet then
        funBack()
        return
    end

    self:HttpGet(2, strUrlName, params, funBack)
end

function HttpClient:SendToUC(strUrlName, params, funBack)
    self:HttpGet(3, strUrlName, params, funBack)
end

function HttpClient:SendToGS(strUrlName, params, funBack,isLock)
    self:HttpPost(4, strUrlName, params, funBack,isLock)
end

--------------------------------------------------------------------------------------------

function HttpClient:SendPayBack(params, funBack)
    self:SendToSDKPost(self.m_ServerInfo.pay.url, params, funBack)
end

function HttpClient:SendGameLog(Type, condition)
    local params = {}
    params["logType"] = Type
    params["logData"] = condition


    local systemInfo = UE.SystemInfo
    params["deviceId"] = systemInfo.deviceUniqueIdentifier
    params["osver"] = systemInfo.operatingSystem
    params["res"] = systemInfo.maxTextureSize
    params["model"] = systemInfo.deviceModel
    params["name"] = UE.Application.identifier
    params["gname"] = systemInfo.graphicsDeviceName
    params["gameResource"] = Game.vers
    params["playerId"] = self:GetPlayerID()

    HttpInstance:SendToUC(999, "/clientlog", params, nil)
end

function HttpClient:SendCloudStorage(key, tbData, funBack)
    local funBack = function(objJson)
        if funBack then
            funBack(objJson)
        end
    end
    local params = {}
    if key then
        params["dataId"] = key
        params[tostring(key)] = GameUtil.ToLiteralStr(tbData)
        params["opt"] = NetDataDefine.HTTP_OPT.UPLOAD
    else
        local strKeys = ""
        for k, v in pairs(tbData) do
            strKeys = strKeys .. k .. "|"
            params[tostring(k)] = GameUtil.ToLiteralStr(v)
        end
        params["dataId"] = strKeys
        params["opt"] = tostring(NetDataDefine.HTTP_OPT.MULTIPLE_UP)
    end
	--Log.Error("=开始=上传存档== ")
    self:SendToGS("/player/updateplayer", params, funBack)
end

--------------------------------------------------------------------------------------------------------
--------------------------------------------------------------------------------------------------------

function HttpClient:GetServerChannel(serverType)
    local inf = self.m_ServerInfo[tostring(serverType)]
	if Game.IsUseSDK then 
		if inf["sdkchannel"] then 
			return  inf["sdkchannel"]
		end
	end
    return inf["channel"]
end

function HttpClient:GetServerSecret(serverType, isPost)
    local inf = self.m_ServerInfo[tostring(serverType)]
    if isPost then
        return inf["sign"]
    else
		if Game.IsUseSDK then
			if inf["sdksecret"] then
				return  inf["sdksecret"]
			end
		end
        return inf["secret"]
    end
end

function HttpClient:GetServerUrl(serverType)
    local inf = self.m_ServerInfo[tostring(serverType)]
    return inf["url"]
end

function HttpClient:IsGameServer(serverType)
    local inf = self.m_ServerInfo[tostring(serverType)]
    return (inf["isGameServer"] >= 1000)
end

function HttpClient:GetNetJsonError(objJson, ignoreData)
    if not Game.IsUseNet then
        return false, 0
    end
    if objJson == nil or objJson["info"] == nil or (not ignoreData and objJson["data"] == nil) then
        return true, -1
    end
    local resCode = tonumber(objJson["info"]["result"])
    if resCode == 0 then
        return false, resCode
    end
    return true, resCode
end

--------------------------------------------------------------------------------------------------------------
---
function HttpClient:GetMD5(str)
    return _G.CS.Rijndael.NetMD5(str)
end

function HttpClient:GetTimestamp()
    return NetDataDefine.GetTimestamp()
end

function HttpClient:GetPlayerID()
    return NetUpdatePlayerData:GetPlayerID()
end

function HttpClient:GetDeviceInfo(name)
    return self.DeviceInfo[name]
end

function HttpClient:UpdateDeviceInfo(params)
    local keys = {
        "deviceUniqueIdentifier",
        "os",
        "net",
        "res",
        "model",
        "device",
        "systemMemorySize",
        "graphicsDeviceName",
        "graphicsDeviceVersion",
        "graphicsMemorySize",
        "maxTextureSize",
    }
    if not self.DeviceInfo then
        local list = {}
        local systemInfo = UE.SystemInfo
        for k, name in pairs(keys) do
            local res = nil
            if name == "deviceUniqueIdentifier" then
                res = systemInfo.deviceUniqueIdentifier

            elseif name == "os" then
                res = systemInfo.operatingSystem

            elseif name == "res" then
                res = ScreenWidth .. "_" .. ScreenHeight

            elseif name == "model" then
                res = systemInfo.deviceModel

            elseif name == "device" then
                res = string.azAZ19(systemInfo.deviceName)

            elseif name == "systemMemorySize" then
                res = systemInfo.systemMemorySize

            elseif name == "graphicsDeviceName" then
                res = systemInfo.graphicsDeviceName

            elseif name == "graphicsDeviceVersion" then
                res = systemInfo.graphicsDeviceVersion

            elseif name == "graphicsMemorySize" then
                res = systemInfo.graphicsMemorySize

            elseif name == "maxTextureSize" then
                res = systemInfo.maxTextureSize
            end
            if res ~= nil then
                list[name] = tostring(res)
            end
        end
        self.DeviceInfo = list
    end

    for k, name in pairs(keys) do
        if params[name] then
            if name == "net" then
                params[name] = UE.Application.internetReachability:ToNum()
            else
                params[name] = self.DeviceInfo[name]
            end
        end
    end
end

return HttpClient
