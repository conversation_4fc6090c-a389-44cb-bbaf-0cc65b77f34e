{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 20044, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 20044, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 20044, "tid": 9341, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 20044, "tid": 9341, "ts": 1758023636891847, "dur": 519, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 20044, "tid": 9341, "ts": 1758023636894512, "dur": 431, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 20044, "tid": 21474836480, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632874165, "dur": 10284, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632884451, "dur": 4003314, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632884458, "dur": 12, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632884471, "dur": 34621, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632919099, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632919103, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632919135, "dur": 6, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632919141, "dur": 1135, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920280, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920305, "dur": 1, "ph": "X", "name": "ProcessMessages 736", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920306, "dur": 18, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920327, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920329, "dur": 27, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920359, "dur": 1, "ph": "X", "name": "ProcessMessages 904", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920360, "dur": 26, "ph": "X", "name": "ReadAsync 904", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920389, "dur": 14, "ph": "X", "name": "ReadAsync 788", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920405, "dur": 12, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920419, "dur": 14, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920435, "dur": 21, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920458, "dur": 11, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920470, "dur": 41, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920516, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920547, "dur": 19, "ph": "X", "name": "ReadAsync 1184", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920568, "dur": 26, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920596, "dur": 1, "ph": "X", "name": "ProcessMessages 891", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920598, "dur": 81, "ph": "X", "name": "ReadAsync 891", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920680, "dur": 25, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920707, "dur": 1, "ph": "X", "name": "ProcessMessages 2804", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920709, "dur": 14, "ph": "X", "name": "ReadAsync 2804", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920725, "dur": 20, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920748, "dur": 1, "ph": "X", "name": "ProcessMessages 575", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920750, "dur": 26, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920777, "dur": 1, "ph": "X", "name": "ProcessMessages 899", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920779, "dur": 13, "ph": "X", "name": "ReadAsync 899", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920794, "dur": 15, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920811, "dur": 10, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920822, "dur": 10, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920835, "dur": 14, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920851, "dur": 10, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920863, "dur": 11, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920879, "dur": 25, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920906, "dur": 12, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920919, "dur": 8, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920928, "dur": 12, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920942, "dur": 14, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920959, "dur": 21, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920982, "dur": 1, "ph": "X", "name": "ProcessMessages 801", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632920984, "dur": 22, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921007, "dur": 11, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921019, "dur": 15, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921036, "dur": 15, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921052, "dur": 14, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921068, "dur": 10, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921080, "dur": 11, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921092, "dur": 12, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921105, "dur": 9, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921116, "dur": 10, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921127, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921140, "dur": 16, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921157, "dur": 10, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921169, "dur": 11, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921181, "dur": 25, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921207, "dur": 11, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921220, "dur": 12, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921233, "dur": 11, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921245, "dur": 11, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921258, "dur": 10, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921270, "dur": 21, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921293, "dur": 10, "ph": "X", "name": "ReadAsync 769", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921304, "dur": 102, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921408, "dur": 1, "ph": "X", "name": "ProcessMessages 2455", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921409, "dur": 11, "ph": "X", "name": "ReadAsync 2455", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921421, "dur": 9, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921432, "dur": 9, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921443, "dur": 12, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921456, "dur": 9, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921467, "dur": 16, "ph": "X", "name": "ReadAsync 290", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921483, "dur": 3, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921488, "dur": 15, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921504, "dur": 14, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921520, "dur": 12, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921533, "dur": 27, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921563, "dur": 17, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921581, "dur": 4, "ph": "X", "name": "ReadAsync 1029", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921587, "dur": 71, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921662, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921698, "dur": 17, "ph": "X", "name": "ReadAsync 1806", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921718, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921720, "dur": 63, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921787, "dur": 28, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921817, "dur": 1, "ph": "X", "name": "ProcessMessages 1075", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921819, "dur": 9, "ph": "X", "name": "ReadAsync 1075", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921830, "dur": 5, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921836, "dur": 2, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921840, "dur": 5, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921845, "dur": 19, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921867, "dur": 46, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921916, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921932, "dur": 11, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921944, "dur": 12, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921957, "dur": 7, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921965, "dur": 13, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632921979, "dur": 17, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922002, "dur": 19, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922024, "dur": 75, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922100, "dur": 24, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922126, "dur": 1, "ph": "X", "name": "ProcessMessages 2499", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922127, "dur": 34, "ph": "X", "name": "ReadAsync 2499", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922162, "dur": 16, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922181, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922182, "dur": 17, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922201, "dur": 12, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922215, "dur": 30, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922247, "dur": 11, "ph": "X", "name": "ReadAsync 1060", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922260, "dur": 10, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922271, "dur": 11, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922285, "dur": 17, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922304, "dur": 14, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922319, "dur": 10, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922331, "dur": 11, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922345, "dur": 9, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922355, "dur": 25, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922382, "dur": 34, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922417, "dur": 17, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922435, "dur": 9, "ph": "X", "name": "ReadAsync 1015", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922445, "dur": 11, "ph": "X", "name": "ReadAsync 175", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922458, "dur": 8, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922468, "dur": 22, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922493, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922514, "dur": 70, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922587, "dur": 14, "ph": "X", "name": "ReadAsync 1658", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922604, "dur": 19, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922625, "dur": 15, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922642, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922644, "dur": 17, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922663, "dur": 12, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922676, "dur": 69, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922748, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922750, "dur": 22, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922774, "dur": 10, "ph": "X", "name": "ReadAsync 1805", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922785, "dur": 10, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922797, "dur": 70, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922869, "dur": 1, "ph": "X", "name": "ProcessMessages 259", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922871, "dur": 35, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922907, "dur": 1, "ph": "X", "name": "ProcessMessages 2751", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632922909, "dur": 105, "ph": "X", "name": "ReadAsync 2751", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923015, "dur": 27, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923043, "dur": 1, "ph": "X", "name": "ProcessMessages 2838", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923045, "dur": 17, "ph": "X", "name": "ReadAsync 2838", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923063, "dur": 11, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923076, "dur": 66, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923146, "dur": 24, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923172, "dur": 10, "ph": "X", "name": "ReadAsync 1876", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923184, "dur": 12, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923197, "dur": 10, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923208, "dur": 9, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923219, "dur": 12, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923234, "dur": 29, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923265, "dur": 9, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923275, "dur": 12, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923288, "dur": 8, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923298, "dur": 10, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923310, "dur": 13, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923325, "dur": 12, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923338, "dur": 13, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923352, "dur": 10, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923364, "dur": 13, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923379, "dur": 8, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923388, "dur": 10, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923399, "dur": 6, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923406, "dur": 8, "ph": "X", "name": "ReadAsync 9", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923416, "dur": 25, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923446, "dur": 14, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923461, "dur": 16, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923479, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923556, "dur": 1, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923557, "dur": 30, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923589, "dur": 1, "ph": "X", "name": "ProcessMessages 1817", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923590, "dur": 11, "ph": "X", "name": "ReadAsync 1817", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923602, "dur": 10, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923613, "dur": 10, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923625, "dur": 10, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923636, "dur": 10, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923649, "dur": 13, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923663, "dur": 16, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923681, "dur": 11, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923693, "dur": 11, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923705, "dur": 12, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923718, "dur": 11, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923730, "dur": 11, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923742, "dur": 1, "ph": "X", "name": "ProcessMessages 212", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923744, "dur": 10, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923755, "dur": 87, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923844, "dur": 9, "ph": "X", "name": "ReadAsync 1684", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923854, "dur": 52, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923908, "dur": 15, "ph": "X", "name": "ReadAsync 687", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923925, "dur": 11, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923938, "dur": 10, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923949, "dur": 10, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923961, "dur": 11, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923973, "dur": 11, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632923985, "dur": 39, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924026, "dur": 12, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924039, "dur": 11, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924052, "dur": 21, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924075, "dur": 11, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924088, "dur": 11, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924100, "dur": 11, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924114, "dur": 12, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924128, "dur": 10, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924139, "dur": 9, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924151, "dur": 10, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924162, "dur": 10, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924174, "dur": 10, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924185, "dur": 11, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924197, "dur": 12, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924210, "dur": 10, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924222, "dur": 16, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924239, "dur": 10, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924250, "dur": 10, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924262, "dur": 40, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924303, "dur": 17, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924323, "dur": 10, "ph": "X", "name": "ReadAsync 1327", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924334, "dur": 13, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924349, "dur": 11, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924362, "dur": 13, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924376, "dur": 10, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924388, "dur": 26, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924416, "dur": 11, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924429, "dur": 10, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924441, "dur": 10, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924453, "dur": 11, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924465, "dur": 11, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924477, "dur": 26, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924505, "dur": 9, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924516, "dur": 9, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924526, "dur": 10, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924537, "dur": 9, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924548, "dur": 11, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924560, "dur": 9, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924571, "dur": 9, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924581, "dur": 11, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924594, "dur": 12, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924608, "dur": 9, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924618, "dur": 18, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924638, "dur": 9, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924648, "dur": 9, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924659, "dur": 13, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924673, "dur": 11, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924686, "dur": 10, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924697, "dur": 9, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924707, "dur": 10, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924719, "dur": 18, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924739, "dur": 11, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924751, "dur": 11, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924763, "dur": 36, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924802, "dur": 11, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924814, "dur": 16, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924832, "dur": 15, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924848, "dur": 11, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924860, "dur": 1, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924861, "dur": 10, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924872, "dur": 9, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924882, "dur": 36, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924919, "dur": 10, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924930, "dur": 10, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924941, "dur": 9, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924952, "dur": 13, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924966, "dur": 15, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924982, "dur": 9, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632924993, "dur": 9, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925004, "dur": 9, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925014, "dur": 9, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925025, "dur": 9, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925035, "dur": 13, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925050, "dur": 13, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925065, "dur": 9, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925076, "dur": 11, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925088, "dur": 9, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925099, "dur": 32, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925133, "dur": 11, "ph": "X", "name": "ReadAsync 1081", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925145, "dur": 11, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925158, "dur": 18, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925177, "dur": 10, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925189, "dur": 12, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925202, "dur": 11, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925215, "dur": 10, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925226, "dur": 12, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925240, "dur": 10, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925254, "dur": 9, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925265, "dur": 11, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925278, "dur": 13, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925292, "dur": 9, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925303, "dur": 11, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925315, "dur": 1, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925316, "dur": 11, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925328, "dur": 10, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925339, "dur": 11, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925352, "dur": 12, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925366, "dur": 12, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925380, "dur": 14, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925395, "dur": 21, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925417, "dur": 15, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925434, "dur": 9, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925444, "dur": 9, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925455, "dur": 9, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925466, "dur": 9, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925476, "dur": 9, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925487, "dur": 9, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925498, "dur": 9, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925508, "dur": 9, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925519, "dur": 10, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925530, "dur": 10, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925542, "dur": 10, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925553, "dur": 10, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925564, "dur": 9, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925575, "dur": 9, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925586, "dur": 26, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925613, "dur": 10, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925624, "dur": 20, "ph": "X", "name": "ReadAsync 107", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925645, "dur": 10, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925656, "dur": 12, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925671, "dur": 12, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925685, "dur": 10, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925696, "dur": 11, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925710, "dur": 9, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925720, "dur": 10, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925732, "dur": 12, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925745, "dur": 9, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925756, "dur": 9, "ph": "X", "name": "ReadAsync 162", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925767, "dur": 21, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925790, "dur": 7, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925798, "dur": 12, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925811, "dur": 10, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925822, "dur": 11, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925835, "dur": 10, "ph": "X", "name": "ReadAsync 177", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925846, "dur": 9, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925857, "dur": 11, "ph": "X", "name": "ReadAsync 151", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925869, "dur": 11, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925882, "dur": 9, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925894, "dur": 11, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925906, "dur": 10, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925918, "dur": 11, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925930, "dur": 14, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925946, "dur": 10, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925957, "dur": 15, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632925984, "dur": 15, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926000, "dur": 10, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926012, "dur": 10, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926023, "dur": 9, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926033, "dur": 9, "ph": "X", "name": "ReadAsync 125", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926044, "dur": 10, "ph": "X", "name": "ReadAsync 121", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926056, "dur": 10, "ph": "X", "name": "ReadAsync 229", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926068, "dur": 14, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926084, "dur": 9, "ph": "X", "name": "ReadAsync 264", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926094, "dur": 14, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926110, "dur": 9, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926121, "dur": 9, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926131, "dur": 10, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926143, "dur": 10, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926154, "dur": 9, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926165, "dur": 12, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926178, "dur": 10, "ph": "X", "name": "ReadAsync 293", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926190, "dur": 13, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926204, "dur": 13, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926219, "dur": 10, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926230, "dur": 13, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926244, "dur": 12, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926258, "dur": 61, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926321, "dur": 11, "ph": "X", "name": "ReadAsync 1607", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926333, "dur": 14, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926348, "dur": 9, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926359, "dur": 10, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926371, "dur": 9, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926382, "dur": 13, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926396, "dur": 10, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926407, "dur": 1, "ph": "X", "name": "ProcessMessages 271", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926408, "dur": 10, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926419, "dur": 12, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926432, "dur": 12, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926446, "dur": 34, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926482, "dur": 9, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926493, "dur": 61, "ph": "X", "name": "ReadAsync 234", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926556, "dur": 16, "ph": "X", "name": "ReadAsync 1550", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926575, "dur": 15, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926592, "dur": 11, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926604, "dur": 13, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926620, "dur": 8, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926630, "dur": 20, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926651, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926663, "dur": 13, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926677, "dur": 10, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926689, "dur": 11, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926701, "dur": 11, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926713, "dur": 25, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926739, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926741, "dur": 16, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926759, "dur": 10, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926771, "dur": 11, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926783, "dur": 11, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926795, "dur": 11, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926808, "dur": 9, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926818, "dur": 11, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926831, "dur": 12, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926844, "dur": 11, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926857, "dur": 12, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926871, "dur": 12, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926885, "dur": 11, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926897, "dur": 8, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632926906, "dur": 121, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632927029, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632927041, "dur": 10, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632927053, "dur": 15, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632927069, "dur": 10, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632927080, "dur": 12, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632927094, "dur": 12, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632927107, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632927108, "dur": 8, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632927118, "dur": 9, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632927129, "dur": 9, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632927139, "dur": 157, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632927297, "dur": 145, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632927443, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632927445, "dur": 21, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632927468, "dur": 2, "ph": "X", "name": "ProcessMessages 720", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632927470, "dur": 53, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632927525, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632927536, "dur": 4, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632927542, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632927550, "dur": 620, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632928173, "dur": 166, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632928340, "dur": 7, "ph": "X", "name": "ProcessMessages 3472", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632928348, "dur": 7, "ph": "X", "name": "ReadAsync 3472", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632928356, "dur": 2, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632928360, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632928435, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632928446, "dur": 12, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632928460, "dur": 113, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632928576, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632928585, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632928587, "dur": 215, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632928805, "dur": 1853, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632930663, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632930687, "dur": 15, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632930703, "dur": 3160, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632933868, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632933880, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632933899, "dur": 6, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632933906, "dur": 157, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632934067, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632934077, "dur": 190, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632934270, "dur": 8, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632934280, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632934328, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632934339, "dur": 41, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632934381, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632934428, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632934444, "dur": 14, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632934460, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632934471, "dur": 167, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632934640, "dur": 4, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632934645, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632934684, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632934687, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632934699, "dur": 2032, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632936736, "dur": 88, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632936827, "dur": 6, "ph": "X", "name": "ProcessMessages 1780", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632936834, "dur": 25, "ph": "X", "name": "ReadAsync 1780", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632936862, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632936864, "dur": 41, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632936909, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632936922, "dur": 8, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632936932, "dur": 8, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632936942, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632936974, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632936989, "dur": 13, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632937004, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632937022, "dur": 12, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632937036, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632937064, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632937076, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632937101, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632937116, "dur": 119, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632937237, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632937260, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632937273, "dur": 9, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632937284, "dur": 671, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632937958, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632937980, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632937982, "dur": 20, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632938006, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632938051, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632938095, "dur": 12, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632938246, "dur": 54, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632938304, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632938328, "dur": 205, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632938538, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632938556, "dur": 271, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632938831, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632938845, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023632938847, "dur": 1659400, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023634598250, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023634598252, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023634598280, "dur": 881, "ph": "X", "name": "ProcessMessages 9867", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023634599162, "dur": 3140, "ph": "X", "name": "ReadAsync 9867", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023634602306, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023634602308, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023634602362, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023634602364, "dur": 1744, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023634604113, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023634604125, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023634604137, "dur": 28327, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023634632468, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023634632471, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023634632484, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023634632485, "dur": 404768, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023635037259, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023635037262, "dur": 116, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023635037381, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023635037384, "dur": 1390, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023635038780, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023635038793, "dur": 16, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023635038810, "dur": 1652734, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023636691549, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023636691551, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023636691579, "dur": 382, "ph": "X", "name": "ProcessMessages 4348", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023636691962, "dur": 1093, "ph": "X", "name": "ReadAsync 4348", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023636693058, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023636693073, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023636693075, "dur": 404, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023636693483, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023636693501, "dur": 8, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023636693510, "dur": 189168, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023636882683, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023636882685, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023636882700, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023636882702, "dur": 997, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023636883705, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023636883735, "dur": 14, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023636883751, "dur": 474, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023636884228, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 21474836480, "ts": 1758023636884244, "dur": 3516, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 20044, "tid": 9341, "ts": 1758023636894945, "dur": 421, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 20044, "tid": 17179869184, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 20044, "tid": 17179869184, "ts": 1758023632874136, "dur": 2, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 20044, "tid": 17179869184, "ts": 1758023632874139, "dur": 10311, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 20044, "tid": 17179869184, "ts": 1758023632884451, "dur": 15, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 20044, "tid": 9341, "ts": 1758023636895368, "dur": 3, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 20044, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 20044, "tid": 1, "ts": 1758023625125116, "dur": 28224, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20044, "tid": 1, "ts": 1758023625153345, "dur": 13205, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 20044, "tid": 1, "ts": 1758023625166556, "dur": 56656, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 20044, "tid": 9341, "ts": 1758023636895372, "dur": 2, "ph": "X", "name": "", "args": {}}, {"pid": 20044, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625123669, "dur": 8911, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625132583, "dur": 5928198, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625133197, "dur": 1396, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625134597, "dur": 312, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625134911, "dur": 7824, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625142744, "dur": 155, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625142900, "dur": 336, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625143240, "dur": 360, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625143602, "dur": 14994, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625158600, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625158602, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625158649, "dur": 323, "ph": "X", "name": "ProcessMessages 1345", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625158974, "dur": 83, "ph": "X", "name": "ReadAsync 1345", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159059, "dur": 2, "ph": "X", "name": "ProcessMessages 10023", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159062, "dur": 16, "ph": "X", "name": "ReadAsync 10023", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159080, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159082, "dur": 15, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159099, "dur": 19, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159119, "dur": 62, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159183, "dur": 14, "ph": "X", "name": "ReadAsync 1780", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159199, "dur": 10, "ph": "X", "name": "ReadAsync 575", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159210, "dur": 11, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159222, "dur": 10, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159234, "dur": 8, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159244, "dur": 13, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159259, "dur": 11, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159271, "dur": 12, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159285, "dur": 13, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159299, "dur": 53, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159355, "dur": 1, "ph": "X", "name": "ProcessMessages 1713", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159356, "dur": 21, "ph": "X", "name": "ReadAsync 1713", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159379, "dur": 1, "ph": "X", "name": "ProcessMessages 983", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159382, "dur": 16, "ph": "X", "name": "ReadAsync 983", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159400, "dur": 12, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159414, "dur": 10, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159425, "dur": 24, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159451, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159463, "dur": 12, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159476, "dur": 11, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159489, "dur": 11, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159502, "dur": 10, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159513, "dur": 12, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159527, "dur": 10, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159539, "dur": 17, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159557, "dur": 11, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159570, "dur": 11, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159582, "dur": 11, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159594, "dur": 9, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159605, "dur": 10, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159616, "dur": 19, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159637, "dur": 15, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159655, "dur": 47, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159704, "dur": 10, "ph": "X", "name": "ReadAsync 1157", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159716, "dur": 29, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159747, "dur": 18, "ph": "X", "name": "ReadAsync 1055", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159766, "dur": 10, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159777, "dur": 20, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159798, "dur": 12, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159811, "dur": 10, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159823, "dur": 12, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159836, "dur": 9, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159846, "dur": 13, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625159861, "dur": 164, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160026, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160041, "dur": 13, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160055, "dur": 10, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160067, "dur": 10, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160079, "dur": 10, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160091, "dur": 127, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160220, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160237, "dur": 12, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160250, "dur": 22, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160274, "dur": 13, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160289, "dur": 11, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160302, "dur": 12, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160316, "dur": 12, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160330, "dur": 114, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160446, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160460, "dur": 14, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160475, "dur": 13, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160490, "dur": 12, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160503, "dur": 15, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160520, "dur": 11, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160536, "dur": 139, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160678, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160694, "dur": 9, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160705, "dur": 71, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160778, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160793, "dur": 13, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160808, "dur": 11, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160821, "dur": 11, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160834, "dur": 22, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160859, "dur": 22, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160883, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160907, "dur": 17, "ph": "X", "name": "ReadAsync 685", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160926, "dur": 14, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160942, "dur": 14, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160958, "dur": 8, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625160969, "dur": 129, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161100, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161114, "dur": 16, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161132, "dur": 13, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161147, "dur": 10, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161158, "dur": 10, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161169, "dur": 9, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161180, "dur": 19, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161201, "dur": 11, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161214, "dur": 12, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161228, "dur": 17, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161247, "dur": 23, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161273, "dur": 14, "ph": "X", "name": "ReadAsync 142", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161290, "dur": 11, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161304, "dur": 12, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161318, "dur": 11, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161331, "dur": 11, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161344, "dur": 12, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161359, "dur": 11, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161372, "dur": 14, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161388, "dur": 9, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161399, "dur": 11, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161412, "dur": 14, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161428, "dur": 10, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161440, "dur": 14, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161455, "dur": 10, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161467, "dur": 17, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161486, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161504, "dur": 11, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161518, "dur": 12, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161532, "dur": 12, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161546, "dur": 10, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161559, "dur": 10, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161571, "dur": 11, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161584, "dur": 11, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161597, "dur": 13, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161612, "dur": 12, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161626, "dur": 11, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161639, "dur": 9, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161650, "dur": 11, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161663, "dur": 11, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161676, "dur": 12, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161690, "dur": 11, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161703, "dur": 13, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161719, "dur": 9, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161731, "dur": 11, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161743, "dur": 11, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161756, "dur": 12, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161770, "dur": 12, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161784, "dur": 11, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161797, "dur": 10, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161808, "dur": 11, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161821, "dur": 12, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161834, "dur": 12, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161849, "dur": 34, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161886, "dur": 13, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161902, "dur": 10, "ph": "X", "name": "ReadAsync 480", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161914, "dur": 11, "ph": "X", "name": "ReadAsync 65", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161927, "dur": 9, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161939, "dur": 11, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161952, "dur": 11, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161965, "dur": 12, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161979, "dur": 12, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625161994, "dur": 12, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162007, "dur": 12, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162022, "dur": 14, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162039, "dur": 16, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162056, "dur": 10, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162068, "dur": 116, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162186, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162202, "dur": 10, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162214, "dur": 13, "ph": "X", "name": "ReadAsync 117", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162229, "dur": 16, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162249, "dur": 11, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162262, "dur": 10, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162274, "dur": 14, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162290, "dur": 10, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162302, "dur": 14, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162319, "dur": 12, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162333, "dur": 13, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162348, "dur": 10, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162359, "dur": 1, "ph": "X", "name": "ProcessMessages 159", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162360, "dur": 12, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162374, "dur": 15, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162391, "dur": 31, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162424, "dur": 10, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162436, "dur": 10, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162449, "dur": 15, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162465, "dur": 16, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162483, "dur": 12, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162496, "dur": 13, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162511, "dur": 12, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162525, "dur": 11, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162538, "dur": 9, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162549, "dur": 11, "ph": "X", "name": "ReadAsync 135", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162563, "dur": 12, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162577, "dur": 28, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162608, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162632, "dur": 11, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162645, "dur": 12, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162658, "dur": 113, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162773, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162789, "dur": 12, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162803, "dur": 12, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162816, "dur": 13, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162831, "dur": 11, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162844, "dur": 12, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162858, "dur": 12, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162871, "dur": 14, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162887, "dur": 43, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162932, "dur": 13, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162946, "dur": 10, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162958, "dur": 11, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162971, "dur": 17, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625162990, "dur": 10, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163001, "dur": 10, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163012, "dur": 105, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163119, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163132, "dur": 11, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163145, "dur": 10, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163156, "dur": 11, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163169, "dur": 12, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163182, "dur": 10, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163195, "dur": 10, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163207, "dur": 114, "ph": "X", "name": "ReadAsync 201", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163322, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163335, "dur": 79, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163416, "dur": 24, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163443, "dur": 23, "ph": "X", "name": "ReadAsync 2464", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163468, "dur": 14, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163484, "dur": 11, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163497, "dur": 15, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163513, "dur": 11, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163525, "dur": 97, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163624, "dur": 1, "ph": "X", "name": "ProcessMessages 2180", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163626, "dur": 65, "ph": "X", "name": "ReadAsync 2180", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163693, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163754, "dur": 13, "ph": "X", "name": "ReadAsync 1452", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163769, "dur": 8, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163779, "dur": 9, "ph": "X", "name": "ReadAsync 89", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163790, "dur": 192, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163983, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625163998, "dur": 10, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164009, "dur": 10, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164021, "dur": 10, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164033, "dur": 11, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164046, "dur": 12, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164060, "dur": 12, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164074, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164076, "dur": 16, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164093, "dur": 1, "ph": "X", "name": "ProcessMessages 428", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164095, "dur": 12, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164111, "dur": 13, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164126, "dur": 11, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164138, "dur": 10, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164150, "dur": 9, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164160, "dur": 9, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164171, "dur": 20, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164193, "dur": 10, "ph": "X", "name": "ReadAsync 174", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164204, "dur": 11, "ph": "X", "name": "ReadAsync 211", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164217, "dur": 10, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164229, "dur": 10, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164241, "dur": 11, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164253, "dur": 11, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164266, "dur": 8, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164276, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164289, "dur": 19, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164310, "dur": 10, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164322, "dur": 10, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164333, "dur": 9, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164344, "dur": 13, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164359, "dur": 9, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164370, "dur": 11, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164383, "dur": 10, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164395, "dur": 11, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164407, "dur": 8, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164417, "dur": 10, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164428, "dur": 9, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164438, "dur": 26, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164466, "dur": 10, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164478, "dur": 94, "ph": "X", "name": "ReadAsync 101", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164575, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164594, "dur": 19, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164615, "dur": 21, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164638, "dur": 12, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164652, "dur": 9, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164663, "dur": 9, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164674, "dur": 12, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164689, "dur": 10, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164700, "dur": 13, "ph": "X", "name": "ReadAsync 190", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164715, "dur": 11, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164728, "dur": 12, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164742, "dur": 11, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164755, "dur": 48, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164806, "dur": 11, "ph": "X", "name": "ReadAsync 1344", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164818, "dur": 12, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164832, "dur": 10, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164845, "dur": 119, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164965, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164981, "dur": 9, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625164991, "dur": 25, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165019, "dur": 15, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165035, "dur": 10, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165047, "dur": 11, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165061, "dur": 10, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165073, "dur": 13, "ph": "X", "name": "ReadAsync 170", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165087, "dur": 13, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165102, "dur": 13, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165118, "dur": 13, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165132, "dur": 12, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165146, "dur": 10, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165158, "dur": 10, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165170, "dur": 15, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165186, "dur": 12, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165200, "dur": 14, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165216, "dur": 11, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165229, "dur": 12, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165242, "dur": 10, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165254, "dur": 10, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165265, "dur": 11, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165278, "dur": 13, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165293, "dur": 11, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165305, "dur": 11, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165319, "dur": 12, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165333, "dur": 11, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165347, "dur": 106, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165455, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165468, "dur": 11, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165481, "dur": 13, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165496, "dur": 12, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165510, "dur": 16, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165528, "dur": 10, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165539, "dur": 13, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165554, "dur": 17, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165573, "dur": 12, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165586, "dur": 10, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165598, "dur": 14, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165613, "dur": 12, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165626, "dur": 9, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165637, "dur": 93, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165731, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165732, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165750, "dur": 11, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165763, "dur": 11, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165775, "dur": 10, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165787, "dur": 18, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165807, "dur": 9, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165818, "dur": 122, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165941, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165954, "dur": 16, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165973, "dur": 24, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625165998, "dur": 11, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166011, "dur": 47, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166060, "dur": 12, "ph": "X", "name": "ReadAsync 921", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166074, "dur": 11, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166087, "dur": 35, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166124, "dur": 12, "ph": "X", "name": "ReadAsync 1024", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166138, "dur": 9, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166148, "dur": 10, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166170, "dur": 15, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166188, "dur": 10, "ph": "X", "name": "ReadAsync 898", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166199, "dur": 12, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166213, "dur": 12, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166227, "dur": 10, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166239, "dur": 110, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166351, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166363, "dur": 17, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166382, "dur": 14, "ph": "X", "name": "ReadAsync 428", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166397, "dur": 14, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166412, "dur": 13, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166427, "dur": 26, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166454, "dur": 10, "ph": "X", "name": "ReadAsync 155", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166465, "dur": 11, "ph": "X", "name": "ReadAsync 102", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166478, "dur": 11, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166491, "dur": 11, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166503, "dur": 10, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166515, "dur": 12, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166528, "dur": 11, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166541, "dur": 11, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166554, "dur": 10, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166566, "dur": 14, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166581, "dur": 11, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166594, "dur": 11, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166607, "dur": 11, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166619, "dur": 15, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166636, "dur": 11, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166649, "dur": 16, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166667, "dur": 23, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166691, "dur": 21, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166714, "dur": 16, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166733, "dur": 107, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166842, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166857, "dur": 11, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166870, "dur": 11, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166882, "dur": 13, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166896, "dur": 16, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166914, "dur": 11, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625166927, "dur": 151, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167079, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167097, "dur": 13, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167112, "dur": 9, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167123, "dur": 16, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167141, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167155, "dur": 12, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167168, "dur": 23, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167193, "dur": 11, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167206, "dur": 82, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167289, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167301, "dur": 1, "ph": "X", "name": "ProcessMessages 343", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167302, "dur": 10, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167314, "dur": 11, "ph": "X", "name": "ReadAsync 251", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167327, "dur": 31, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167359, "dur": 13, "ph": "X", "name": "ReadAsync 1083", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167374, "dur": 11, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167387, "dur": 12, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167401, "dur": 11, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167413, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167432, "dur": 12, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167446, "dur": 17, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167464, "dur": 11, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167477, "dur": 11, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167491, "dur": 12, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167505, "dur": 208, "ph": "X", "name": "ReadAsync 63", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167714, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167728, "dur": 15, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167744, "dur": 16, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167762, "dur": 11, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167775, "dur": 12, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167788, "dur": 10, "ph": "X", "name": "ReadAsync 422", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167800, "dur": 107, "ph": "X", "name": "ReadAsync 166", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167908, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625167921, "dur": 358, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625168282, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625168304, "dur": 264, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625168570, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625168650, "dur": 2, "ph": "X", "name": "ProcessMessages 896", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625168653, "dur": 44, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625168699, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625168700, "dur": 23, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625168726, "dur": 17, "ph": "X", "name": "ReadAsync 212", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625168745, "dur": 57, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625168803, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625168824, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625168839, "dur": 14, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625168856, "dur": 6, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625168863, "dur": 3, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625168867, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625168901, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625168961, "dur": 21, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625168984, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625168985, "dur": 44, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169033, "dur": 77, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169113, "dur": 24, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169139, "dur": 51, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169192, "dur": 16, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169210, "dur": 16, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169228, "dur": 111, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169342, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169381, "dur": 20, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169405, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169436, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169438, "dur": 31, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169472, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169474, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169502, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169504, "dur": 19, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169525, "dur": 21, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169549, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169569, "dur": 81, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169653, "dur": 31, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169687, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169703, "dur": 17, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169723, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169749, "dur": 11, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169762, "dur": 9, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169773, "dur": 10, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169785, "dur": 7, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169794, "dur": 7, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169803, "dur": 18, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625169826, "dur": 1430, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625171260, "dur": 58, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625171321, "dur": 3245, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625174570, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625174571, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625174623, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625174624, "dur": 372, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625174999, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625175026, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625175045, "dur": 42, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625175090, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625175109, "dur": 266, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625175378, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625175421, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625175443, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625175467, "dur": 172, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625175641, "dur": 1651, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625177295, "dur": 123, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625177419, "dur": 2, "ph": "X", "name": "ProcessMessages 1136", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625177423, "dur": 74, "ph": "X", "name": "ReadAsync 1136", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625177498, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625177520, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625177521, "dur": 14, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625177537, "dur": 106, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625177644, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625177686, "dur": 17, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625177705, "dur": 33, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625177742, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625177779, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625177827, "dur": 25, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625177855, "dur": 58, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625177915, "dur": 121, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625178039, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625178060, "dur": 270, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625178335, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625178357, "dur": 53, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625178414, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625178439, "dur": 14, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625178455, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625178507, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625178551, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625178553, "dur": 21, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625178576, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625178602, "dur": 16, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625178620, "dur": 9, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625178631, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625178669, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625178689, "dur": 17, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625178708, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625178748, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625178765, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625178813, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625178829, "dur": 8, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625178841, "dur": 285, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625179128, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625179151, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625179180, "dur": 45, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625179227, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625179271, "dur": 6, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625179279, "dur": 125, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625179406, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625179431, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625179454, "dur": 199, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625179655, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625179676, "dur": 764, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625180442, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625180460, "dur": 367, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023625180828, "dur": 5866368, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023631047204, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023631047207, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023631047243, "dur": 12821, "ph": "X", "name": "ProcessMessages 10200", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023631060069, "dur": 41, "ph": "X", "name": "ReadAsync 10200", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023631060112, "dur": 259, "ph": "X", "name": "ProcessMessages 25", "args": {}}, {"pid": 20044, "tid": 12884901888, "ts": 1758023631060373, "dur": 120, "ph": "X", "name": "ReadAsync 25", "args": {}}, {"pid": 20044, "tid": 9341, "ts": 1758023636895376, "dur": 343, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 20044, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 20044, "tid": 8589934592, "ts": 1758023625122136, "dur": 101118, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 20044, "tid": 8589934592, "ts": 1758023625223256, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 20044, "tid": 8589934592, "ts": 1758023625223258, "dur": 844, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 20044, "tid": 9341, "ts": 1758023636895719, "dur": 5, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 20044, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 20044, "tid": 4294967296, "ts": 1758023624993951, "dur": 6067389, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 20044, "tid": 4294967296, "ts": 1758023624997038, "dur": 121847, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 20044, "tid": 4294967296, "ts": 1758023631061413, "dur": 1810258, "ph": "X", "name": "await ExecuteBuildProgram", "args": {}}, {"pid": 20044, "tid": 4294967296, "ts": 1758023632871790, "dur": 4015998, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 20044, "tid": 4294967296, "ts": 1758023632871868, "dur": 2248, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 20044, "tid": 4294967296, "ts": 1758023636887798, "dur": 2819, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 20044, "tid": 4294967296, "ts": 1758023636889656, "dur": 24, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 20044, "tid": 4294967296, "ts": 1758023636890620, "dur": 9, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 20044, "tid": 9341, "ts": 1758023636895725, "dur": 6, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1758023632884671, "dur": 35087, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758023632919767, "dur": 115, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758023632919945, "dur": 517, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758023632920478, "dur": 6872, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758023632927357, "dur": 3956753, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758023636884166, "dur": 70, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758023636884443, "dur": 835, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1758023632920563, "dur": 6799, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632927367, "dur": 718, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632928260, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632928389, "dur": 83, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632928472, "dur": 403, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632928912, "dur": 503, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\Framework\\Tcp\\NetworkEvent\\NetworkEvent_SocketClosed.cs"}}, {"pid": 12345, "tid": 1, "ts": 1758023632928875, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632929966, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Assets\\AppleSignIn\\AppleAuth\\Native\\PasswordCredential.cs"}}, {"pid": 12345, "tid": 1, "ts": 1758023632929525, "dur": 1097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632930622, "dur": 191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632930813, "dur": 412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632931226, "dur": 748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632931974, "dur": 1500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632933475, "dur": 312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632933787, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632934056, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632934530, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632934632, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632934894, "dur": 695, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632935596, "dur": 1254, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 1, "ts": 1758023632935591, "dur": 1778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1758023632937369, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632937497, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632937628, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632937715, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632937830, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632938010, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632938253, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023632938520, "dur": 1663466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023634601986, "dur": 2281947, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023632920580, "dur": 6796, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023632927379, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BD6B6DA2CAED0C51.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758023632927510, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758023632927510, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_0D716A535EA5D9A0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758023632927698, "dur": 89, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_0D716A535EA5D9A0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758023632928189, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023632928319, "dur": 560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023632928913, "dur": 533, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\Framework\\Http\\HTTPParamField.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758023632928879, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023632929952, "dur": 1173, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\Solution.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758023632929553, "dur": 2830, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023632932393, "dur": 599, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\UnityTestProtocol\\UtpMessageReporter.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758023632933011, "dur": 529, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\UnityTestProtocol\\UnityTestProtocolListener.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758023632932391, "dur": 1304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023632933696, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023632934062, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023632934519, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023632934639, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023632934878, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023632934981, "dur": 1842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758023632936831, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758023632937248, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758023632937329, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758023632938035, "dur": 281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023632938316, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023632938543, "dur": 1663455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023634601998, "dur": 2281911, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632920635, "dur": 6757, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632927425, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1758023632927397, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_D57CD8953DD5F64D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758023632927688, "dur": 87, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Types.dll_A6E727B0B6898769.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758023632927777, "dur": 494, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1758023632927775, "dur": 498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_AD339B360B767264.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758023632928305, "dur": 617, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632928924, "dur": 97, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632929021, "dur": 126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632929147, "dur": 128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632929275, "dur": 418, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632929694, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632930390, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632930632, "dur": 333, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632930965, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632931787, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632932146, "dur": 890, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Utility\\VertexHelper.cs"}}, {"pid": 12345, "tid": 3, "ts": 1758023632932068, "dur": 1214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632933282, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632933734, "dur": 327, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632934061, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632934523, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632934639, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632934885, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632935033, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758023632935325, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758023632935437, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758023632935712, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Wx.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758023632935977, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/WxEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758023632936033, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758023632936092, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758023632936307, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758023632936552, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UIEffect-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758023632936845, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632936950, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632937092, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632937246, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632937297, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632937513, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632937625, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632937720, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632937855, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632938020, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632938260, "dur": 256, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023632938516, "dur": 1663476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023634601992, "dur": 2281937, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023632920571, "dur": 6797, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023632927378, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_D699E5DF035CDE66.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758023632927513, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1758023632927505, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_F61FF965A6F6FAF4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758023632927698, "dur": 918, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_F61FF965A6F6FAF4.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758023632928797, "dur": 640, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Scripts\\ScriptDLL\\GameLuaObjectData.cs"}}, {"pid": 12345, "tid": 4, "ts": 1758023632928617, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023632929885, "dur": 1100, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Window\\Modes\\TimeReferenceMode.cs"}}, {"pid": 12345, "tid": 4, "ts": 1758023632929584, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023632931401, "dur": 954, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\UnityConfigurationChecker.cs"}}, {"pid": 12345, "tid": 4, "ts": 1758023632932355, "dur": 1833, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\UI\\UnityThreadWaiter.cs"}}, {"pid": 12345, "tid": 4, "ts": 1758023632931082, "dur": 3189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023632934271, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023632934573, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023632934644, "dur": 1720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023632936364, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758023632936731, "dur": 319, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023632937055, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023632937345, "dur": 132, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023632937519, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023632937644, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023632937758, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023632937874, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023632938052, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023632938315, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023632938542, "dur": 1663431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023634601973, "dur": 2282168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023632920602, "dur": 6778, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023632927385, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_65323E1310BDBE7B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758023632927654, "dur": 1009, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_E2C9DE69A35782B8.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758023632928664, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023632929147, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023632929929, "dur": 1323, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\IUniTaskSource.cs"}}, {"pid": 12345, "tid": 5, "ts": 1758023632931404, "dur": 902, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\Internal\\PlayerLoopRunner.cs"}}, {"pid": 12345, "tid": 5, "ts": 1758023632932306, "dur": 1897, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\Internal\\MinimumQueue.cs"}}, {"pid": 12345, "tid": 5, "ts": 1758023632929352, "dur": 4851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023632934203, "dur": 377, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023632934580, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023632934637, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023632935308, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758023632935548, "dur": 1522, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758023632937206, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758023632935422, "dur": 2009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1758023632937432, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023632937648, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023632937771, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023632937876, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023632938047, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023632938312, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023632938525, "dur": 1663478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023634602003, "dur": 2281934, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023632920626, "dur": 6760, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023632927427, "dur": 284, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1758023632927392, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_A062D8221399A90F.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758023632927738, "dur": 712, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1758023632927738, "dur": 713, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_328CD27C104796DE.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758023632928459, "dur": 348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023632928808, "dur": 92, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023632928909, "dur": 543, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\Common\\Lson\\OffsetToLineCol.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758023632928900, "dur": 727, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023632929963, "dur": 588, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Utilities\\Scopes\\LabelWidthScope.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758023632929627, "dur": 1025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023632930653, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023632931235, "dur": 567, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\UI\\Tree\\TreeViewItemIds.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758023632931828, "dur": 548, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\UI\\Tree\\TreeHeaderSettings.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758023632932376, "dur": 1868, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\UI\\Tree\\TreeHeaderColumns.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758023632931136, "dur": 3275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023632934411, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023632934554, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023632934657, "dur": 2140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023632936797, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023632937030, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023632937172, "dur": 148, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023632937320, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023632937502, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023632937620, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023632937761, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023632937873, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023632938064, "dur": 229, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023632938294, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023632938533, "dur": 1663468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023634602001, "dur": 2281947, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023632920655, "dur": 6779, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023632927438, "dur": 256, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1758023632927437, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_7D6B8E0347C20661.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758023632927700, "dur": 975, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_7D6B8E0347C20661.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758023632928679, "dur": 1128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758023632930253, "dur": 906, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 7, "ts": 1758023632931160, "dur": 243, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\AllocatingGCMemoryConstraint.cs"}}, {"pid": 12345, "tid": 7, "ts": 1758023632931404, "dur": 950, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\ConstraintsExtensions.cs"}}, {"pid": 12345, "tid": 7, "ts": 1758023632932354, "dur": 513, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\InvalidSignatureException.cs"}}, {"pid": 12345, "tid": 7, "ts": 1758023632932891, "dur": 369, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\ILogScope.cs"}}, {"pid": 12345, "tid": 7, "ts": 1758023632933271, "dur": 620, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogMatch.cs"}}, {"pid": 12345, "tid": 7, "ts": 1758023632929826, "dur": 4156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758023632934163, "dur": 423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758023632934677, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758023632935595, "dur": 1424, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758023632937025, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1758023632937510, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023632937635, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023632937745, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023632937877, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023632938050, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023632938308, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023632938527, "dur": 1663475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023634602002, "dur": 2281941, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023632920677, "dur": 6761, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023632927509, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023632928337, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023632929170, "dur": 535, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\GameSdkManagerWrap.cs"}}, {"pid": 12345, "tid": 8, "ts": 1758023632928574, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023632929908, "dur": 1073, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\TimelineHelpers.cs"}}, {"pid": 12345, "tid": 8, "ts": 1758023632931733, "dur": 2277, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Signals\\SignalUtility.cs"}}, {"pid": 12345, "tid": 8, "ts": 1758023632929705, "dur": 4331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023632934037, "dur": 507, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023632934544, "dur": 122, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023632934666, "dur": 2414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023632937081, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023632937238, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023632937290, "dur": 238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023632937528, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023632937737, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023632937878, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023632938049, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023632938317, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023632938544, "dur": 1663460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023634602004, "dur": 2281926, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023632920701, "dur": 6742, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023632927679, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.GradleProject.dll_78AB6F32676F7162.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758023632927764, "dur": 783, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758023632927763, "dur": 785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_0EED61BE115AECA6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758023632929175, "dur": 567, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\OrganComponentDataWrap.cs"}}, {"pid": 12345, "tid": 9, "ts": 1758023632928555, "dur": 1231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023632929910, "dur": 602, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Animation\\BindingTreeViewDataSource.cs"}}, {"pid": 12345, "tid": 9, "ts": 1758023632930659, "dur": 605, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Analytics\\TimelineAnalytics.cs"}}, {"pid": 12345, "tid": 9, "ts": 1758023632931264, "dur": 1090, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Activation\\ActivationTrackInspector.cs"}}, {"pid": 12345, "tid": 9, "ts": 1758023632932354, "dur": 1905, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Activation\\ActivationTrackEditor.cs"}}, {"pid": 12345, "tid": 9, "ts": 1758023632929786, "dur": 4532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023632934318, "dur": 251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023632934570, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023632934646, "dur": 960, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023632935607, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758023632936004, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758023632936061, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758023632936122, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758023632936356, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758023632936600, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023632936765, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023632936841, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023632937032, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023632937170, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023632937237, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023632937292, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023632937521, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023632937737, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023632937832, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023632938102, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023632938270, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023632938504, "dur": 1663525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023634602029, "dur": 2282117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023632920721, "dur": 6799, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023632927679, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Android.Gradle.dll_52C8AB7AD3D8A783.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758023632927737, "dur": 562, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1758023632927737, "dur": 563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_ED95F4E26287FEC9.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758023632928377, "dur": 1240, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Editor\\spine-unity\\Modules\\Ragdoll\\Editor\\SkeletonRagdollInspector.cs"}}, {"pid": 12345, "tid": 10, "ts": 1758023632929718, "dur": 806, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\Spine\\Editor\\spine-unity\\Editor\\SpineInspectorUtility.cs"}}, {"pid": 12345, "tid": 10, "ts": 1758023632928335, "dur": 2280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023632930615, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023632931707, "dur": 2461, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\Diff\\GetClientDiffInfos.cs"}}, {"pid": 12345, "tid": 10, "ts": 1758023632930987, "dur": 3248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023632934235, "dur": 341, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023632934576, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023632934640, "dur": 2170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023632936810, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023632937068, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023632937177, "dur": 50, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023632937247, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023632937317, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023632937472, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023632937529, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023632937638, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023632937714, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023632937845, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023632938028, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023632938258, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023632938516, "dur": 1663469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023634601986, "dur": 2282138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023632920735, "dur": 6788, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023632927726, "dur": 768, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023632928500, "dur": 106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023632928647, "dur": 543, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Scripts\\ScriptDLL\\Injectiondouble_3.cs"}}, {"pid": 12345, "tid": 11, "ts": 1758023632928606, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023632929290, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023632929986, "dur": 822, "ph": "X", "name": "File", "args": {"detail": "Assets\\AppleSignIn\\AppleAuth\\Native\\SerializationTools.cs"}}, {"pid": 12345, "tid": 11, "ts": 1758023632929505, "dur": 1324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023632930829, "dur": 337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023632931717, "dur": 2245, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\UI\\HandleMenuItem.cs"}}, {"pid": 12345, "tid": 11, "ts": 1758023632931166, "dur": 2847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023632934039, "dur": 504, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023632934543, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023632934667, "dur": 2288, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023632936956, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023632937070, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023632937251, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023632937313, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023632937526, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023632937640, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023632937759, "dur": 116, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023632937875, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023632938029, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023632938254, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023632938521, "dur": 1663519, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023634602040, "dur": 2282103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632920756, "dur": 6955, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632927738, "dur": 705, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632928506, "dur": 687, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\UnityEngine_UI_InputFieldWrap.cs"}}, {"pid": 12345, "tid": 12, "ts": 1758023632928448, "dur": 838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632929286, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632929459, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632929889, "dur": 740, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Manipulators\\Cursors\\TimelineCursors.cs"}}, {"pid": 12345, "tid": 12, "ts": 1758023632930682, "dur": 1516, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Localization\\Localization.cs"}}, {"pid": 12345, "tid": 12, "ts": 1758023632929761, "dur": 2875, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632932636, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632932938, "dur": 907, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\TestLaunchers\\PlatformSetup\\StadiaPlatformSetup.cs"}}, {"pid": 12345, "tid": 12, "ts": 1758023632932814, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632933908, "dur": 185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632934093, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632934516, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632934676, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632934903, "dur": 702, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632935606, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/AppleAuth.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1758023632935836, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632936050, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1758023632936116, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1758023632936390, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632936551, "dur": 342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1758023632936949, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632937074, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632937173, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632937237, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632937318, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632937498, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632937560, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632937640, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632937729, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632937837, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632938070, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632938304, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023632938525, "dur": 1663465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023634601990, "dur": 2282058, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023632920779, "dur": 6938, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023632927737, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1758023632927736, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_90AA0436019DA739.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1758023632928293, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023632928646, "dur": 1039, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Scripts\\CommonTools\\ScrollRectItem.cs"}}, {"pid": 12345, "tid": 13, "ts": 1758023632928646, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023632929921, "dur": 526, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\TMP_SpriteAssetMenu.cs"}}, {"pid": 12345, "tid": 13, "ts": 1758023632929892, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023632931406, "dur": 898, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Animation\\AnimationPreviewUpdateCallback.cs"}}, {"pid": 12345, "tid": 13, "ts": 1758023632930697, "dur": 1635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023632932332, "dur": 1447, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Button.cs"}}, {"pid": 12345, "tid": 13, "ts": 1758023632932332, "dur": 1663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023632933995, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023632934086, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023632934542, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023632934670, "dur": 237, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023632934907, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023632935607, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1758023632935811, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1758023632936069, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UIEffect.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1758023632936303, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1758023632936618, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023632936761, "dur": 381, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023632937217, "dur": 522, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023632937741, "dur": 108, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023632937849, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023632938028, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023632938262, "dur": 250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023632938512, "dur": 1663472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023634601985, "dur": 2282151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023632920808, "dur": 6941, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023632927754, "dur": 769, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1758023632927752, "dur": 773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_6A4C4ABC6FBD89C0.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1758023632929016, "dur": 847, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\TMPro_TextMeshProUGUIWrap.cs"}}, {"pid": 12345, "tid": 14, "ts": 1758023632928535, "dur": 1372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023632929907, "dur": 1204, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\DropdownOptionListDrawer.cs"}}, {"pid": 12345, "tid": 14, "ts": 1758023632931399, "dur": 795, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.36\\Rider\\Editor\\RiderStyles.cs"}}, {"pid": 12345, "tid": 14, "ts": 1758023632932194, "dur": 1793, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.36\\Rider\\Editor\\RiderScriptEditorDataPersisted.cs"}}, {"pid": 12345, "tid": 14, "ts": 1758023632929907, "dur": 4080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023632933987, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023632934086, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023632934538, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023632934677, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023632934897, "dur": 1840, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023632936784, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023632937039, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023632937309, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023632937522, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023632937642, "dur": 120, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023632937762, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023632937873, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023632938046, "dur": 264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023632938310, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023632938538, "dur": 1663456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023634601994, "dur": 2282046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023632920829, "dur": 6932, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023632927766, "dur": 803, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1758023632927764, "dur": 806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_AEE611DA6D6531BB.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1758023632928635, "dur": 982, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\DG_Tweening_ShortcutExtensions50Wrap.cs"}}, {"pid": 12345, "tid": 15, "ts": 1758023632928581, "dur": 1075, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023632929930, "dur": 635, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\treeview\\TrackGui\\TimelineTrackErrorGUI.cs"}}, {"pid": 12345, "tid": 15, "ts": 1758023632929656, "dur": 1109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023632930765, "dur": 413, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023632931246, "dur": 1085, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\UI\\EditorDispatcher.cs"}}, {"pid": 12345, "tid": 15, "ts": 1758023632932331, "dur": 1950, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\UI\\DropDownTextField.cs"}}, {"pid": 12345, "tid": 15, "ts": 1758023632931178, "dur": 3248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023632934426, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023632934550, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023632934659, "dur": 2131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023632936791, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023632937064, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023632937255, "dur": 58, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023632937314, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023632937474, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023632937526, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023632937618, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023632937718, "dur": 123, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023632937841, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023632938024, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023632938266, "dur": 242, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023632938508, "dur": 1663474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023634601982, "dur": 2282075, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023632920851, "dur": 6913, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023632927770, "dur": 815, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1758023632927768, "dur": 818, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_4C96AFA9785B3E8A.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1758023632928600, "dur": 986, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\AssetManagerWrap.cs"}}, {"pid": 12345, "tid": 16, "ts": 1758023632928595, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023632929933, "dur": 1150, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Shortcuts.cs"}}, {"pid": 12345, "tid": 16, "ts": 1758023632929708, "dur": 2154, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023632931999, "dur": 2120, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\Properties\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 16, "ts": 1758023632931865, "dur": 2312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023632934177, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023632934587, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023632934686, "dur": 1701, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023632936388, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UIEffect-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1758023632936470, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1758023632936538, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1758023632936612, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1758023632936945, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023632937042, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023632937191, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023632937258, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023632937324, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023632937529, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023632937641, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023632937760, "dur": 117, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023632937877, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023632938051, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023632938314, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023632938509, "dur": 1663458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023634601968, "dur": 2282121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023632920865, "dur": 6911, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023632928951, "dur": 210, "ph": "X", "name": "WriteText", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1758023632929162, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023632929949, "dur": 1221, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\Channel.cs"}}, {"pid": 12345, "tid": 17, "ts": 1758023632929403, "dur": 1977, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023632931736, "dur": 2107, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 17, "ts": 1758023632931380, "dur": 2763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023632934143, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023632934590, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023632934680, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023632934890, "dur": 1873, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023632936779, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023632937036, "dur": 161, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023632937245, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023632937336, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023632937517, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023632937620, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023632937719, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023632937865, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023632938011, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023632938260, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023632938501, "dur": 1663523, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023634602024, "dur": 2281884, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632920877, "dur": 6907, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632928330, "dur": 485, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632928905, "dur": 635, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\Framework\\WebSocket\\NetworkWebSocketEventMgr.cs"}}, {"pid": 12345, "tid": 18, "ts": 1758023632928815, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632929957, "dur": 1082, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\Discovery.cs"}}, {"pid": 12345, "tid": 18, "ts": 1758023632929556, "dur": 1748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632931304, "dur": 820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632932640, "dur": 527, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaterialModifiers\\IMaterialModifier.cs"}}, {"pid": 12345, "tid": 18, "ts": 1758023632932124, "dur": 1688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632933812, "dur": 241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632934053, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632934536, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632934626, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632934900, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632935039, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1758023632935581, "dur": 402, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 18, "ts": 1758023632935459, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1758023632936100, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/WxEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1758023632936361, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1758023632936779, "dur": 257, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632937036, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632937196, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632937251, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632937330, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632937523, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632937629, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632937710, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632937835, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632938017, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632938253, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023632938508, "dur": 1663458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023634601967, "dur": 2091178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023636693147, "dur": 189711, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 18, "ts": 1758023636693147, "dur": 189712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 18, "ts": 1758023636882876, "dur": 996, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 19, "ts": 1758023632920889, "dur": 7306, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023632928908, "dur": 546, "ph": "X", "name": "File", "args": {"detail": "Assets\\Editor\\CustomTools\\ETAfterBuild.cs"}}, {"pid": 12345, "tid": 19, "ts": 1758023632928333, "dur": 1301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023632929634, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023632929927, "dur": 1120, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\TMP_EditorCoroutine.cs"}}, {"pid": 12345, "tid": 19, "ts": 1758023632929901, "dur": 1919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023632932170, "dur": 659, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Runtime\\TMP_FontFeaturesCommon.cs"}}, {"pid": 12345, "tid": 19, "ts": 1758023632931820, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023632932897, "dur": 434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023632933527, "dur": 843, "ph": "X", "name": "File", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 19, "ts": 1758023632933331, "dur": 1064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023632934395, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023632934559, "dur": 60, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023632934619, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023632934904, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023632936786, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1758023632936852, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1758023632937169, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1758023632937493, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1758023632937872, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023632938018, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023632938266, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023632938510, "dur": 1663452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023634601965, "dur": 435463, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 19, "ts": 1758023634601964, "dur": 435465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 19, "ts": 1758023635037446, "dur": 1498, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 19, "ts": 1758023635038947, "dur": 1845045, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023632920904, "dur": 7481, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023632928388, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023632928600, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023632929231, "dur": 541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023632929955, "dur": 1239, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\DirectorStyles.cs"}}, {"pid": 12345, "tid": 20, "ts": 1758023632931296, "dur": 539, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\CurveEditUtility.cs"}}, {"pid": 12345, "tid": 20, "ts": 1758023632929772, "dur": 2159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023632931931, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023632932171, "dur": 501, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\InputField.cs"}}, {"pid": 12345, "tid": 20, "ts": 1758023632932156, "dur": 722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023632932878, "dur": 248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023632933126, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023632933341, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023632933555, "dur": 59, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023632933677, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023632934048, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023632934524, "dur": 95, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023632934627, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023632934892, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023632935042, "dur": 468, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1758023632935581, "dur": 1528, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1758023632935526, "dur": 2243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1758023632937861, "dur": 163, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023632938024, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023632938272, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023632938504, "dur": 1663460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023634601966, "dur": 532, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1758023634601965, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1758023634602548, "dur": 1712, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 20, "ts": 1758023634604262, "dur": 2279745, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632920927, "dur": 7470, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632928399, "dur": 96, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632928495, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632928970, "dur": 100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632929070, "dur": 150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632929220, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632929964, "dur": 1102, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.36\\Rider\\Editor\\RiderScriptEditorData.cs"}}, {"pid": 12345, "tid": 21, "ts": 1758023632929963, "dur": 2067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632932030, "dur": 1433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632933464, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632933895, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632934047, "dur": 489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632934536, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632934626, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632934903, "dur": 687, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632935591, "dur": 1197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\3rd\\Demigiant\\DemiLib\\DemiLib.dll"}}, {"pid": 12345, "tid": 21, "ts": 1758023632935591, "dur": 1479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1758023632937071, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632937290, "dur": 197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632937506, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632937632, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632937730, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632937830, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632938017, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632938253, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1758023632938311, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023632938491, "dur": 1663487, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023634601979, "dur": 2281961, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023632920947, "dur": 7645, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023632928598, "dur": 399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023632928997, "dur": 99, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023632929096, "dur": 169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023632929265, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023632929443, "dur": 107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023632929913, "dur": 1123, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\Testing\\TestRunnerCallbacks.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758023632929550, "dur": 1530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023632931080, "dur": 776, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023632932168, "dur": 755, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\uint4.gen.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758023632931856, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023632932923, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023632933543, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023632933807, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023632934055, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023632934531, "dur": 101, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023632934632, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023632934898, "dur": 697, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023632935596, "dur": 1362, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\3rd\\Demigiant\\DemiLib\\DemiLib.dll"}}, {"pid": 12345, "tid": 22, "ts": 1758023632935595, "dur": 1658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1758023632937289, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1758023632937740, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023632937836, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023632938011, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023632938306, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023632938497, "dur": 1663480, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023634601977, "dur": 2281966, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023632920995, "dur": 7714, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023632928713, "dur": 630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023632929900, "dur": 1054, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\UnityAsyncExtensions.AssetBundleRequestAllAssets.cs"}}, {"pid": 12345, "tid": 23, "ts": 1758023632929344, "dur": 1951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023632931735, "dur": 932, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Gluon\\UpdateReport\\UpdateReportListHeaderState.cs"}}, {"pid": 12345, "tid": 23, "ts": 1758023632932684, "dur": 1167, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Gluon\\UpdateProgress.cs"}}, {"pid": 12345, "tid": 23, "ts": 1758023632931296, "dur": 2657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023632933953, "dur": 139, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023632934092, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023632934564, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023632934652, "dur": 2151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023632936803, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023632937028, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023632937175, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023632937242, "dur": 52, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023632937294, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023632937498, "dur": 126, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023632937624, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023632937710, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023632937890, "dur": 141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023632938031, "dur": 253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023632938285, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023632938533, "dur": 1663476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023634602009, "dur": 2281898, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023632921022, "dur": 7701, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023632928725, "dur": 305, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023632929030, "dur": 117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023632929148, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023632929345, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023632929905, "dur": 1222, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Actions\\Menus\\TimelineContextMenu.cs"}}, {"pid": 12345, "tid": 24, "ts": 1758023632931398, "dur": 611, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\TMP_UIStyleManager.cs"}}, {"pid": 12345, "tid": 24, "ts": 1758023632929874, "dur": 2191, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023632932171, "dur": 1856, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\GraphicEditor.cs"}}, {"pid": 12345, "tid": 24, "ts": 1758023632932065, "dur": 2133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023632934198, "dur": 385, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023632934619, "dur": 2197, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023632936816, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023632937049, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023632937184, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023632937311, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023632937541, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023632937627, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023632937748, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023632937837, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023632938020, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023632938267, "dur": 228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023632938495, "dur": 1663476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023634601972, "dur": 2282092, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023632920964, "dur": 7696, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023632928666, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023632928910, "dur": 552, "ph": "X", "name": "File", "args": {"detail": "Assets\\IronSource\\Scripts\\IUnityInterstitial.cs"}}, {"pid": 12345, "tid": 25, "ts": 1758023632928904, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023632929958, "dur": 616, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Window\\TimelineWindow_PlayableLookup.cs"}}, {"pid": 12345, "tid": 25, "ts": 1758023632929576, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023632930620, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023632930797, "dur": 433, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023632931323, "dur": 1007, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\PlasticPlugin.cs"}}, {"pid": 12345, "tid": 25, "ts": 1758023632932330, "dur": 1901, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\PlasticNotification.cs"}}, {"pid": 12345, "tid": 25, "ts": 1758023632931230, "dur": 3128, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023632934358, "dur": 209, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023632934567, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023632934650, "dur": 2172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023632936822, "dur": 221, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023632937043, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023632937190, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023632937264, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023632937317, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023632937535, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023632937731, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023632937830, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023632938013, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023632938252, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1758023632938310, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023632938487, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023632938711, "dur": 1663264, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023634601976, "dur": 2282081, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023632920978, "dur": 7701, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023632928683, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1758023632928911, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\3rd\\Demigiant\\DOTween\\DOTween46.dll"}}, {"pid": 12345, "tid": 26, "ts": 1758023632929602, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 26, "ts": 1758023632930253, "dur": 1038, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\AxisEventData.cs"}}, {"pid": 12345, "tid": 26, "ts": 1758023632931292, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\BaseEventData.cs"}}, {"pid": 12345, "tid": 26, "ts": 1758023632931388, "dur": 325, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventInterfaces.cs"}}, {"pid": 12345, "tid": 26, "ts": 1758023632931714, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventSystem.cs"}}, {"pid": 12345, "tid": 26, "ts": 1758023632931881, "dur": 506, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\Physics2DRaycaster.cs"}}, {"pid": 12345, "tid": 26, "ts": 1758023632932387, "dur": 1994, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\PhysicsRaycaster.cs"}}, {"pid": 12345, "tid": 26, "ts": 1758023632928877, "dur": 5566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1758023632934549, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023632934663, "dur": 316, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023632936829, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1758023632936903, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1758023632937224, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1758023632937310, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1758023632937626, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023632937717, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023632937844, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023632937913, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023632938010, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023632938071, "dur": 203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023632938274, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023632938482, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1758023632938710, "dur": 1663273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023634601983, "dur": 2281954, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632921006, "dur": 7713, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632928722, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632929038, "dur": 108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632929146, "dur": 174, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632929320, "dur": 136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632929456, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632929906, "dur": 1041, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\inspectors\\MarkerInspector.cs"}}, {"pid": 12345, "tid": 27, "ts": 1758023632929767, "dur": 1587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632931354, "dur": 644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632931998, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632932507, "dur": 172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632932679, "dur": 165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632932844, "dur": 270, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632933115, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632933299, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632933538, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632933771, "dur": 273, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632934044, "dur": 472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632934531, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632934630, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632934884, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632935041, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1758023632935330, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1758023632935433, "dur": 1682, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632937159, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Sirenix\\Assemblies\\Sirenix.OdinInspector.Attributes.dll"}}, {"pid": 12345, "tid": 27, "ts": 1758023632937457, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1758023632937122, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1758023632937710, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1758023632938087, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632938259, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023632938503, "dur": 1663466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023634601969, "dur": 2282009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023632921039, "dur": 7688, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023632928727, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023632929025, "dur": 119, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023632929144, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023632929938, "dur": 1240, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\Manipulators\\TimelineClipGroup.cs"}}, {"pid": 12345, "tid": 28, "ts": 1758023632929724, "dur": 1624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023632932375, "dur": 1013, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\AssetsUtils\\RefreshAsset.cs"}}, {"pid": 12345, "tid": 28, "ts": 1758023632931349, "dur": 2107, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023632933456, "dur": 299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023632933755, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023632934060, "dur": 469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023632934529, "dur": 104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023632934633, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023632934891, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023632934977, "dur": 1703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1758023632936689, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1758023632937018, "dur": 621, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023632937642, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1758023632937736, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1758023632938062, "dur": 142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1758023632938305, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1758023632938863, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1758023632938483, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1758023632938965, "dur": 73, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023632939045, "dur": 1659547, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1758023634602196, "dur": 30024, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 28, "ts": 1758023634601964, "dur": 30350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1758023634632584, "dur": 68, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023634632663, "dur": 2059128, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1758023636693143, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 28, "ts": 1758023636693142, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 28, "ts": 1758023636693273, "dur": 426, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 28, "ts": 1758023636693701, "dur": 190308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758023636886384, "dur": 1305, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "netcorerun.dll"}}, {"pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-1"}}, {"pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 35942, "tid": 1, "ts": 1758023632478393, "dur": 378384, "ph": "X", "name": "BuildProgram", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1758023632479454, "dur": 160937, "ph": "X", "name": "BuildProgramContextConstructor", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1758023632829468, "dur": 3204, "ph": "X", "name": "OutputData.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1758023632832674, "dur": 24097, "ph": "X", "name": "Backend.Write", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1758023632833763, "dur": 17573, "ph": "X", "name": "JsonToString", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1758023632861453, "dur": 844, "ph": "X", "name": "", "args": {}}, {"pid": 35942, "tid": 1, "ts": 1758023632861164, "dur": 1286, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1758023625132835, "dur": 20152, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758023625153000, "dur": 5886, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758023625158980, "dur": 576, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758023625159996, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_FFCA22B00416E73E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1758023625168210, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1758023625159570, "dur": 9353, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758023625168930, "dur": 56527, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758023625225459, "dur": 259, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758023625225781, "dur": 5825880, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758023631051813, "dur": 867, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1758023625159598, "dur": 9338, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625169020, "dur": 494, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1758023625168958, "dur": 557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_D699E5DF035CDE66.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758023625169557, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625169733, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1758023625169881, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1758023625170107, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1758023625170480, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625170841, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625171462, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625171932, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625172714, "dur": 790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625173504, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625174559, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625175316, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625175815, "dur": 196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625176011, "dur": 402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625176413, "dur": 274, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625176697, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758023625176768, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1758023625177136, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758023625177196, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625177316, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1758023625177635, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1758023625177929, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758023625178163, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625178476, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625178650, "dur": 124, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625178793, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625178855, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625179393, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625179556, "dur": 157, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625179749, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625180171, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1758023625180240, "dur": 188, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1758023625180438, "dur": 45091, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023625159629, "dur": 9330, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023625169015, "dur": 413, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1758023625168964, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_A062D8221399A90F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758023625169456, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023625169679, "dur": 262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1758023625170375, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758023625170463, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758023625170976, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758023625171230, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758023625171292, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758023625171538, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758023625171713, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758023625171814, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758023625172046, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758023625172123, "dur": 250, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758023625172375, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758023625172651, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758023625172920, "dur": 366, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758023625173422, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\InvalidSignatureException.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758023625173567, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\Is.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758023625173621, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogAssert.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758023625173696, "dur": 437, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogEvent.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758023625174347, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\BaseDelegator.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758023625174498, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\BeforeAfterTestCommandBase.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758023625174576, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableApplyChangesToContextCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758023625174700, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableRepeatedTestCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758023625174891, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\OuterUnityTestActionCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758023625175133, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IAsyncTestAssemblyBuilder.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758023625175205, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ITestSuiteModifier.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758023625175555, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityWorkItemDataHolder.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758023625175618, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\WorkItemFactory.cs"}}, {"pid": 12345, "tid": 2, "ts": 1758023625170014, "dur": 5904, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758023625176094, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758023625176462, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758023625176774, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758023625177027, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023625177093, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758023625177187, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758023625177260, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1758023625177331, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758023625177608, "dur": 276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758023625177884, "dur": 606, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023625178709, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758023625178913, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1758023625178492, "dur": 836, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1758023625179328, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023625179505, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023625179562, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023625179753, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023625180200, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1758023625180455, "dur": 45029, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625159696, "dur": 9317, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625169040, "dur": 318, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1758023625169018, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7C2C4F34C6B423AF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758023625169416, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625169566, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1758023625169565, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_38431C43644D6EBD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758023625169652, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625169831, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1758023625170100, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1758023625170329, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1758023625170382, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625170513, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625170759, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625170844, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625171843, "dur": 1187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625173030, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625173374, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625174232, "dur": 910, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625175142, "dur": 921, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625176063, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625176420, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625176686, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625177077, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1758023625177137, "dur": 668, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625177807, "dur": 749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1758023625178556, "dur": 252, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625178830, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625178945, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625179381, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625179519, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625179592, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625179741, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625179836, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625180186, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1758023625180457, "dur": 45017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625159724, "dur": 9312, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625169053, "dur": 257, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1758023625169040, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_24A8B6F9BAE245F7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758023625169412, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625169487, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625169632, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1758023625169854, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1758023625170083, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1758023625170245, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625170348, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/AppleAuth.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1758023625170435, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625170747, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625170930, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625171328, "dur": 501, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625171829, "dur": 701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625172909, "dur": 569, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\Cli.cs"}}, {"pid": 12345, "tid": 4, "ts": 1758023625172530, "dur": 1644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625174174, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625174689, "dur": 558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625175247, "dur": 612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625175860, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625176033, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625176399, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625176857, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758023625176968, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758023625177069, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758023625177205, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1758023625177312, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625177538, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758023625177900, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625178031, "dur": 453, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1758023625178484, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625178637, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625178784, "dur": 689, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625179550, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625179748, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625180173, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1758023625180464, "dur": 45039, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023625159602, "dur": 9347, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023625168979, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758023625169032, "dur": 264, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1758023625168961, "dur": 335, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_BD6B6DA2CAED0C51.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758023625169334, "dur": 295, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758023625169333, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_22CB9FA5AA6A31F0.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1758023625169644, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1758023625169904, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1758023625170243, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023625170509, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023625170616, "dur": 459, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1758023625171076, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023625172304, "dur": 833, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023625173137, "dur": 1301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023625174943, "dur": 583, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Editor\\UI\\RawImageEditor.cs"}}, {"pid": 12345, "tid": 5, "ts": 1758023625174439, "dur": 1350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023625175790, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023625176009, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023625176438, "dur": 243, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023625176681, "dur": 1481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023625178165, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023625178423, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023625178765, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758023625178956, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 5, "ts": 1758023625178538, "dur": 712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1758023625179250, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023625179490, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023625179549, "dur": 202, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023625179751, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023625180189, "dur": 285, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1758023625180475, "dur": 44990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023625159623, "dur": 9331, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023625169012, "dur": 232, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1758023625168958, "dur": 287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_65323E1310BDBE7B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758023625169379, "dur": 248, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023625169680, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1758023625169965, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1758023625170368, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1758023625170569, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1758023625170642, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1758023625170866, "dur": 597, "ph": "X", "name": "File", "args": {"detail": "Assets\\ZTemp\\QuickActions.cs"}}, {"pid": 12345, "tid": 6, "ts": 1758023625170866, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023625171997, "dur": 705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023625172703, "dur": 1749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023625174452, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023625175729, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023625176021, "dur": 410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023625176431, "dur": 800, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023625177232, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758023625177306, "dur": 1384, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1758023625178709, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\FacebookSDK\\Plugins\\Facebook.Unity.dll"}}, {"pid": 12345, "tid": 6, "ts": 1758023625178691, "dur": 639, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758023625179330, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023625179487, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1758023625179833, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023625180251, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023625180431, "dur": 44670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1758023625225102, "dur": 321, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023625159648, "dur": 9316, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023625169019, "dur": 457, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1758023625168969, "dur": 507, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_D57CD8953DD5F64D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758023625169571, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1758023625169571, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_AEE611DA6D6531BB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1758023625169706, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1758023625169977, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1758023625170193, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1758023625170477, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023625170590, "dur": 318, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1758023625170909, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023625171163, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023625171880, "dur": 1100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023625172981, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023625173643, "dur": 2209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023625175852, "dur": 153, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023625176005, "dur": 438, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023625176443, "dur": 1928, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023625178383, "dur": 344, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1758023625178745, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023625178816, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023625178937, "dur": 390, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023625179327, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023625179491, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023625179548, "dur": 207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023625179755, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023625180176, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1758023625180479, "dur": 45023, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023625159669, "dur": 9301, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023625168978, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1758023625169031, "dur": 404, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1758023625168973, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_7D6B8E0347C20661.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758023625169539, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1758023625169539, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_58061DC3ABA3501B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758023625169677, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023625169741, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1758023625169895, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1758023625170117, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023625170323, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Editor.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1758023625170583, "dur": 303, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1758023625170886, "dur": 584, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\UnityEngine_UI_But<PERSON>_ButtonClickedEventWrap.cs"}}, {"pid": 12345, "tid": 8, "ts": 1758023625170886, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023625171647, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023625172584, "dur": 681, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023625173266, "dur": 1116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023625175009, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\bool4x3.gen.cs"}}, {"pid": 12345, "tid": 8, "ts": 1758023625174383, "dur": 1367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023625175750, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023625176017, "dur": 418, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023625176435, "dur": 321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023625176756, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758023625176826, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023625177096, "dur": 520, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1758023625177617, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023625177695, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758023625177811, "dur": 860, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/WxEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758023625178919, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1758023625178698, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/WxEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1758023625179322, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023625179545, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023625179681, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023625179768, "dur": 383, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023625180170, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1758023625180235, "dur": 215, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1758023625180450, "dur": 45010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023625159692, "dur": 9313, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023625169027, "dur": 395, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1758023625169008, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_59D59D2570B707D8.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758023625169423, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023625169610, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1758023625169566, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_6A4C4ABC6FBD89C0.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758023625169740, "dur": 339, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1758023625170098, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 9, "ts": 1758023625170353, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1758023625170432, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023625170810, "dur": 950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023625171760, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023625172585, "dur": 1133, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023625174102, "dur": 717, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Configuration\\CloudEdition\\Welcome\\AutoLogin.cs"}}, {"pid": 12345, "tid": 9, "ts": 1758023625174942, "dur": 549, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Configuration\\ChannelCertificateUiImpl.cs"}}, {"pid": 12345, "tid": 9, "ts": 1758023625173719, "dur": 2246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023625175965, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023625176032, "dur": 392, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023625176424, "dur": 1423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023625177848, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1758023625178765, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1758023625178517, "dur": 882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1758023625179400, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023625179544, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023625179601, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023625179747, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023625180214, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1758023625180463, "dur": 44999, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023625159719, "dur": 9313, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023625169047, "dur": 294, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1758023625169035, "dur": 306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_BDC059FEE0C56713.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758023625169341, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023625169631, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1758023625169811, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1758023625170023, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023625170166, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023625170463, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023625170549, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UIEffect.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1758023625170830, "dur": 502, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023625171333, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023625172472, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023625173178, "dur": 535, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\TimelinePlayable.cs"}}, {"pid": 12345, "tid": 10, "ts": 1758023625174900, "dur": 696, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Scripting\\PlayableTrack.cs"}}, {"pid": 12345, "tid": 10, "ts": 1758023625173146, "dur": 2619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023625175766, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023625176013, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023625176438, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023625176715, "dur": 1672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758023625178409, "dur": 366, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758023625178920, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1758023625178775, "dur": 853, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1758023625179629, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023625179820, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1758023625180169, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1758023625180236, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1758023625180429, "dur": 370, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1758023625181175, "dur": 72, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1758023625181462, "dur": 5866915, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1758023625159742, "dur": 9303, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023625169060, "dur": 343, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1758023625169048, "dur": 356, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_3D07E2B87C74DF17.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1758023625169522, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023625169731, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 11, "ts": 1758023625170204, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023625170418, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1758023625170575, "dur": 231, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1758023625170839, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023625170911, "dur": 344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023625171256, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023625172391, "dur": 482, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023625172874, "dur": 404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023625173431, "dur": 641, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\Merge\\Developer\\ChangeCategoryTreeViewItem.cs"}}, {"pid": 12345, "tid": 11, "ts": 1758023625174920, "dur": 605, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\Locks\\LocksListHeaderState.cs"}}, {"pid": 12345, "tid": 11, "ts": 1758023625173279, "dur": 2431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023625175710, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023625176012, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023625176439, "dur": 235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023625176674, "dur": 1502, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023625178178, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023625178320, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/CasualGame.Dreamteck.Utilities.Editor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1758023625178442, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023625178646, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023625178772, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023625178927, "dur": 406, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023625179333, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023625179559, "dur": 169, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023625179741, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023625180242, "dur": 201, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1758023625180443, "dur": 45029, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023625159759, "dur": 9293, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023625169065, "dur": 303, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1758023625169055, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_8D0CB068D769A31A.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1758023625169369, "dur": 343, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023625169714, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1758023625169973, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1758023625170430, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023625170570, "dur": 180, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1758023625170782, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023625170876, "dur": 513, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Src\\GenericDelegateBridge.cs"}}, {"pid": 12345, "tid": 12, "ts": 1758023625170876, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023625171866, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023625172916, "dur": 702, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\Channel.cs"}}, {"pid": 12345, "tid": 12, "ts": 1758023625172377, "dur": 2201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023625174578, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023625175426, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023625175605, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023625176018, "dur": 413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023625176431, "dur": 764, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023625177196, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1758023625177266, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/AppleAuth.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1758023625177316, "dur": 670, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023625178710, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll"}}, {"pid": 12345, "tid": 12, "ts": 1758023625179038, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 12, "ts": 1758023625178440, "dur": 759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/AppleAuth.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1758023625179200, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023625179302, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1758023625179749, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023625180202, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1758023625180470, "dur": 45031, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023625159771, "dur": 9285, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023625169074, "dur": 626, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": 1758023625169060, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_1542A8759536CB9C.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1758023625169718, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 13, "ts": 1758023625170048, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023625170284, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1758023625170480, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023625170591, "dur": 323, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/WxEditor.rsp2"}}, {"pid": 12345, "tid": 13, "ts": 1758023625170915, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023625171284, "dur": 664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023625171948, "dur": 781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023625173649, "dur": 645, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\inspectors\\BasicAssetInspector.cs"}}, {"pid": 12345, "tid": 13, "ts": 1758023625174498, "dur": 598, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\DirectorStyles.cs"}}, {"pid": 12345, "tid": 13, "ts": 1758023625172729, "dur": 2367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023625175096, "dur": 825, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023625175921, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023625176040, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023625176417, "dur": 1871, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023625178334, "dur": 226, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Sirenix.OdinInspector.CompatibilityLayer.pdb"}}, {"pid": 12345, "tid": 13, "ts": 1758023625178765, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1758023625178561, "dur": 957, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1758023625179519, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023625179619, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023625179766, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023625180152, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023625180250, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1758023625180437, "dur": 45022, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023625159792, "dur": 9269, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023625169079, "dur": 301, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 14, "ts": 1758023625169064, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_7B26F11653A8BB46.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1758023625169380, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023625169617, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1758023625169616, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_AD339B360B767264.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1758023625169745, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1758023625170074, "dur": 187, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1758023625170334, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1758023625170505, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023625170603, "dur": 398, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1758023625171002, "dur": 1328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023625172330, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023625174076, "dur": 785, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\ObjectMenuCreation\\MenuItems.cs"}}, {"pid": 12345, "tid": 14, "ts": 1758023625173116, "dur": 2102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023625175219, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023625175861, "dur": 191, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023625176052, "dur": 375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023625176427, "dur": 900, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023625177327, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1758023625177579, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023625177656, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UIEffect.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1758023625177733, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1758023625177813, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1758023625177889, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1758023625178434, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\3rd\\Demigiant\\DemiLib\\DemiLib.dll"}}, {"pid": 12345, "tid": 14, "ts": 1758023625178765, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1758023625178433, "dur": 986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1758023625179419, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023625179563, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023625179743, "dur": 477, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023625180221, "dur": 239, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1758023625180460, "dur": 45023, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625159805, "dur": 9265, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625169085, "dur": 601, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 15, "ts": 1758023625169072, "dur": 614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_4FFE5013DEBA169E.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1758023625169707, "dur": 591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1758023625170298, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625170518, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625170726, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625170860, "dur": 529, "ph": "X", "name": "File", "args": {"detail": "Assets\\3rd\\AssetDanshari\\Editor\\TreeDataModel\\AssetMultiColumnHeader.cs"}}, {"pid": 12345, "tid": 15, "ts": 1758023625170860, "dur": 1037, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625172905, "dur": 591, "ph": "X", "name": "File", "args": {"detail": "Assets\\WX-WASM-SDK-V2\\Editor\\WXPluginVersion.cs"}}, {"pid": 12345, "tid": 15, "ts": 1758023625171897, "dur": 1599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625173734, "dur": 571, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\UI\\Tree\\ListViewItemIds.cs"}}, {"pid": 12345, "tid": 15, "ts": 1758023625174834, "dur": 613, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\UI\\Progress\\ProgressControlsForViews.cs"}}, {"pid": 12345, "tid": 15, "ts": 1758023625173497, "dur": 1960, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625175457, "dur": 144, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625175601, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625176022, "dur": 408, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625176430, "dur": 888, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625177318, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1758023625177599, "dur": 414, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625178038, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1758023625178124, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UIEffect-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1758023625178253, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625178326, "dur": 158, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Sprite.Editor.pdb"}}, {"pid": 12345, "tid": 15, "ts": 1758023625178487, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625178734, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625178834, "dur": 498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625179333, "dur": 155, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625179488, "dur": 56, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625179544, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625179609, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625179749, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625180195, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1758023625180472, "dur": 45037, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023625159820, "dur": 9253, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023625169088, "dur": 615, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 16, "ts": 1758023625169076, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_F085666F1110AAE3.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1758023625169717, "dur": 415, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1758023625170208, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1758023625170303, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023625170489, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023625170602, "dur": 369, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1758023625170972, "dur": 1465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023625172437, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023625173214, "dur": 1401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023625174616, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023625175177, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023625175852, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023625176015, "dur": 422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023625176437, "dur": 313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023625176750, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1758023625176850, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1758023625177195, "dur": 1311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023625178632, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1758023625178759, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1758023625178915, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 16, "ts": 1758023625179153, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 16, "ts": 1758023625179336, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 16, "ts": 1758023625178508, "dur": 1036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1758023625179544, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023625179742, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023625180233, "dur": 217, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1758023625180450, "dur": 45006, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625159847, "dur": 9230, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625169093, "dur": 306, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 17, "ts": 1758023625169080, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_7F3AF681D6CECB6F.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1758023625169399, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625169725, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1758023625169917, "dur": 544, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1758023625170462, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625170595, "dur": 326, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1758023625170921, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625171139, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625171809, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625172451, "dur": 359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625172810, "dur": 804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625173615, "dur": 729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625174344, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625175059, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625175422, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625175578, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625176029, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625176425, "dur": 1195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625177620, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1758023625177915, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1758023625178033, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1758023625178321, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625178409, "dur": 371, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1758023625178784, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625179056, "dur": 246, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625179355, "dur": 159, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625179545, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625179754, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625180183, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1758023625180477, "dur": 45002, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023625159859, "dur": 9225, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023625169096, "dur": 300, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 18, "ts": 1758023625169086, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F5D6C7D883717C04.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1758023625169397, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023625169611, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1758023625169610, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_0EED61BE115AECA6.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1758023625169744, "dur": 551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1758023625170406, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1758023625170596, "dur": 336, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1758023625170932, "dur": 1478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023625172410, "dur": 706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023625173117, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023625174111, "dur": 569, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023625174680, "dur": 556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023625175237, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023625175876, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023625176044, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023625176410, "dur": 1928, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023625178346, "dur": 310, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.dll"}}, {"pid": 12345, "tid": 18, "ts": 1758023625178656, "dur": 90, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023625178783, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023625178892, "dur": 440, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023625179332, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023625179568, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023625179741, "dur": 503, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023625180244, "dur": 193, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1758023625180437, "dur": 45036, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023625159872, "dur": 9215, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023625169100, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 19, "ts": 1758023625169090, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_FFCA22B00416E73E.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1758023625169354, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023625169673, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023625169827, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1758023625169918, "dur": 384, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Runtime.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1758023625170332, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1758023625170460, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023625170584, "dur": 296, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1758023625170880, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023625171478, "dur": 902, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023625172381, "dur": 427, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023625172809, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023625173210, "dur": 900, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Control\\ControlPlayableAsset.cs"}}, {"pid": 12345, "tid": 19, "ts": 1758023625173210, "dur": 1548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023625174758, "dur": 773, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023625175582, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023625176025, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023625176429, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023625176683, "dur": 1658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023625178381, "dur": 339, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 19, "ts": 1758023625178794, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023625178900, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023625179325, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023625179446, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023625179637, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023625179748, "dur": 460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023625180208, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1758023625180466, "dur": 45009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625159886, "dur": 9205, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625169104, "dur": 251, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 20, "ts": 1758023625169094, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_389F212FAF489129.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1758023625169566, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625169738, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1758023625170055, "dur": 253, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1758023625170326, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1758023625170524, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625170684, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1758023625170798, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625170991, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625172037, "dur": 688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625172725, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625173643, "dur": 1313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625174957, "dur": 867, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625175824, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625176005, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625176414, "dur": 267, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625176681, "dur": 525, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625177207, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Wx.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1758023625177260, "dur": 730, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625177998, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Wx.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1758023625178283, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625178447, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625178647, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625178792, "dur": 69, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625178861, "dur": 526, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625179387, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625179608, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625179700, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625179755, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625180152, "dur": 61, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625180213, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1758023625180445, "dur": 45010, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023625159907, "dur": 9188, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023625169110, "dur": 254, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 21, "ts": 1758023625169098, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_58980D046BFD513D.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1758023625169390, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023625169633, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1758023625169931, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1758023625170436, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023625170525, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023625170643, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1758023625170825, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023625171509, "dur": 516, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023625172025, "dur": 674, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023625173426, "dur": 688, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Editor\\TimelineSelection.cs"}}, {"pid": 12345, "tid": 21, "ts": 1758023625172699, "dur": 1415, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023625174115, "dur": 697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023625174812, "dur": 514, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023625175326, "dur": 109, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023625175435, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023625175571, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023625176018, "dur": 388, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023625176407, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023625176697, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1758023625176842, "dur": 1126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023625177969, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1758023625178482, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023625178798, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1758023625178901, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023625179037, "dur": 285, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1758023625178968, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1758023625179747, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023625180179, "dur": 283, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1758023625180462, "dur": 44991, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023625159924, "dur": 9176, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023625169121, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 22, "ts": 1758023625169103, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_FEB44C0922F521E3.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1758023625169375, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023625169485, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023625169690, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1758023625170233, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\UnityChannel\\ChannelPurchase.dll"}}, {"pid": 12345, "tid": 22, "ts": 1758023625170872, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 22, "ts": 1758023625170979, "dur": 210, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 22, "ts": 1758023625171237, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 22, "ts": 1758023625171304, "dur": 247, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 22, "ts": 1758023625171636, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 22, "ts": 1758023625171802, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 22, "ts": 1758023625172119, "dur": 231, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 22, "ts": 1758023625172350, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 22, "ts": 1758023625172639, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 22, "ts": 1758023625172910, "dur": 411, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 22, "ts": 1758023625173442, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventHandle.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758023625173562, "dur": 542, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventSystem.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758023625174134, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInput.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758023625174291, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycasterManager.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758023625174464, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycastResult.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758023625174555, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIElements\\PanelEventHandler.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758023625174690, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\AnimationTriggers.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758023625174778, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\CanvasUpdateRegistry.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758023625174931, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\IClipRegion.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758023625175019, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\DefaultControls.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758023625175125, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Dropdown.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758023625175265, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\CanvasScaler.cs"}}, {"pid": 12345, "tid": 22, "ts": 1758023625170047, "dur": 5467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1758023625175590, "dur": 421, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023625176011, "dur": 403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023625176414, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023625176723, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1758023625176858, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1758023625177254, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023625177322, "dur": 457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1758023625177779, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023625177904, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1758023625178013, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UIEffect.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1758023625178303, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023625178375, "dur": 341, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UIEffect.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1758023625179026, "dur": 336, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll"}}, {"pid": 12345, "tid": 22, "ts": 1758023625178733, "dur": 807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UIEffect-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1758023625179591, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023625179701, "dur": 54, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023625179755, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023625180152, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023625180219, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1758023625180438, "dur": 45041, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625159936, "dur": 9168, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625169122, "dur": 262, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 23, "ts": 1758023625169107, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_F61FF965A6F6FAF4.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1758023625169439, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625169707, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1758023625169879, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1758023625170172, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 23, "ts": 1758023625170382, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625170510, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625170568, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1758023625170703, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625170783, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625170916, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625171157, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625171364, "dur": 721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625172085, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625172533, "dur": 684, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625173218, "dur": 410, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625173628, "dur": 996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625174655, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625174861, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625175421, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625175584, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625176018, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625176407, "dur": 289, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625176696, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1758023625176771, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1758023625177043, "dur": 934, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625177998, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1758023625178073, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625178303, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1758023625178630, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625178757, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625178835, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1758023625178994, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1758023625179369, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625179576, "dur": 131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625179754, "dur": 404, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625180207, "dur": 244, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1758023625180451, "dur": 45034, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023625159952, "dur": 9155, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023625169122, "dur": 278, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 24, "ts": 1758023625169110, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_0D716A535EA5D9A0.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1758023625169569, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023625169670, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023625169894, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1758023625170047, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023625170229, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1758023625170458, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023625170742, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1758023625170917, "dur": 480, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023625171398, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023625172059, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023625172512, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023625174949, "dur": 600, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.7\\Runtime\\Events\\MarkerTrack.cs"}}, {"pid": 12345, "tid": 24, "ts": 1758023625173169, "dur": 2698, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023625175867, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023625176049, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023625176450, "dur": 1434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023625177888, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1758023625178037, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1758023625178144, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023625178424, "dur": 184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll"}}, {"pid": 12345, "tid": 24, "ts": 1758023625178709, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 24, "ts": 1758023625178306, "dur": 668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1758023625178975, "dur": 618, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023625179617, "dur": 125, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023625179742, "dur": 485, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023625180227, "dur": 230, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1758023625180457, "dur": 45019, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023625159974, "dur": 9137, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023625169127, "dur": 574, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 25, "ts": 1758023625169113, "dur": 588, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_243621AF56FD8BB7.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1758023625169716, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1758023625170195, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1758023625170516, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023625170636, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1758023625170812, "dur": 1165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023625172363, "dur": 587, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.cysharp.unitask@95998ff3f2\\Runtime\\Linq\\Merge.cs"}}, {"pid": 12345, "tid": 25, "ts": 1758023625171978, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023625173023, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023625173434, "dur": 909, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023625174755, "dur": 648, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\math.cs"}}, {"pid": 12345, "tid": 25, "ts": 1758023625174343, "dur": 1378, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023625175721, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023625176006, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023625176442, "dur": 233, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023625176675, "dur": 1671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023625178355, "dur": 324, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 25, "ts": 1758023625178680, "dur": 53, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023625178801, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1758023625178919, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1758023625179358, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023625179436, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023625179559, "dur": 147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023625179706, "dur": 57, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023625179763, "dur": 395, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023625180158, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023625180249, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1758023625180428, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1758023625180671, "dur": 44796, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023625159989, "dur": 9126, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023625169138, "dur": 273, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 26, "ts": 1758023625169117, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_C549C74F0FF91F05.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1758023625169412, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023625169564, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023625169697, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 26, "ts": 1758023625169810, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 26, "ts": 1758023625169937, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1758023625170231, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023625170508, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023625170609, "dur": 408, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.Linq.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1758023625171017, "dur": 600, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023625171617, "dur": 443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023625172061, "dur": 509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023625172570, "dur": 506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023625173076, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023625174125, "dur": 799, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023625174924, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023625175423, "dur": 151, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023625175574, "dur": 459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023625176033, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023625176422, "dur": 1479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023625177902, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1758023625178325, "dur": 164, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Lofelt.NiceVibrations.pdb"}}, {"pid": 12345, "tid": 26, "ts": 1758023625178513, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UniTask.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1758023625179004, "dur": 461, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023625179552, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023625179756, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023625180170, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1758023625180238, "dur": 206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1758023625180444, "dur": 45017, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625160002, "dur": 9116, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625169139, "dur": 274, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 27, "ts": 1758023625169121, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_7B54AD0B13F0D210.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1758023625169414, "dur": 284, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625169700, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 27, "ts": 1758023625169860, "dur": 233, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 27, "ts": 1758023625170193, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1758023625170467, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625170590, "dur": 300, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UniTask.DOTween.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 27, "ts": 1758023625170891, "dur": 552, "ph": "X", "name": "File", "args": {"detail": "Assets\\XLua\\Gen\\UnityEngine_RendererWrap.cs"}}, {"pid": 12345, "tid": 27, "ts": 1758023625170891, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625171735, "dur": 810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625172546, "dur": 419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625172965, "dur": 388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625173644, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.7.1\\Editor\\Views\\Diff\\Dialogs\\GetRestorePathDialog.cs"}}, {"pid": 12345, "tid": 27, "ts": 1758023625173354, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625174337, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625175184, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625175875, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625176052, "dur": 394, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625176446, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625176708, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1758023625176793, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1758023625177119, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1758023625177182, "dur": 796, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625178003, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1758023625178345, "dur": 220, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityWebSocket.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1758023625178628, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625178766, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625178885, "dur": 453, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625179338, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625179571, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625179708, "dur": 55, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625179763, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625180160, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625180248, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625180430, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1758023625180683, "dur": 44795, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625160021, "dur": 9105, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625169138, "dur": 281, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 28, "ts": 1758023625169127, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_A31099604CCBA22C.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1758023625169465, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625169682, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1758023625170047, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.rsp2"}}, {"pid": 12345, "tid": 28, "ts": 1758023625170304, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Wx.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1758023625170515, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625170614, "dur": 408, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1758023625171023, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625171665, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625172276, "dur": 447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625172723, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625173245, "dur": 387, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625173632, "dur": 1402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625175035, "dur": 416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625175451, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625175572, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625176037, "dur": 384, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625176421, "dur": 1860, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625178283, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625178354, "dur": 316, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/CasualGame.Dreamteck.Utilities.Editor.pdb"}}, {"pid": 12345, "tid": 28, "ts": 1758023625178670, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625178772, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625178879, "dur": 466, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625179345, "dur": 160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625179554, "dur": 158, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625179713, "dur": 51, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625179764, "dur": 491, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625180255, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1758023625180482, "dur": 45005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1758023631053843, "dur": 1644, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 20044, "tid": 9341, "ts": 1758023636896033, "dur": 1309, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"}}, {"pid": 20044, "tid": 9341, "ts": 1758023636897953, "dur": 21, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"}}, {"pid": 20044, "tid": 9341, "ts": 1758023636898041, "dur": 8, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 20044, "tid": 9341, "ts": 1758023636897374, "dur": 577, "ph": "X", "name": "backend2.traceevents", "args": {}}, {"pid": 20044, "tid": 9341, "ts": 1758023636897991, "dur": 50, "ph": "X", "name": "buildprogram0.traceevents", "args": {}}, {"pid": 20044, "tid": 9341, "ts": 1758023636898056, "dur": 313, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 20044, "tid": 9341, "ts": 1758023636893635, "dur": 5305, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}