﻿using System;
using UnityEngine;
using System.IO;
using System.Text;
using System.Diagnostics;
using System.Linq;
using System.Collections.Generic;

[Flags]
public enum LogLevel
{
    NONE = 0,
    DEBUG = 1,
    INFO = 2,
    WARNING = 4,
    ERROR = 8,
    EXCEPT = 16,
    CRITICAL = 32,
}

public class LogWriter
{
    public const string DEF_LOG_FILE_FORMAT = "{0}_{1}.txt";
    public const string DEF_LOG_FILE_TEMP_FORMAT = "log_{0}.txt";

    enum WriteType
    {
        Cache2Write,
        DoWrite
    }
    private WriteType m_writeType = WriteType.Cache2Write;

    private string m_logPath = null;
    private string m_logFilePath = null;

    private FileStream m_FileStream = null;
    private StreamWriter m_StreamWriter = null;

    private StringBuilder m_LogRecord = null;

    private readonly static object m_locker = new object();

    public LogWriter()
    {
        m_logPath = GameHelper.persistentDataPath + "/log/";

        if (!Directory.Exists(m_logPath))
            Directory.CreateDirectory(m_logPath);

        m_logFilePath = String.Concat(m_logPath, String.Format(DEF_LOG_FILE_TEMP_FORMAT, DateTime.Today.ToString("yyyyMMdd")));

        try
        {
            if (m_writeType == WriteType.Cache2Write)
            {
                m_LogRecord = new StringBuilder();
            }
            else
            {
                m_FileStream = new FileStream(m_logFilePath, FileMode.Append, FileAccess.Write, FileShare.ReadWrite);
                m_StreamWriter = new StreamWriter(m_FileStream);
            }
        }
        catch (Exception ex)
        {
            UnityEngine.Debug.LogError(ex.Message);
        }
    }

    public void Release()
    {
        lock (m_locker)
        {
            if (m_StreamWriter != null)
            {
                //m_StreamWriter.Flush();
                m_StreamWriter.Close();
                m_StreamWriter.Dispose();
            }

            if (m_FileStream != null)
            {
                //m_FileStream.Flush();
                m_FileStream.Close();
                m_FileStream.Dispose();
            }
        }
    }
    public void ReName(string logTitle = null, string time = null)
    {
        if (string.IsNullOrEmpty(logTitle.ToString())) return;
        if (string.IsNullOrEmpty(time.ToString())) return;

        string title = logTitle.Replace(".", "_");
        string movePath = string.Concat(m_logPath, String.Format(DEF_LOG_FILE_FORMAT, title, time));

        if (m_writeType == WriteType.DoWrite)
        {
            File.Move(m_logFilePath, movePath);
        }
        else
        {
            FileUtility.SafeWriteAllText(movePath, m_LogRecord.ToString());
        }
    }

    public void WriteLog(string msg, LogLevel level)
    {
        lock (m_locker)
        {
            try
            {
                if (m_writeType == WriteType.Cache2Write)
                {
                    m_LogRecord = m_LogRecord.AppendLine(msg);
                    m_LogRecord = m_LogRecord.Append("\r\n");
                }
                else
                {
                    m_StreamWriter.WriteLine(msg);
                    m_StreamWriter.Flush();
                }
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError(ex.Message);
            }
        }
    }

    public List<string> GetLogs()
    {
        if (Directory.Exists(m_logPath))
            return Directory.GetFiles(m_logPath, "*.txt", SearchOption.AllDirectories).ToList();
        else
            return null;
    }

    //get by timedate
    public List<string> GetLogs(string timeSt, string timeEd)
    {
        if (string.IsNullOrEmpty(timeSt)) return null;
        if (string.IsNullOrEmpty(timeEd)) return null;

        List<string> source = GetLogs();

        if (source == null || source.Count == 0)
            return null;

        try
        {
            List<string> retList = new List<string>();
            Dictionary<string, string> sourceDic = new Dictionary<string, string>();
            for (int index = 0; index < source.Count; index++)
            {
                string name = Path.GetFileNameWithoutExtension(source[index]);
                string[] strs = name.Split('_');
                if (strs.Length == 3)
                    sourceDic.Add(strs[2], source[index]);
            }

            if (timeSt.Equals(timeEd))
            {
                foreach (var data in sourceDic)
                {
                    if (data.Key.CompareTo(timeSt) == 0)
                    {
                        retList.Add(data.Value);
                        break;
                    }
                }
            }
            else
            {
                foreach (var data in sourceDic)
                {
                    if (data.Key.CompareTo(timeSt) > 0 && data.Key.CompareTo(timeEd) < 0)
                        retList.Add(data.Value);
                }
            }
            return retList;
        }
        catch (Exception ex)
        {
            UnityEngine.Debug.LogError(ex.Message);
        }

        return null;
    }
}

public class LogMan
{
    private const String DEF_NEW_RUN = "---------------------- Program Line ---------------------------";
    private const String DEF_END_RUN = "---------------------- End ---------------------------";

    /// default param in method
    public const Boolean ShowStack = true;

    // is Write log
    public static Boolean LogReport = true;
    public static Boolean LogWrite = false;

    // filter of level
    public static LogLevel CurrentLogLevels = LogLevel.DEBUG | LogLevel.INFO | LogLevel.WARNING | LogLevel.ERROR | LogLevel.CRITICAL | LogLevel.EXCEPT;

    //writer
    private static LogWriter logWriter = null;

    public static void Init()
    {
#if UNITY_EDITOR
        LogWrite = true;
#endif
        logWriter = new LogWriter();

        Application.logMessageReceived += ProcessExceptionReport;

        Log(string.Format(DEF_NEW_RUN, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")), LogLevel.INFO);
    }

    public static void Release()
    {
        Log(string.Format(DEF_END_RUN, DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")), LogLevel.INFO);
        logWriter.Release();
    }

    public static List<string> GetLogs(string timeSt = null, string timeEd = null)
    {
        if (timeSt == null)
            return logWriter.GetLogs();
        return logWriter.GetLogs(timeSt, timeEd);
    }

    public static void ReNameLogFile(string logTitle, string time)
    {
        logWriter.ReName(logTitle, time);
    }

    public static string getStackInfo(bool isShowStack)
    {
 #if UNITY_EDITOR
        return isShowStack ? string.Concat("\n", GetStackInfo()) : "";
#endif
        return "";
    }

    public static void Debug(object message, Boolean isShowStack = ShowStack)
    {
        if (LogLevel.DEBUG == (CurrentLogLevels & LogLevel.DEBUG))
            Log(string.Concat(message, getStackInfo(isShowStack)), LogLevel.DEBUG);
    }

    public static void Info(object message, Boolean isShowStack = ShowStack)
    {
        if (LogLevel.INFO == (CurrentLogLevels & LogLevel.INFO))
            Log(string.Concat(message, getStackInfo(isShowStack)), LogLevel.INFO);
    }

    public static void Warning(object message, Boolean isShowStack = ShowStack)
    {
        if (LogLevel.WARNING == (CurrentLogLevels & LogLevel.WARNING))
            Log(string.Concat(message, getStackInfo(isShowStack)), LogLevel.WARNING);
    }

    public static void Error(object message, Boolean isShowStack = ShowStack)
    {
        if (LogLevel.ERROR == (CurrentLogLevels & LogLevel.ERROR))
            Log(string.Concat(message, getStackInfo(isShowStack)), LogLevel.ERROR);
    }

    public static void Critical(object message, Boolean isShowStack = ShowStack)
    {
        if (LogLevel.CRITICAL == (CurrentLogLevels & LogLevel.CRITICAL))
            Log(string.Concat(message, getStackInfo(isShowStack)), LogLevel.CRITICAL);
    }

    public static void Except(Exception ex, object message = null)
    {
        if (LogLevel.EXCEPT == (CurrentLogLevels & LogLevel.EXCEPT))
        {
            Exception innerException = ex;
            while (innerException.InnerException != null)
            {
                innerException = innerException.InnerException;
            }
            Log(string.Concat(message == null ? "" : message + "\n", ex.Message, innerException.StackTrace), LogLevel.CRITICAL);
        }
    }
    /////////////////////////////////////////////////////////////////////////////

    private static String GetStacksInfo()
    {
        StringBuilder sb = new StringBuilder();
        StackTrace st = new StackTrace();
        var sf = st.GetFrames();
        for (int i = 2; i < sf.Length; i++)
        {
            sb.AppendLine(sf[i].ToString());
        }

        return sb.ToString();
    }

    private static String GetStackInfo()
    {
        StackTrace st = new StackTrace();
        StackFrame sf = st.GetFrame(2);//[0]self [1]referencer
        var method = sf.GetMethod();
        return String.Format("{0}.{1}(): ", method.ReflectedType.Name, method.Name);
    }

    private static void Log(string message, LogLevel level)
    {
        if (!LogReport)
            return;

        var msg = string.Concat(message, "\n", level.ToString(), " ", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss,fff"));

        switch (level)
        {
            case LogLevel.DEBUG:
            case LogLevel.INFO:
                UnityEngine.Debug.Log(msg);
                break;
            case LogLevel.WARNING:
                UnityEngine.Debug.LogWarning(msg);
                break;
            case LogLevel.ERROR:
            case LogLevel.EXCEPT:
            case LogLevel.CRITICAL:
                UnityEngine.Debug.LogError(msg);
                break;
            default:
                break;
        }

        if (LogWrite)
        {
            logWriter.WriteLog(msg, level);
        }
    }

    private static void ProcessExceptionReport(string message, string stackTrace, LogType type)
    {
        var logLevel = LogLevel.DEBUG;
        switch (type)
        {
            case LogType.Assert:
                logLevel = LogLevel.DEBUG;
                break;
            case LogType.Error:
                logLevel = LogLevel.ERROR;
                break;
            case LogType.Exception:
                logLevel = LogLevel.EXCEPT;
                break;
            case LogType.Log:
                logLevel = LogLevel.DEBUG;
                break;
            case LogType.Warning:
                logLevel = LogLevel.WARNING;
                break;
            default:
                break;
        }
        if (logLevel != (CurrentLogLevels & logLevel))
        {
            Log(string.Concat(" [SYS_", logLevel, "]: \n", message, '\n', stackTrace), logLevel);
        }
    }

    public static string DEF_STR_LUA_LOG = "[ LUA ] ";
    public static string DEF_STR_LUA_EX_ERR = "LUA_Exception : ";
    public static void LuaError(System.String strErr)
    {
        Error(DEF_STR_LUA_EX_ERR + strErr);
    }

}