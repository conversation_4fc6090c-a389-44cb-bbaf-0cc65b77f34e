﻿using UnityEngine;
using System.Collections;
using UnityEngine.Networking;
using System.Collections.Generic;
using GameNetWork.Net;

public class HttpMono : MonoBehaviour
{
        public static HttpMono mInstance = null;
        public static HttpMono Instance
        {
                get
                {
                        if (mInstance == null)
                        {
                                mInstance = GameObject.FindObjectOfType(typeof(HttpMono)) as HttpMono;
                                if (mInstance == null)
                                {
                                        GameObject go = new GameObject(typeof(HttpMono).Name);
                                        mInstance = go.AddComponent<HttpMono>();
                                        GameObject parent = GameObject.Find("Managers");
                                        if (parent == null)
                                        {
                                                parent = new GameObject("Managers");
                                                GameObject.DontDestroyOnLoad(parent);
                                        }
                                        if (parent != null)
                                        {
                                                go.transform.parent = parent.transform;
                                        }
                                }
                        }
                        return mInstance;
                }
        }

        private void Update()
        {
                if (StartGame.Instance.IsUseNet)
                {
                        GameHttp.Instance.onUpdateMainThread();
                }
        }

        public void Init()
        {
                LogMan.Info("****** HttpMono Win");

                GameHttp.Instance.Init();
        }


    public void SendToUC2(int packetId, string url, Dictionary<string, string> param, System.Action<int, long, string, object> callback, Dictionary<string, string> replaceParam = null)
    {
        if (!StartGame.Instance.IsUseNet)
        {
            Debug.Log("-- 去掉网络");
            return;
        }
        HttpPacket packet = new HttpPacket();
        packet.Id = packetId;
        packet.Url = url;
        packet.Method = HTTPMethods.Get;
        packet.Params = param;
        packet.ReplaceParams = replaceParam;
        //packet.CallBack = (request, response) =>
        //{
        //    InvokeCallback(request, response, callback);
        //};
        //GameHttp.Instance.SendHttpPacket(packet);
        string u = GameHttp.Instance.GetLoginURL(packet);
        HTTPGet(u, (a ,b,c) => {
            callback(2, b, c, null);
        });

    }
    
        public void SendToUC(int packetId, string url, Dictionary<string, string> param, System.Action<int, long, string, object> callback, Dictionary<string, string> replaceParam = null)
        {
                if (!StartGame.Instance.IsUseNet)
                {
                        Debug.Log("-- 去掉网络");
                        return;
                }
                HttpPacket packet = new HttpPacket();
                packet.Id = packetId;
                packet.Url = url;
                packet.Method = HTTPMethods.Get;
                packet.Params = param;
                packet.ReplaceParams = replaceParam;
                packet.CallBack = (request, response) =>
                {
                        InvokeCallback(request, response, callback);
                };
                GameHttp.Instance.SendHttpPacket(packet);
        }

        public void SendToGS(int packetId, string url, Dictionary<string, string> param, System.Action<int, long, string, object> callback, Dictionary<string, string> replaceParam = null)
        {
                if (!StartGame.Instance.IsUseNet)
                {
                        Debug.Log("-- 去掉网络");
                        return;
                }
                HttpPacket packet = new HttpPacket();
                packet.Id = packetId;
                packet.Url = url;
                packet.Method = HTTPMethods.Post;
                packet.Params = param;
                packet.ReplaceParams = replaceParam;
                packet.IsZip = !GameHelper.IsWebGL();
                packet.CallBack = (request, response) =>
                {
                        InvokeCallback(request, response, callback);
                };
                GameHttp.Instance.SendHttpPacket(packet);
        }

        public void SendToSDK(int packetId, string url, int method, Dictionary<string, string> param, System.Action<int, long, string, object> callback, object ext1 = null)
        {
                if (!StartGame.Instance.IsUseTCPNet)
                {
                        Debug.Log("-- 去掉网络");
                        return;
                }
                HttpPacket packet = new HttpPacket();
                packet.Id = packetId;
                packet.Url = url;
                packet.Method = (HTTPMethods)method;
                packet.Params = param;
                packet.ex1 = ext1;
                packet.CallBack = (request, response) =>
                {
                        InvokeCallback(request, response, callback);
                };
                GameHttp.Instance.SendHttpPacket(packet);
        }
        private void InvokeCallback(HTTPRequest request, HTTPResponse response, System.Action<int, long, string, object> callback)
        {
                try
                {
                        if (callback == null)
                                return;

                        HttpPacket packet = request.UserData as HttpPacket;
                        int state = (int)request.State;
                        string result = response.DataAsText;
                        object ext1 = packet.ex1;
                        callback(state, packet.Id, result, ext1);
                }
                catch (System.Exception e)
                {
                        LogMan.Error("###### InvokeCallback: " + e.Message);

                        if (response!= null && response.DataAsText != null)
                            LogMan.Error("###### ErrResData: " + response.DataAsText);
                        if (request != null)
                            LogMan.Error("###### ErrReqData: " + request.DebugInfo());
                }
        }

        public void HTTPGet(string url, System.Action<int, long, string> callback)
        {
                if (!StartGame.Instance.IsUseNet)
                {
                        Debug.Log("-- 去掉网络");
                        return;
                }
                StartCoroutine(Get(url, callback));
        }

        public void HTTPPost(string url, string strMsg, Dictionary<string, string> headers, System.Action<int, long, string> callback)
        {
                if (!StartGame.Instance.IsUseNet)
                {
                        Debug.Log("-- 去掉网络");
                        return;
                }
                byte[] postData = System.Text.Encoding.UTF8.GetBytes(strMsg);
                strMsg = null;

                StartCoroutine(Post(url, postData, headers, callback));
                postData = null;
        }

        public IEnumerator Get(string url, System.Action<int, long, string> callback)
        {
                using (UnityWebRequest req = UnityWebRequest.Get(url))
                {
                        UnityWebRequestAsyncOperation operation = req.SendWebRequest();

                        yield return operation;

                        int errorState = 0;
                        string result = string.Empty;
                        if (req.isHttpError || req.isNetworkError)
                        {
                                result = req.error;
                                errorState = 1;
                        }
                        else
                        {
                                result = req.downloadHandler.text;
                        }
                        long code = req.responseCode;

                        if (callback != null)
                                callback(errorState, code, result);
                }
        }
        public IEnumerator GetFile(string url, int pathType, System.Action<int, long, string> callback)
        {
                if (pathType == 0)
                {
                        if (Application.platform == RuntimePlatform.Android)
                                url = "file://" + url;
                }

                if (Application.platform == RuntimePlatform.IPhonePlayer || Application.platform == RuntimePlatform.OSXEditor)
                        url = "file://" + url;

                Debug.Log("****** GetFile: \n" + url);
                using (UnityWebRequest req = UnityWebRequest.Get(url))
                {
                        UnityWebRequestAsyncOperation operation = req.SendWebRequest();

                        yield return operation;

                        int errorState = 0;
                        string result = string.Empty;
                        if (req.isHttpError || req.isNetworkError)
                        {
                                result = req.error;
                                errorState = 1;
                        }
                        else
                        {
                                result = req.downloadHandler.text;
                        }
                        long code = req.responseCode;

                        if (callback != null)
                                callback(errorState, code, result);
                }
        }

        public IEnumerator GetBytes(string url, System.Action<int, long, object> callback)
        {
                using (UnityWebRequest req = UnityWebRequest.Get(url))
                {
                        UnityWebRequestAsyncOperation operation = req.SendWebRequest();

                        yield return operation;

                        int errorState = 0;
                        object result = null;
                        if (req.isHttpError || req.isNetworkError)
                        {
                                result = req.error;
                                errorState = 1;
                        }
                        else
                        {
                                result = req.downloadHandler.data;
                        }
                        long code = req.responseCode;

                        if (callback != null)
                                callback(errorState, code, result);
                }
        }

        public IEnumerator Post(string url, byte[] postBytes, Dictionary<string, string> headers, System.Action<int, long, string> callback)
        {
                using (UnityWebRequest req = new UnityWebRequest(url, "POST"))
                {
                        req.downloadHandler = new DownloadHandlerBuffer();
                        req.uploadHandler = new UploadHandlerRaw(postBytes);
                        postBytes = null;

                        if (headers != null)
                        {
                                foreach (KeyValuePair<string, string> kv in headers)
                                {
                                        req.SetRequestHeader(kv.Key, kv.Value);
                                }
                        }
                        UnityWebRequestAsyncOperation operation = req.SendWebRequest();

                        yield return operation;

                        int errorState = 0;
                        string result = string.Empty;
                        if (req.isHttpError || req.isNetworkError)
                        {
                                result = req.error;
                                errorState = 1;
                        }
                        else
                        {
                                result = req.downloadHandler.text;
                        }
                        long code = req.responseCode;

                        if (callback != null)
                                callback(errorState, code, result);
                }
        }
}
