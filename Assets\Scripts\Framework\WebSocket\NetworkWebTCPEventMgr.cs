using System;
using System.Collections.Generic;
using UnityEngine;
using WeChatWASM;

public class NetworkWebTCPEventMgr
{
    private static NetworkWebTCPEventMgr m_instance;

    public static NetworkWebTCPEventMgr Instance
    {
        get
        {
            if (m_instance == null)
            {
                m_instance = new NetworkWebTCPEventMgr();
            }

            return m_instance;
        }
    }

    private static long m_globalConnectID = 0;
    
    static Dictionary<long,WebTCPSession> m_connectedSession = new Dictionary<long, WebTCPSession>();

    public static long StartConnect(string host, int port, Action<int, long, int, byte[]> luaCallback)
    {
        // host = "************";
        // port = 8626;
        Debug.LogError("Web_TCP_StartConnect host:" + host + "_" + port);
        WebTCPSession session = new WebTCPSession(m_globalConnectID++, host,port, luaCallback);
        session.ConnectAsync();

        m_connectedSession.Add(session.ConnID, session);
        
        return session.ConnID;
    }
    
    public static long StartConnect2(string host, int port, Action<int, long, int, byte[]> luaCallback)
    {
        // host = "************";
        // port = 9000;
        Debug.LogError("Web_TCP_StartConnect host2:" + host + "_" + port);
        WebTCPSession2 session = new WebTCPSession2(m_globalConnectID++, host,port, luaCallback);
        session.ConnectAsync();

        m_connectedSession.Add(session.ConnID, session);
        
        return session.ConnID;
    }
    
    public static void SendMessage(long iConnID, int iProtoID, byte[] msg)
    {
        if(m_connectedSession.TryGetValue(iConnID, out var session))
        {
            session.SendMessage(iProtoID, msg);
        }
    }
    
    public static bool IsConnected(long iConnID)
    {
        if(m_connectedSession.TryGetValue(iConnID, out var session))
        {
            return session.IsConnected();
        }

        return false;
    }

    public static void Disconnect(long iConnID)
    {
        if(m_connectedSession.TryGetValue(iConnID, out var session))
        {
            session.Disconnect();
            m_connectedSession.Remove(session.ConnID);
        }
    }
    
}


