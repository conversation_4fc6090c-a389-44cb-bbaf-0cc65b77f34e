using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEngine;
using WeChatWASM;

public class WXFileSystem : IFileSystem
{
    private WXFileSystemManager _fileSystemManager;

    public WXFileSystem()
    {
        _fileSystemManager = WX.GetFileSystemManager();
    }
    
    private string ConvertPathToWX(string path)
    {
        if (string.IsNullOrEmpty(path)) return path;
        return path.Replace("\\", "/");
    }
    
    public string GetFileName(string path)
    {
        string wxPath = ConvertPathToWX(path);
        try
        {
            return Path.GetFileNameWithoutExtension(wxPath);
        }
        catch (Exception ex)
        {
            Debug.LogError($"WXFileSystem.GetFileName failed for path: {path}. Error: {ex.Message}");
            return null;
        }
    }

    public string[] GetSpecifyFilesInFolder(string path, string[] extensions = null, bool exclude = false)
    {
        List<string> result = new List<string>();
        TraverseDir(path, extensions, exclude, result);
        return result.ToArray();
    }
    
    public string[] GetAllFilesInFolder(string path)
    {
        return GetSpecifyFilesInFolder(path);
    }

    public void CheckDirAndCreateWhenNeeded(string folderPath)
    {
        if (string.IsNullOrEmpty(folderPath))
        {
            return;
        }

        if (_fileSystemManager.AccessSync(folderPath) != "access:ok")
        {
            _fileSystemManager.MkdirSync(folderPath, true);
        }
    }

    public bool SafeWriteAllBytes(string outFile, byte[] outBytes)
    {
        try
        {
            if (string.IsNullOrEmpty(outFile) || outBytes == null)
            {
                return false;
            }

            // 保证目录存在
            CheckFileAndCreateDirWhenNeeded(outFile);

            // 写入文件
            _fileSystemManager.WriteFileSync(outFile, outBytes);

            return true;
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"SafeWriteAllBytes failed! path = {outFile} with err = {ex.Message}");
            return false;
        }
    }

    public bool SafeWriteAllText(string outFile, string text)
    {
        try
        {
            if (string.IsNullOrEmpty(outFile) || text == null)
            {
                return false;
            }

            // 保证目录存在
            CheckFileAndCreateDirWhenNeeded(outFile);

            // 写入文件
            _fileSystemManager.WriteFileSync(outFile, text);

            return true;
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"SafeWriteAllBytes failed! path = {outFile} with err = {ex.Message}");
            return false;
        }
    }

    public byte[] SafeReadAllBytes(string inFile)
    {
        try
        {
            if (string.IsNullOrEmpty(inFile))
            {
                return null;
            }

            if (_fileSystemManager.AccessSync(inFile) != "access:ok")
            {
                return null;
            }

            // 读取文件（以二进制方式）
            return _fileSystemManager.ReadFileSync(inFile);
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"SafeReadAllBytes failed! path = {inFile} with err = {ex.Message}");
            return null;
        }
    }

    public byte[] SafeReadAllBytesReadOnly(string inFile)
    {
        return SafeReadAllBytes(inFile);
    }

    public string SafeReadAllText(string inFile)
    {
        try
        {
            if (string.IsNullOrEmpty(inFile))
            {
                return null;
            }

            if(_fileSystemManager.AccessSync(inFile)!= "access:ok")
            {
                return null;
            }

            return _fileSystemManager.ReadFileSync(inFile, "utf8");
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"SafeReadAllText failed! path = {inFile} with err = {ex.Message}");
            return null;
        }
    }

    public bool SafeClearDir(string folderPath)
    {
        try
        {
            if (string.IsNullOrEmpty(folderPath))
            {
                return true;
            }

            bool exists = _fileSystemManager.AccessSync(folderPath) == "access:ok";

            if (exists)
            {
                // 删除整个目录（递归）
                _fileSystemManager.RmdirSync(folderPath, true);
                _fileSystemManager.MkdirSync(folderPath, true);
            }
            
            return true;
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"SafeClearDir failed! path = {folderPath} with err = {ex.Message}");
            return false;
        }
    }

    public bool SafeDeleteDir(string folderPath)
    {
        try
        {
            if (string.IsNullOrEmpty(folderPath))
            {
                return true;
            }

            bool exists = _fileSystemManager.AccessSync(folderPath) == "access:ok";

            if (exists)
            {
                // 删除整个目录（递归）
                _fileSystemManager.RmdirSync(folderPath, true);
            }

            return true;
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"SafeClearDir failed! path = {folderPath} with err = {ex.Message}");
            return false;
        }
    }

    public bool SafeDeleteFile(string filePath)
    {
        try
        {
            if (string.IsNullOrEmpty(filePath))
            {
                return true;
            }

            if (_fileSystemManager.AccessSync(filePath) == "access:ok")
            {
                // 删除文件
                _fileSystemManager.UnlinkSync(filePath);    
            }

            return true;
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"SafeDeleteFile failed! path = {filePath} with err: {ex.Message}");
            return false;
        }
    }

#if UNITY_EDITOR
    
    
    public bool SafeCopyFile(string fromFile, string toFile)
    {
        throw new System.NotImplementedException();
    }

    public bool MoveFolder(string sourcePath, string destPath, bool isDelDestPath = false)
    {
        throw new System.NotImplementedException();
    }
#endif   
    
    #region private
    private void TraverseDir(string path, string[] extensions, bool exclude, List<string> result)
    {
        try
        {
            var states = _fileSystemManager.StatSync(path);
            for (int i = 0; i < states.Length; i++)
            {
                var s = states[i];
                string fullPath = path + "/" + s.path;
                if (s.stats.isDirectory())
                {
                    TraverseDir(fullPath, extensions, exclude, result);
                }
                else
                {
                    string ext = System.IO.Path.GetExtension(s.path).ToLower();
                    if (extensions == null)
                    {
                        result.Add(fullPath);
                    }
                    else if (exclude)
                    {
                        if (!extensions.Contains(ext))
                            result.Add(fullPath);
                    }
                    else
                    {
                        if (extensions.Contains(ext))
                            result.Add(fullPath);
                    }
                }
            }
        }
        catch (System.Exception e)
        {
            Debug.Log("TraverseDir Error: " + e.Message);
        }
    }
    
    private void CheckFileAndCreateDirWhenNeeded(string filePath)
    {
        string dir = GetWXDirName(filePath);
        if (!string.IsNullOrEmpty(dir))
        {
            CheckDirAndCreateWhenNeeded(dir);
        }
    }
    
    private string GetWXDirName(string filePath)
    {
        if (string.IsNullOrEmpty(filePath))
            return null;

        // 微信小游戏环境的路径统一用 "/"
        string path = filePath.Replace("\\", "/");

        int lastSlash = path.LastIndexOf('/');
        if (lastSlash < 0)
            return "";

        return path.Substring(0, lastSlash);
    }
    
    #endregion
    
}