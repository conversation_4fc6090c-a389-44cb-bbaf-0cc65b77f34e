local UI_HeroUpStarPanel = Class(BaseView)
local HeroStarAttr = require("UI.HeroStarAttr")

function UI_HeroUpStarPanel:OnInit()
    self.skillCondList = {};
    EventMgr:Add(EventID.HERO_WINDOW_CLOSE, self.Close, self);
    EventMgr:Add(EventID.UPDATE_HERO_INFO, self.OnUpdatePanel, self);
    EventMgr:Add(EventID.BAG_CHANGE, self.OnUpdateCost, self);
    
    self.heroAttr = HeroStarAttr.new()
    self.heroAttr:Create(self.ui.m_goStarAttr)

    self.refreshEffect = false
    SetActive(self.ui.m_goStarAttr, false)
    EventMgr:Add(EventID.UPDATE_HERO_SUM_STAR, self.UpdateHeroStarAttr, self)
end

function UI_HeroUpStarPanel:OnCreate(param)
    local rightBg = GetChild(self.uiGameObject, "rightBg");
    if UIHeight > 1920 then
        SetUIAnchors(rightBg, 0.5, 0, 0.5, 0);
        SetUIPos(rightBg, 0, 1020);
        SetUIAnchors(self.ui.m_goStarAttr, 0.5, 0, 0.5, 0);
        SetUIPos(self.ui.m_goStarAttr, 0, 280);
    else
        SetUIAnchors(rightBg, 0.5, 0.5, 0.5, 0.5);
        SetUIPos(rightBg, 0, 60);
        SetUIAnchors(self.ui.m_goStarAttr, 0.5, 0.5, 0.5, 0.5);
        SetUIPos(self.ui.m_goStarAttr, 0, -680);
    end
    
    self.heroId = param;
    self:OnUpdatePanel();
end

function UI_HeroUpStarPanel:OnRefresh(_type, param)
    if _type == 1 then
        self.heroId = param;
        self:OnUpdatePanel();
    end
end

function UI_HeroUpStarPanel:onDestroy()
    EventMgr:Remove(EventID.HERO_WINDOW_CLOSE, self.Close, self);
    EventMgr:Remove(EventID.UPDATE_HERO_INFO, self.OnUpdatePanel, self);
    EventMgr:Remove(EventID.BAG_CHANGE, self.OnUpdateCost, self);
    EventMgr:Remove(EventID.UPDATE_HERO_SUM_STAR, self.UpdateHeroStarAttr, self)

    if self.heroAttr then
        self.heroAttr:Destroy()
        self.heroAttr = nil
    end
end

function UI_HeroUpStarPanel:onUIEventClick(go, param)
    local name = go.name
    if name == "tipBtn" then
        UI_SHOW(UIDefine.UI_HeroDescTipWindow, LangMgr:GetLang(70000157));
    elseif name == "m_btnUpgradeOne" then
        if self.heroVo.starLv < self.heroVo:GetHeroMaxStar() then
            if self.oneCostId and self.costNum then
                local hasNum = BagManager:GetBagItemCount(self.oneCostId);
                local remainNum = self.costNum - hasNum;
                if remainNum <= 0 then
                    local itemList = { { self.oneCostId, self.costNum } };
                    local lastHeroVo = DeepCopyTable(self.heroVo);
                    HeroManager:OnRequestHeroUpStar(self.heroVo.heroId, false, itemList, function(respData)
                        self:OnShowStarEff(lastHeroVo, respData.star);
                        AudioMgr:Play(121);
                    end);
                else
                    UI_SHOW(UIDefine.UI_SlgGetWay, { { id = self.oneCostId, needNum = self.costNum } });
                end
            end
        end
    elseif name == "m_btnUpgradeTwo" then
        if self.heroVo.starLv < self.heroVo:GetHeroMaxStar() then
            if self.oneCostId and self.twoCostId and self.costNum then
                local hasNum = BagManager:GetBagItemCount(self.oneCostId);
                local hasNum2 = BagManager:GetBagItemCount(self.twoCostId);
                local remainNum = self.costNum - hasNum - hasNum2;
                if remainNum <= 0 then
                    local isReplace = hasNum < self.costNum;
                    local itemList;
                    if isReplace then
                        if hasNum > 0 then
                            itemList = { { self.oneCostId, hasNum }, { self.twoCostId, self.costNum - hasNum } };
                        else
                            itemList = { { self.twoCostId, self.costNum } };
                        end
                    else
                        itemList = { { self.oneCostId, self.costNum } };
                    end
                    local lastHeroVo = DeepCopyTable(self.heroVo);
                    HeroManager:OnRequestHeroUpStar(self.heroVo.heroId, isReplace, itemList, function(respData)
                        self:OnShowStarEff(lastHeroVo, respData.star);
                        AudioMgr:Play(121);
                    end);
                else
                    UI_SHOW(UIDefine.UI_SlgGetWay, { { id = self.twoCostId, needNum = self.costNum - hasNum } });
                end
            end
        end
    elseif name == "m_btnGo" then
        UI_SHOW(UIDefine.UI_HeroHonorWindow, self.heroId);
    elseif name == "skillBg1" or name == "skillBg2" or name == "skillBg3" or name == "skillBg4" then
        local clickIndex = v2n(string.gsub(name, "skillBg", ""));
        if clickIndex then
            self:OnShowSkillTip(go, clickIndex);
        end
    elseif name == "m_goSkillTipMask" then
        SetActive(self.ui.m_goSkillTipMask, false);
    end
    UIMgr:Refresh(UIDefine.UI_HeroDevelopWindow, 3);
end

function UI_HeroUpStarPanel:OnUpdatePanel()
    self.heroVo = HeroManager:GetHeroVoById(self.heroId);
    if not self.heroVo then
        return
    end

    local skillObj;
    local skillConfig;
    local star, order = self.heroVo:GetHeroStarOrder();
    for i = 1, 4 do
        skillObj = GetChild(self.ui.m_goSkill, "skillBg" .. i);
        skillConfig = self.heroVo:GetHeroSkillConfig(i);
        if skillConfig then
            local skillSp = GetChild(skillObj, "skillSp", UEUI.Image);
            local levelTxt = GetChild(skillObj, "levelBg/levelTxt", UEUI.Text);
            local starObj = GetChild(skillObj, "starObj");
            local lockBg = GetChild(skillObj, "lockBg");

            if skillConfig.icon then
                SetUIImage(skillSp, skillConfig.icon, false, function()
                    SetActive(skillSp, true);
                end);
            end

            local level = self.heroVo:GetHeroSkillLv(i);
            levelTxt.text = level < self.heroVo:GetHeroSkillMaxLv(i) and level or LangMgr:GetLang(58006030);

            local unLockState = self.heroVo:GetHeroSkillUnlockState(i);
            local num = self.heroVo:GetHeroSkillStarNum(i);
            for i = 1, 5 do
                local starSp = GetChild(starObj, "star" .. i, UEUI.Image);
                DOKill(starSp);
                if i <= num then
                    if i == star + 1 and order == 4 then
                        DOFade(starSp, 0.1, 1, 0.8, LoopType.Yoyo);
                        SetActive(starSp, true);
                    else
                        starSp.color = Color.New(1, 1, 1, 1);
                        local isShow = unLockState and star >= i;
                        SetActive(starSp, isShow);
                    end
                else
                    SetActive(starSp, false);
                end
            end
            SetActive(lockBg, not unLockState);
        end
        SetActive(skillObj, skillConfig ~= nil);
    end

    for i = 1, 5 do
        local img = GetChild(self.ui.m_goStarList, "star" .. i, UEUI.Image);
        if i <= star then
            SetUIImage(img, "Sprite/ui_slg_jueseyangcheng/yangcheng_star5.png", false);
        elseif i == star + 1 then
            SetUIImage(img, "Sprite/ui_slg_jueseyangcheng/yangcheng_star" .. order .. ".png", false);
        else
            SetUIImage(img, "Sprite/ui_slg_jueseyangcheng/yangcheng_star0.png", false);
        end
    end
    self.ui.m_txtStar.text = LangMgr:GetLangFormat(58006031, star, order);

    local isMaxStar = self.heroVo:isHeroMaxStar();
    local curStarConfig = self.heroVo:GetHeroStarConfig();
    local nextStarConfig;
    if not isMaxStar then
        nextStarConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_hero_star, self.heroVo:GetHeroStarId() + 1);
    end
    local field;
    for i = 1, 3 do
        local attrObj = GetChild(self.ui.m_goAttrList, "attrObj" .. i);
        local curAttrTxt = GetChild(attrObj, "curAttrTxt", UEUI.Text);
        local nextAttrTxt = GetChild(attrObj, "nextAttrTxt", UEUI.Text);
        local arrowSp = GetChild(attrObj, "arrowSp");

        if i == 1 then
            field = "life"
        elseif i == 2 then
            field = "atk"
        elseif i == 3 then
            field = "defense"
        end
        curAttrTxt.text = NumToGameString(curStarConfig[field]);

        if isMaxStar then
            nextAttrTxt.text = "";
            SetUIPos(curAttrTxt, 254.6, 4.6);
        else
            if nextStarConfig then
                nextAttrTxt.text = NumToGameString(nextStarConfig[field]);
            end
            SetUIPos(curAttrTxt, 63, 4.6);
        end
        SetActive(arrowSp, not isMaxStar);
    end
    SetActive(self.ui.m_goNormal, not isMaxStar);
    SetActive(self.ui.m_goMax, isMaxStar);

    self:OnUpdateCost();
end

function UI_HeroUpStarPanel:OnUpdateCost()
    if not self.heroVo:isHeroMaxStar() then
        local curStarConfig = self.heroVo:GetHeroStarConfig();
        local costDic = string.split(curStarConfig.piece, "|");
        self.oneCostId = v2n(costDic[1]);
        self.costNum = v2n(costDic[2]);
        local hasNum = BagManager:GetBagItemCount(self.oneCostId);
        local showStr;
        local isEnough = hasNum >= self.costNum;
        if isEnough then
            showStr = GetStrRichColor(NumToGameString(hasNum), "95ff46") .. "/" .. NumToGameString(self.costNum);
        else
            showStr = GetStrRichColor(NumToGameString(hasNum), "ff3535") .. "/" .. NumToGameString(self.costNum);
        end
        self.ui.m_txtCostOne.text = showStr;
        SetUIImage(self.ui.m_imgCostOne, ItemConfig:GetIcon(self.oneCostId), false);
        SetActive(self.ui.m_goUpgradeRedOne, isEnough);

        if not isEnough then
            local isShow = hasNum > 0;
            if isShow then
                self.ui.m_txtCostAdd.text = GetStrRichColor(NumToGameString(hasNum), "95ff46") .. "/" .. NumToGameString(hasNum);
                SetUIImage(self.ui.m_imgCostAdd, ItemConfig:GetIcon(self.oneCostId), false);
            end
            SetActive(self.ui.m_txtCostAdd, isShow);

            local offsetNum = self.costNum - hasNum;
            self.twoCostId = curStarConfig.general_piece;
            hasNum = BagManager:GetBagItemCount(self.twoCostId);
            local isMeet = hasNum >= offsetNum;
            if isMeet then
                self.ui.m_txtCostTwo.text = GetStrRichColor(NumToGameString(hasNum), "95ff46") .. "/" .. NumToGameString(offsetNum);
            else
                self.ui.m_txtCostTwo.text = GetStrRichColor(NumToGameString(hasNum), "ff3535") .. "/" .. NumToGameString(offsetNum);
            end
            SetUIImage(self.ui.m_imgCostTwo, ItemConfig:GetIcon(self.twoCostId), false);
            --SetActive(self.ui.m_goUpgradeRedTwo, isMeet);

            SetUIPos(self.ui.m_goCostOne, 254, -175.4);
            SetUIPos(self.ui.m_btnUpgradeOne, 249, -293.5);
        else
            SetUIPos(self.ui.m_goCostOne, 5, -175.4);
            SetUIPos(self.ui.m_btnUpgradeOne, 0, -293.5);
        end
        SetActive(self.ui.m_goCostTwo, not isEnough);
        SetActive(self.ui.m_btnUpgradeTwo, not isEnough);
        UIRefreshLayout(self.ui.m_goCostTwo);
    end
end

function UI_HeroUpStarPanel:OnShowStarEff(lastHeroVo, starLv)
    local star, order = HeroManager:GetStarOrderByLv(starLv);
    if order > 0 then
        local posX = 46.5 + star * 93;
        local posY = -44;
        local rotate = (order - 2) * 70;
        SetUIPos(self.ui.m_goStarEffect, posX, posY);
        self.ui.m_goStarEffect.transform.localRotation = Quaternion.Euler(0, 0, rotate);
        SetActive(self.ui.m_goStarEffect, false);
        SetActive(self.ui.m_goStarEffect, true);
    else
        UI_SHOW(UIDefine.UI_HeroUpStarSuccess, lastHeroVo, starLv);
        if self.heroAttr then
            self.heroAttr:SetActive(false)
        end
    end
    SetActive(self.ui.m_goAttrEffect, false);
    SetActive(self.ui.m_goAttrEffect, true);
    SetActive(self.ui.m_goUpEffect, false);
    SetActive(self.ui.m_goUpEffect, true);
end

function UI_HeroUpStarPanel:OnShowSkillTip(go, clickIndex)
    local skillConfig = self.heroVo:GetHeroSkillConfig(clickIndex);
    if skillConfig.icon then
        SetUIImage(self.ui.m_imgSkillSp, skillConfig.icon, false);
    end

    local skillLv = self.heroVo:GetHeroSkillLv(clickIndex);
    self.ui.m_txtSkillDes.text = HeroManager:GetHeroSkillDesc(skillConfig, skillLv);

    local isUnlock = self.heroVo:GetHeroSkillUnlockState(clickIndex);
    local height = isUnlock and 490 or 360;
    if isUnlock then
        local num = self.heroVo:GetHeroSkillCurStar(clickIndex);
        for i = 1, 5 do
            local starImg = GetChild(self.ui.m_goSkillStar, "star" .. i, UEUI.Image);
            if i <= num then
                SetUIImage(starImg, "Sprite/ui_slg_jueseyangcheng/yangcheng_1_s_star3.png", false);
            else
                SetUIImage(starImg, "Sprite/ui_slg_jueseyangcheng/yangcheng_1_s_star4.png", false);
            end
            SetActive(starImg, i <= num);
        end

        local list = self.heroVo:GetHeroSkillExtraList(clickIndex);
        local count = #list;
        local num = #self.skillCondList;
        local len = count > num and count or num;
        local item;
        for i = 1, len do
            item = self.skillCondList[i];
            if not item then
                item = CreateGameObjectWithParent(self.ui.m_goCond, self.ui.m_scrollview.content);
                table.insert(self.skillCondList, item);
            end

            if i <= count then
                local starImg = GetChild(item, "starImg", UEUI.Image);
                local lockTxt = GetChild(item, "lockTxt", UEUI.Text);
                local lockOutline = GetChild(item, "lockTxt", UEUI.Outline);

                local data = list[i];
                local descStr = "";
                if data.langId then
                    local strList = string.split(data.showList, "|");
                    descStr = LangMgr:GetLangFormat(data.langId, strList[1], strList[2], strList[3]);
                end

                if self.heroVo.starLv >= data.starLv then
                    lockTxt.text = descStr;
                    lockTxt.color = GetColorByHex("95ff46");
                    lockOutline.enabled = true;
                    SetUIImage(starImg, "Sprite/ui_slg_jueseyangcheng/yangcheng_1_s_star3.png", false);
                else
                    lockTxt.text = descStr .. LangMgr:GetLangFormat(70000149, math.floor(data.starLv / 5));
                    lockTxt.color = GetColorByHex("D78E2E");
                    lockOutline.enabled = false;
                    SetUIImage(starImg, "Sprite/ui_slg_jueseyangcheng/yangcheng_1_s_star4.png", false);
                end
            end
            SetActive(item, i <= count);
        end
        SetActive(self.ui.m_goSkillLimit, count > 0);

        if count > 0 then
            height = height + self.ui.m_txtSkillDes.preferredHeight - 31;
        else
            height = 360;
        end

        local levelStr = string.format("Lv.%s/%s", skillLv, self.heroVo:GetHeroSkillCurMaxLv(clickIndex));
        self.ui.m_txtSkillName.text = LangMgr:GetLang(skillConfig.name) .. levelStr;
    else
        self.ui.m_txtSkillName.text = LangMgr:GetLangFormat(70000150, LangMgr:GetLang(skillConfig.name));
        SetActive(self.ui.m_goSkillLimit, false);
    end
    SetActive(self.ui.m_goSkillStar, isUnlock);
    SetActive(self.ui.m_goSkillLock, not isUnlock);

    local tipBg = GetChild(self.ui.m_goSkillTipTarget, "tipBg", UEUI.Image);
    local pos = self.uiGameObject.transform:InverseTransformPoint(go.transform.position);
    if clickIndex % 2 > 0 then
        SetUIPos(tipBg, 322.5, 221.5);
        self.ui.m_imgArrow.transform.localScale = Vector3(1, 1, 1);
        self.ui.m_goSkillTipTarget.transform.localPosition = Vector3(pos.x + 100, pos.y, 0);
    else
        SetUIPos(tipBg, -322.5, 221.5);
        self.ui.m_imgArrow.transform.localScale = Vector3(-1, 1, 1);
        self.ui.m_goSkillTipTarget.transform.localPosition = Vector3(pos.x - 100, pos.y, 0);
    end
    SetUISize(tipBg, 650, height);

    SetActive(self.ui.m_goSkillTipMask, true);
    SetUIPos(self.ui.m_scrollview.content, 0, 0);
    UIRefreshLayout(self.ui.m_scrollview.content);
    self:CreateScheduleFun(function()
        UIRefreshLayout(self.ui.m_scrollview.content);
    end, 0.02, 1);
end

--更新英雄星级属性加成
function UI_HeroUpStarPanel:UpdateHeroStarAttr()
    if not self.heroAttr then
        self.heroAttr = HeroStarAttr.new()
        self.heroAttr:Create(self.ui.m_goStarAttr, false, function()
            self.heroAttr:ShowAnim()
        end)
    else
        SetActive(self.ui.m_goStarAttr, true)
        self.heroAttr:ShowAnim()
    end
    
    if not self.refreshEffect then
        self:SortOrderAllCom(true)
        self.refreshEffect = true
    end
end

return UI_HeroUpStarPanel