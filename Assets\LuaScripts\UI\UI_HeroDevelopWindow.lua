local UI_HeroDevelopWindow = Class(BaseView)

function UI_HeroDevelopWindow:OnInit()
    EventMgr:Add(EventID.UPDATE_HERO_INFO, self.UpdateHeroShow, self);
    EventMgr:Add(EventID.UPDATE_HERO_ANIMAL, self.OnUpdateAnimalRed, self);
    EventMgr:Add(EventID.UPDATE_HERO_TOTAL_FIGHT, self.OnUpdateFight, self);
    self.curPanelIndex = 0;
end

function UI_HeroDevelopWindow:OnCreate(heroId, heroVoList, heroIndex, panelIndex, showType)
    self.heroId = heroId;
    self.heroVoList = heroVoList;
    self.heroIndex = heroIndex;
    self.showType = showType or 0; -- 0:默认 1:角色礼包跳转

    if not self.heroVoList then
        if HeroManager:IsHeroActive(self.heroId) then
            self.heroVoList = HeroManager:GetHeroVoList(HERO_KIND.ALL, true, true);
        else
            local heroVo = HeroManager:GetHeroVoById(self.heroId);
            self.heroVoList = { heroVo };
        end
    end

    if not self.heroIndex then
        local index = 1;
        for i = 1, #self.heroVoList do
            if self.heroVoList[i].heroId == self.heroId then
                index = i;
                break ;
            end
        end
        self.heroIndex = index;
    end

    SetActive(self.ui.m_btnLeft, self.heroIndex > 1);
    SetActive(self.ui.m_btnRight, self.heroIndex < #self.heroVoList);
    SetActive(self.ui.m_imgHero, false);

    if UIWidth > 1080 then
        SetUIPos(self.ui.m_goTogList, 0, 113.4);
    else
        SetUIPos(self.ui.m_goTogList, 63, 113.4);
    end

    if UIHeight > 1920 then
        SetUIAnchors(self.ui.m_goFight, 0.5, 1, 0.5, 1);
        SetUIPos(self.ui.m_goFight, 25.1, -148.5);
    else
        SetUIAnchors(self.ui.m_goFight, 0.5, 0.5, 0.5, 0.5);
        SetUIPos(self.ui.m_goFight, 25.1, 811.5);
    end

    local canvas = GetComponent(self.ui.m_goTipTarget, UE.Canvas);
    canvas.sortingOrder = self:GetViewSortingOrder() + 12;

    self:UpdateHeroShow();

    panelIndex = panelIndex or 1;
    if panelIndex == 1 then
        self:OnSwitchPanel(panelIndex);
    else
        self:OnSelectTog(panelIndex);
    end
    self:OnUpdateTog();
end

function UI_HeroDevelopWindow:OnRefresh(_type, param)
    if _type == 1 then
        -- 切换英雄页签
        if self:OnCheckPanel(param) then
            self:OnSelectTog(param);
        end
    elseif _type == 2 then
        -- 刷新英雄显示
        self:UpdateHeroShow();
    elseif _type == 3 then
        self:OnCloseTip();
    end
end

function UI_HeroDevelopWindow:onDestroy()
    EventMgr:Remove(EventID.UPDATE_HERO_INFO, self.UpdateHeroShow, self);
    EventMgr:Remove(EventID.UPDATE_HERO_ANIMAL, self.OnUpdateAnimalRed, self);
    EventMgr:Remove(EventID.UPDATE_HERO_TOTAL_FIGHT, self.OnUpdateFight, self);
    EventMgr:Dispatch(EventID.HERO_WINDOW_CLOSE);
    UI_UPDATE(UIDefine.UI_HeroWindow, 1);
end

function UI_HeroDevelopWindow:onUIEventClick(go, param)
    local name = go.name
    if name == "closeBtn" then
        self:Close();
    elseif name == "m_btnLeft" then
        self:OnSwtichHero(self.heroIndex - 1);
    elseif name == "m_btnRight" then
        self:OnSwtichHero(self.heroIndex + 1);
    elseif name == "m_imgKind" then
        local kind = self.heroVo:GetHeroKind();
        local showStr = LangMgr:GetLang(58006059);
        if kind == HERO_KIND.TANK then
            showStr = showStr .. LangMgr:GetLang(58006060);
        elseif kind == HERO_KIND.PLAEN then
            showStr = showStr .. LangMgr:GetLang(58006061);
        elseif kind == HERO_KIND.MISSILE then
            showStr = showStr .. LangMgr:GetLang(58006062);
        end
        self:OnShowTip(self.ui.m_imgKind, showStr);
    elseif name == "m_imgCareer" then
        local career = self.heroVo:GetHeroCareer();
        local showStr;
        if career == HERO_CAREER.SUP then
            showStr = LangMgr:GetLang(70000050);
        elseif career == HERO_CAREER.DEF then
            showStr = LangMgr:GetLang(70000049);
        elseif career == HERO_CAREER.ADC then
            showStr = LangMgr:GetLang(70000051);
        end
        self:OnShowTip(self.ui.m_imgCareer, showStr);
    elseif name == "m_btnChangeSkin" then
        local openView = NetHandbook:IsCanOpenDress(self.heroId);
        if openView then
            local mapItem = MapController:GetItemById(self.heroId);
            if mapItem then
                UI_SHOW(UIDefine.UI_MapHeroDress, mapItem[1]);
            else
                UI_SHOW(UIDefine.UI_MapHeroDress, nil, nil, self.heroId);
            end
        else
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(6011));
        end
    elseif name == "m_goTipMask" then
        SetActive(self.ui.m_goTipMask, false);
    elseif string.startswith(name, "m_tog") then
        if self.ui[name].isOn then
            local panelIndex = string.gsub(name, "m_tog", "");
            self:OnSwitchPanel(v2n(panelIndex));
        end
    end
end

function UI_HeroDevelopWindow:GetPanelName(panelIndex)
    if panelIndex == 1 then
        return UIDefine.UI_HeroAttrPanel;
    elseif panelIndex == 2 then
        return UIDefine.UI_HeroSkillPanel;
    elseif panelIndex == 3 then
        return UIDefine.UI_HeroUpStarPanel;
    elseif panelIndex == 4 then
        return UIDefine.UI_HeroAnimalPanel;
    end
    return nil;
end

function UI_HeroDevelopWindow:OnCheckPanel(panelIndex)
    if panelIndex == 3 or panelIndex == 4 then
        return self.heroVo:GetHeroActive();
    end
    return true;
end

function UI_HeroDevelopWindow:OnSwitchPanel(panelIndex)
    if self.curPanelIndex == panelIndex then
        return
    end

    local panelName = self:GetPanelName(self.curPanelIndex);
    if panelName then
        local actView = UIMgr:GetUIItem(panelName);
        if actView and actView.isShow then
            UI_CLOSE(panelName);
        end
    end
    self.curPanelIndex = panelIndex;

    local panelName = self:GetPanelName(panelIndex);
    if panelName then
        local actView = UIMgr:GetUIItem(panelName);
        if not actView or not actView.isShow then
            UI_SHOW(panelName, self.heroId, self.showType);
        end
    end

    SetActive(self.ui.m_goShow1, panelIndex == 1);
    SetActive(self.ui.m_goShow2, panelIndex ~= 1);
    SetActive(self.ui.m_btnChangeSkin, self.curPanelIndex ~= 1 and self.heroVo:GetHeroActive());
end

function UI_HeroDevelopWindow:OnSelectTog(index)
    local nameStr = "m_tog" .. index;
    local tog = self.ui[nameStr];
    if tog then
        tog.isOn = true;
    end
end

function UI_HeroDevelopWindow:OnUpdateTog()
    local isActive = self.heroVo:GetHeroActive();
    SetActive(self.ui.m_tog3, isActive);
    SetActive(self.ui.m_tog4, isActive);
end

function UI_HeroDevelopWindow:OnUpdateTogRed()
    local isActive = self.heroVo:GetHeroActive();
    local state = false;
    for i = 1, 3 do
        local redDot = GetChild(self.ui["m_tog" .. i], "redDot");
        if redDot ~= nil then
            if isActive then
                if i == 1 then
                    state = self.heroVo:GetHeroUpgradeRed() or EquipmentManager:IsNeedHigherFightingEquipment(self.heroId);
                elseif i == 2 then
                    state = self.heroVo:GetHeroSkillUpRed();
                elseif i == 3 then
                    state = self.heroVo:GetHeroUpStarRed();
                else
                    state = false;
                end
            end
            SetActive(redDot, isActive and state);
        end
    end
    self:OnUpdateAnimalRed();
end

function UI_HeroDevelopWindow:OnSwtichHero(index)
    if self.heroIndex == index or index < 1 or index > #self.heroVoList then
        return
    end
    self.heroIndex = index;

    self.heroId = self.heroVoList[index].heroId;
    self:UpdateHeroShow();

    if not self:OnCheckPanel(self.curPanelIndex) then
        self:OnSelectTog(1);
    else
        -- 刷新当前界面英雄
        UIMgr:Refresh(self:GetPanelName(self.curPanelIndex), 1, self.heroId);
    end
    self:OnUpdateTog();

    SetActive(self.ui.m_btnLeft, index > 1);
    SetActive(self.ui.m_btnRight, index < #self.heroVoList);
    SetActive(self.ui.m_btnChangeSkin, self.curPanelIndex ~= 1 and self.heroVo:GetHeroActive());
end

function UI_HeroDevelopWindow:UpdateHeroShow()
    if not self.heroId then
        return
    end
    self.heroVo = HeroManager:GetHeroVoById(self.heroId);

    local isActive = self.heroVo:GetHeroActive();
    local fightNum = isActive and self.heroVo.power or HeroManager:CalculateFight(self.heroId);
    self.ui.m_txtFight.text = HeroManager:GetFightShowStr(fightNum);
    self.ui.m_txtName.text = self.heroVo:GetHeroName();

    SetUIImage(self.ui.m_imgHero, self.heroVo:GetHeroIcon(), false, function()
        if self.ui then
            SetActive(self.ui.m_imgHero, true);
        end
    end);
    SetUIImage(self.ui.m_imgQuality, HeroManager:GetHeroQualityIcon(self.heroVo:GetHeroQuality()), true);
    SetUIImage(self.ui.m_imgCareer, HeroManager:GetHeroCareerIcon(self.heroVo:GetHeroCareer()), false);

    local kind = self.heroVo:GetHeroKind();
    SetUIImage(self.ui.m_imgKind, HeroManager:GetHeroKindIcon(kind), true, function()
        if self.ui then
            if kind == HERO_KIND.TANK then
                SetUISize(self.ui.m_imgKind, 63 * 0.72, 69 * 0.72);
            elseif kind == HERO_KIND.PLAEN then
                SetUISize(self.ui.m_imgKind, 71 * 0.72, 66 * 0.72);
            elseif kind == HERO_KIND.MISSILE then
                SetUISize(self.ui.m_imgKind, 73 * 0.72, 60 * 0.72);
            end
        end
    end);
    self:OnUpdateTogRed();
end

function UI_HeroDevelopWindow:OnUpdateAnimalRed()
    local redDot = GetChild(self.ui.m_tog4, "redDot");
    SetActive(redDot, self.heroVo:GetHeroActive() and self.heroVo:GetHeroAnimalRed());
end

function UI_HeroDevelopWindow:OnUpdateFight()
    SetActive(self.ui.m_goFightEff, false);
    if self.curPanelIndex ~= 1 then
        SetActive(self.ui.m_goFightEff, true);
    end
end

function UI_HeroDevelopWindow:OnShowTip(go, str)
    local pos = self.uiGameObject.transform:InverseTransformPoint(go.transform.position);
    self.ui.m_goTipTarget.transform.localPosition = pos;
    self.ui.m_txtTipShow.text = str;
    SetActive(self.ui.m_goTipMask, true);

    local tipBg = GetChild(self.ui.m_goTipTarget, "tipBg");
    UIRefreshLayout(tipBg);
end

function UI_HeroDevelopWindow:OnCloseTip()
    if self.ui.m_goTipMask.activeSelf then
        SetActive(self.ui.m_goTipMask, false);
    end
end

return UI_HeroDevelopWindow