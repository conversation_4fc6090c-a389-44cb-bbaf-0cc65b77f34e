local UI_TradeWagonsView = Class(BaseView)
local GoTradeTrain = require("UI.GoTradeTrain")

local CurSelectWagon
local CurSelectRecordID

local OriginBgHeight = 1920
local OriginTrainPosY = 494

local PlayerBorder = {
    [1] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win1_mingchengbg.png",
    [2] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_win1_mingchengbg2.png"
}

function UI_TradeWagonsView:OnInit()

end

function UI_TradeWagonsView:OnCreate(isPush, param)
    self:OnClickTab(TRADE_WAGONS_TAB.Other)
    self.cloudAnim = GetComponent(self.ui.m_goCloud.gameObject, UE.Animation)
    self.tradeCarSelectEffect = {}
    SetActive(self.ui.m_goWagonItem, false)

    local leagueId = LeagueManager:GetMyLeagueId()
    self.isJoinTeam = leagueId and leagueId > 0

    if not self.goTradeTrain then
        self:InitTrain()
        self:RefreshTrain()
    end
    self.fitBgRect = GetComponent(self.ui.m_imgFitBg, UE.RectTransform)
    self.fitBgHeight = self.fitBgRect.rect.height
    self:RefreshPanel()
    self:RefreshRedPoint()

    TradeWagonsManager:RequestAllInfo(function (data)
        self:RefreshWagon(data)
        self:RefreshPanel()

        if IsTableEmpty(TradeWagonsManager.wagonsInfoMe) then
            self:RefreshWagonInfoMe()
        end

        self:RefreshRedPoint()
    end)

    TradeWagonsManager:RequestTrainLoadData(function (data)
        if param then
            TradeWagonsManager:RequestTrainInfo(param, function (trainData)
                local leagueDetails = LeagueManager:GetMyLeagueDetails()
                -- 自己联盟的火车
                if trainData.train_info.league.id == v2n(leagueDetails.id) then
                    self:RefreshWagonInfoMe(function ()
                        if IsTableNotEmpty(TradeWagonsManager.wagonsInfoMe) then
                            UI_SHOW(UIDefine.UI_TradeWagonsWindow)
                            UI_SHOW(UIDefine.UI_TradeWagonsTrainDetail, TRADE_WAGONS_DETAIL.Me, trainData.train_info)
                        end
                    end)
                -- 别人联盟的火车
                else
                    TradeWagonsManager:SetCanLootTrain(trainData.train_info)
                    self:RefreshTrain()
                    UI_SHOW(UIDefine.UI_TradeWagonsTrainDetail, TRADE_WAGONS_DETAIL.Other, trainData.train_info)
                end
            end)
        else
            self:RefreshTrain()
        end
    end)

    -- 引导一次
    local isGuide = NetGlobalData:GetActivityGuideCache("TradeWagons")
    if not isGuide then
        NetGlobalData:SetActivityGuideCache("TradeWagons")
        self.isGuide = true
        self:Guide()
    end

    EventMgr:Add(EventID.BATTLE_TRADEWAGON_RESULT, self.BattleTradeWagonResult, self)

    self:SetIsUpdateTick(true)

    self.canStrongGiftTimer = true
    self.giftList = {}
    self.canStrongGiftTimer = false
    self.finalTime = 0
    self:OnRefreshStrongGift()
    self.timer = TimeMgr:CreateTimer(self, function()
        self:OnStrongGiftTimerLogic()
    end, 1)

    local BattleEnterItem = require("UI.BattleEnterItem");
    local obj = GetChild(self.uiGameObject, "bg/BattleEnter");
    self.battleEnterItem = BattleEnterItem.new(obj, 900, function()
        UI_CLOSE(UIDefine.UI_ActivityRankCenter);
    end);
    self.battleEnterItem:OnInit();
end

function UI_TradeWagonsView:OnRefresh(type)
    if type == 1 then
        self:RefreshPanel()
        self:RefreshRedPoint()
    elseif type == 2 then
        self:OnRefreshStrongGift()
    -- 刷新火车
    elseif type == 3 then
        self:RefreshTrain()
        self:RefreshPanel()
    end
end

function UI_TradeWagonsView:onDestroy()
    self:SetIsUpdateTick(false)
    EventMgr:Remove(EventID.BATTLE_TRADEWAGON_RESULT, self.BattleTradeWagonResult, self)
    CurSelectWagon = nil
    CurSelectRecordID = nil
    self.goTradeTrain = nil
    if self.timer then
        TimeMgr:DestroyTimer(self,self.timer)
    end

    if self.battleEnterItem then
        self.battleEnterItem:OnDestroy();
        self.battleEnterItem = nil;
    end

    UI_CLOSE(UIDefine.UI_TradeWagonsWindow)
end

function UI_TradeWagonsView:onUIEventClick(go)
    local name = go.name
    -- 帮助按钮
    if name == "m_btnHelp" then
        UI_SHOW(UIDefine.UI_TradeWagonsHelpAll)
    -- 掠夺记录按钮
    elseif name == "m_btnPlunderRecord" then
        UI_SHOW(UIDefine.UI_TradeWagonsRecord, TRADE_WAGONS_RECORD.Plunder)
    -- 重置货车按钮
    elseif name == "m_btnResetWagon" then
        self:ResetWagon()
    -- 开始运输按钮
    elseif name == "m_btnStartShipping" then
        self:OnClickTab(TRADE_WAGONS_TAB.My)
    elseif name == "m_btnStrongGift" then
        UI_SHOW(UIDefine.UI_StrengthenGift)
    elseif name == "m_btnTabOtherTruck" then
        self:OnClickTab(TRADE_WAGONS_TAB.Other)
    elseif name == "m_btnTabMyTruck" then
        self:OnClickTab(TRADE_WAGONS_TAB.My)
    end
end

function UI_TradeWagonsView:TickUI(deltaTime)
    if self.fitBgHeight ~= self.fitBgRect.rect.height then
        self.fitBgHeight = self.fitBgRect.rect.height
        local factor = self.fitBgHeight / OriginBgHeight
        local newPosY = OriginTrainPosY * factor
        SetUIPos(self.ui.m_goTradeTrain, 5.5, newPosY)
        SetUIPos(self.ui.m_goTradeTrain2, 5.5, newPosY)
    end
end

function UI_TradeWagonsView:OnClickTab(type, isGuide)
    local clickType = type or TRADE_WAGONS_TAB.Other
    self.clickType = clickType

    SetActive(self.ui.m_imgTabOtherTruckLight, clickType == TRADE_WAGONS_TAB.Other)
    SetActive(self.ui.m_imgTabMyTruckLight, clickType == TRADE_WAGONS_TAB.My)
    SetActive(self.ui.m_goTruckOtherInfo, clickType == TRADE_WAGONS_TAB.Other)
    SetActive(self.ui.m_goMiddleOther, clickType == TRADE_WAGONS_TAB.Other)

    if clickType == TRADE_WAGONS_TAB.Other then
        UI_CLOSE(UIDefine.UI_TradeWagonsWindow)
    elseif clickType == TRADE_WAGONS_TAB.My then
        self:RefreshWagonInfoMe(function ()
            if IsTableNotEmpty(TradeWagonsManager.wagonsInfoMe) then
                UI_SHOW(UIDefine.UI_TradeWagonsWindow, isGuide)
            end
        end)
    end
end

--- 货车战斗结果事件
--- @param isWin boolean 是否胜利
function UI_TradeWagonsView:BattleTradeWagonResult(isWin)
    self:RefreshPanel()
    self:RefreshRedPoint()
    if CurSelectRecordID then

        local end_timestamp
        for _, value in pairs(TradeWagonsManager.loadInfo.other_trade_info) do
            if value.record_id == CurSelectRecordID then
                end_timestamp = value.end_timestamp
            end
        end

        if not end_timestamp then return end

        local remain = end_timestamp - TimeMgr:GetServerTime()
        if remain > 0 then
            TradeWagonsManager:RequestWagonInfoOther(CurSelectRecordID, function (dataInfo)
                self:RefreshWagon(TradeWagonsManager.loadInfo)
            end)
        end
    end
end

--- 刷新界面
function UI_TradeWagonsView:RefreshPanel()
    -- 今日可掠夺次数
    local plunderCount = TradeWagonsManager:GetPlunderCount()
    local plunderCountMax = v2n(TradeWagonsManager:GetTradeSettingConfig(6))
    local title = LangMgr:GetLang(70000444)
    self.ui.m_txtCountToday.text = string.format("%s%s/%s", title, plunderCountMax - plunderCount, plunderCountMax)

    -- 文本宽度自适应，红点跟在文本右边
    local txtTopicRT = GetComponent(self.ui.m_txtCountToday, UE.RectTransform)
    UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(txtTopicRT)
    local width = txtTopicRT.rect.width
    local maxWidth = 650
    if width >= maxWidth then
        local contentSizeFitter = GetComponent(self.ui.m_txtCountToday, UEUI.ContentSizeFitter)
        contentSizeFitter.enabled = false
        SetUISize(self.ui.m_txtCountToday, maxWidth, txtTopicRT.rect.height)
    else
        local contentSizeFitter = GetComponent(self.ui.m_txtCountToday, UEUI.ContentSizeFitter)
        contentSizeFitter.enabled = true
    end

    -- 今日运输次数
    local departCount = TradeWagonsManager.loadInfo.today_trade_times
    local departCountMax = v2n(TradeWagonsManager:GetTradeSettingConfig(11))
    self.ui.m_txtCountStart.text = string.format("%s/%s", departCountMax - departCount, departCountMax)
end

--region ----------------------------------------- 货车 -----------------------------------------

--- 刷新货车
function UI_TradeWagonsView:RefreshWagon(data)
    local parent = self.ui.m_transWagon
    local otherInfo = data.other_trade_info

    -- 创建位置映射表：前6个位置按顺序，后6个位置随机
    local positionMap = {}

    -- 前6个位置按顺序遍历
    for i = 1, 6 do
        positionMap[i] = i
    end

    -- 后6个位置随机排列
    local backPositions = {7, 8, 9, 10, 11, 12}
    -- 使用Fisher-Yates洗牌算法随机打乱后6个位置
    for i = #backPositions, 2, -1 do
        local j = math.random(i)
        backPositions[i], backPositions[j] = backPositions[j], backPositions[i]
    end

    -- 将随机后的位置添加到映射表
    for i = 7, 12 do
        positionMap[i] = backPositions[i - 6]
    end

    for i = 1, 12, 1 do
        local pos = GetChild(parent, "pos" .. positionMap[i])
        if pos then
            local otherData = otherInfo[i]

            -- 货车节点
            local wagon
            if pos.transform.childCount == 0 then
                wagon = CreateGameObjectWithParent(self.ui.m_goWagonItem, pos.transform)
                SetUIPos(wagon, 0, 0)
            else
                wagon = pos.transform:GetChild(0)
            end

            if otherData then
                -- 货车图标
                local icon = GetChild(wagon, "icon", UEUI.Image)
                local quality = otherData.quality
                SetUIImage(icon, TradeWagonsManager:GetWagonIcon(quality), true)

                -- 货车 spine
                local spine = GetChild(wagon, "icon/spine", CS.Spine.Unity.SkeletonGraphic)
                spine.material = ResMgr:LoadAssetSync(TradeWagonsManager:GetWagonMaterial(quality), AssetDefine.LoadType.Instant)
                spine.skeletonDataAsset = ResMgr:LoadAssetSync(TradeWagonsManager:GetWagonSpine(quality), AssetDefine.LoadType.Instant)
                spine:Initialize(true)

                -- 选中特效
                local tradeCarSelectEffect = GetChild(wagon, "icon/TradeCarSelectEffect")
                self.tradeCarSelectEffect[positionMap[i]] = tradeCarSelectEffect

                -- 设置选中特效位置
                local selectEffectPos = TradeWagonsManager:GetWagonsSelectEffectPos(quality)
                SetUIPos(tradeCarSelectEffect, selectEffectPos[1], selectEffectPos[2])

                -- 货车图标按钮
                local btnIcon = GetChild(wagon, "icon", UEUI.Button)
                btnIcon.onClick:RemoveAllListeners()
                btnIcon.onClick:AddListener(function ()
                    CurSelectWagon = positionMap[i]
                    CurSelectRecordID = otherData.record_id
                    if CurSelectWagon then
                        self:HideSelectEffect()
                        SetActive(tradeCarSelectEffect, CurSelectWagon == positionMap[i])
                    else
                        SetActive(tradeCarSelectEffect, false)
                    end

                    if otherData.end_timestamp then
                        local remain = otherData.end_timestamp - TimeMgr:GetServerTime()
                        if remain > 0 then
                            TradeWagonsManager:RequestWagonInfoOther(otherData.record_id, function (dataInfo)
                                UI_SHOW(UIDefine.UI_TradeWagonsDetail, TRADE_WAGONS_DETAIL.Other, dataInfo.truck_info)
                            end)
                        else
                            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000687))
                        end
                    end
                end)

                if CurSelectWagon then
                    self:HideSelectEffect()
                    SetActive(tradeCarSelectEffect, CurSelectWagon == positionMap[i])
                else
                    SetActive(tradeCarSelectEffect, false)
                end

                local border = GetChild(wagon, "border")
                local wantIcon = GetChild(wagon, "border/icon")
                local playerData = otherData.player
                local txtName = GetChild(wagon, "border/name", UEUI.Text)
                txtName.text = playerData.name

                SetActive(border, quality == SLG_QUALITY.UR)

                local wantStatus = otherData.want_status
                if wantStatus == 0 then
                    SetUIImage(border, PlayerBorder[1], false)
                    SetActive(wantIcon, false)
                else
                    SetUIImage(border, PlayerBorder[2], false)
                    SetActive(wantIcon, true)
                    SetActive(border, true)
                end

                local head = GetChild(wagon, "border/head")
                local headNode
                if head.transform.childCount == 0 then
                    headNode = CreateCommonHead(head, 0.39)
                else
                    headNode = head.transform:GetChild(0)
                end

                SetHeadAndBorderByGo(headNode, playerData.icon, playerData.border)

                local leagueData = otherData.league
                local league = GetChild(wagon, "league")
                local txtLeagueName = GetChild(wagon, "league/name", UEUI.Text)
                local txtLeagueIcon = GetChild(wagon, "league/icon", UEUI.Image)

                if leagueData then
                    if not IsNilOrEmpty(leagueData.icon) then
                        SetImageSprite(txtLeagueIcon, LeagueManager:GetUnionImageById(leagueData.icon), false)
                        SetActive(txtLeagueIcon, true)
                    else
                        SetActive(txtLeagueIcon, false)
                    end
                    txtLeagueName.text = leagueData.name
                    SetActive(txtLeagueName, true)

                    if leagueData.icon == 0 then
                        SetActive(txtLeagueIcon, false)
                    end
                else
                    SetActive(txtLeagueIcon, false)
                    SetActive(txtLeagueName, false)
                end

                SetActive(league, quality == SLG_QUALITY.UR)
                if leagueData.id == 0 or IsNilOrEmpty(leagueData.name) or leagueData.icon == 0 then
                    SetActive(league, false)
                end

                local fire1 = GetChild(wagon, "battlt_huochehuo_1")
                local fire2 = GetChild(wagon, "battlt_huochehuo_2")
                SetActive(fire1, false)
                SetActive(fire2, false)
                if otherData.attacked_times == 1 then
                    SetActive(fire1, true)
                elseif otherData.attacked_times == 2 then
                    SetActive(fire1, true)
                    SetActive(fire2, true)
                end
                SetActive(wagon, true)
            else
                SetActive(wagon, false)
            end
        end
    end
end

--- 刷新我的货车信息
function UI_TradeWagonsView:RefreshWagonInfoMe(callback)
    local unlockPos = self:GetUnlockPos()
    local needCount = 0
    local backCount = 0

    local isWagonFinished = false
    local isTrainFinished = false

    local isCallback = false

    for i = 1, 4, 1 do
        local info = TradeWagonsManager.loadInfo.trade_index_info[i]
        if info then
            if unlockPos[tostring(i)] then
                needCount = needCount + 1
                TradeWagonsManager:RequestWagonInfoMe(i, function (dataInfo)
                    backCount = backCount + 1
                    if backCount >= needCount then
                        isWagonFinished = true
                        if isWagonFinished and isTrainFinished and not isCallback then
                            isCallback = true
                            if callback then
                                callback()
                            end
                        end
                    end
                end)
            end
        end
    end

    TradeWagonsManager:RequestTrainLoadData(function (data)
        self:RefreshTrain()
        isTrainFinished = true
        if isWagonFinished and isTrainFinished and not isCallback then
            isCallback = true
            if callback then
                callback()
            end
        end
    end)
end

--- 获取已解锁的车位
--- @return table unlockPos 已解锁的位置
function UI_TradeWagonsView:GetUnlockPos()
    local unlockPos = {}

    -- 默认位置 1 和 2
    local defaultPos = TradeWagonsManager:GetTradeSettingConfig(3)
    local defaultPosTable = string.split(defaultPos, "|")
    for _, value in ipairs(defaultPosTable) do
        unlockPos[value] = true
    end

    -- 等级达到条件，解锁 3
    local pos3 = TradeWagonsManager:GetTradeSettingConfig(4)
    local pos3Table = string.split(pos3, "|")
    local level = NetUpdatePlayerData:GetLevel()
    if level >= v2n(pos3Table[2]) then
        unlockPos[pos3Table[1]] = true
    end

    -- 购买月卡，解锁 4
    local pos4 = TradeWagonsManager:GetTradeSettingConfig(5)
    local pos4Table = string.split(pos4, "|")
    local isUnlockMonthCard = NetMonthCardData:CheckUnlock(v2n(pos4Table[2]))
    if isUnlockMonthCard then
        unlockPos[pos4Table[1]] = true
    end

    return unlockPos
end

--- 重置货车
function UI_TradeWagonsView:ResetWagon()
    -- 云朵遮挡中
    local function openMid()
        CurSelectWagon = nil
        -- 请求重置货车和火车
        TradeWagonsManager:RequestWagonReset(function (data)
            -- 刷新他人货车
            self:RefreshWagon(data)

            -- 刷新火车
            if data and IsTableNotEmpty(data.train_record) then
                self:RefreshTrain()
            end

            -- 引导中
            if self.isGuide then
                self.isGuide = false
                TimeMgr:CreateTimer("UI_TradeWagonsView", function()
                    self:Guide2(data)
                end, 0.6, 1)
            end
        end)
    end

    -- 开云
    local function openEnd()
        UIMgr:SetUILock(false, "UI_TradeWagonsView", true)
    end

    -- 播放云朵动画
    PlayAnimStatusIndex(self.cloudAnim, "cloud_tradewagon", openMid, openEnd)

    -- 锁定屏幕
    UIMgr:SetUILock(true, "UI_TradeWagonsView", true)
end

--- 隐藏选中效果
function UI_TradeWagonsView:HideSelectEffect()
    for _, value in pairs(self.tradeCarSelectEffect) do
        SetActive(value, false)
    end
end

--- 刷新红点
function UI_TradeWagonsView:RefreshRedPoint()
    local totalCount = TradeWagonsManager:GetRedPointCount()
    self.ui.m_txtRedPointNum.text = totalCount
    SetActive(self.ui.m_goRedPoint, totalCount > 0)

    local hasPlunderRedPoint = TradeWagonsManager:CheckPlunderRedPoint()
    SetActive(self.ui.m_goPlunderRedPoint, hasPlunderRedPoint)
end

-- 打开新手引导
function UI_TradeWagonsView:Guide()

    -- 最后一步引导回调
    local function GuideCallback4()
        self:ResetWagon()
        UI_CLOSE(UIDefine.UI_GuideMask)
    end

    -- 引导积分达标奖励
    local function Guide4()
        local centerPos = UIRectPosFit(self.ui.m_btnResetWagon)
        UI_SHOW(UIDefine.UI_GuideMask, {
            {2, 0, 90},                -- 遮罩类型和大小
            centerPos,                 -- 遮罩位置
            {0.5, 0.5},                -- 遮罩按钮大小
            0.5,                       -- 缩放动画的时长
            function() GuideCallback4() end,   -- 点击回调
            {centerPos[1] / 100, centerPos[2] / 100 - 2, 0, 0, 180},   -- 箭头位置
            {-0.5, 0, 70000603},                    -- 对话框位置和内容
            "Sprite/new_hero/headFrame_1.png",   -- 对话框头像
            nil,
        })
    end

    -- 引导积分获取途径
    local function Guide3()
        local centerPos = UIRectPosFit(self.ui.m_btnPlunderRecord)
        local rt = GetComponent(self.ui.m_btnPlunderRecord, typeof(UE.RectTransform))
        local width = rt.rect.width
        local height = rt.rect.height
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetCenter, centerPos)
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetShow,{2, 0, 90})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetBtnSize,{0.5, 0.5})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetDialog,{-0.5, 0, 70000602})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetArrow,{centerPos[1] / 100, centerPos[2] / 100 - 2, 0, 0, 180})
        UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetCallBack,function ()
            UI_CLOSE(UIDefine.UI_GuideMask)
            UI_SHOW(UIDefine.UI_TradeWagonsRecord, TRADE_WAGONS_RECORD.Plunder, Guide4)
        end)
    end

    -- 关闭帮助界面，引导每日主题
    local function Guide2()
        local centerPos = UIRectPosFit(self.ui.m_txtCountToday)
        UI_SHOW(UIDefine.UI_GuideMask, {
            {2, 0, 150},             -- 遮罩类型和大小
            centerPos,                 -- 遮罩位置
            {2.8, 0.8},                    -- 遮罩按钮大小
            0.5,                       -- 缩放动画的时长
            function() Guide3() end,   -- 点击回调
            {centerPos[1] / 100, centerPos[2] / 100 - 2.2, 0, 0, 180},   -- 箭头位置
            {-0.5, 0, 70000601},                    -- 对话框位置和内容
            "Sprite/new_hero/headFrame_1.png",   -- 对话框头像
            nil,
        })
    end

    -- 第一步引导回调，打开帮助界面
    local function GuideCallback()
        UI_CLOSE(UIDefine.UI_GuideMask)
        UI_SHOW(UIDefine.UI_TradeWagonsHelpAll, Guide2)
    end

    -- 第一步引导帮助按钮
    local centerPos = UIRectPosFit(self.ui.m_btnHelp)
    UI_SHOW(UIDefine.UI_GuideMask, {
        {2, 0, 90},                -- 遮罩类型和大小
        centerPos,                 -- 遮罩位置
        {0.5, 0.5},                    -- 遮罩按钮大小
        0.5,                       -- 缩放动画的时长
        function() GuideCallback() end,   -- 点击回调
        {centerPos[1] / 100, centerPos[2] / 100 - 2, 0, 0, 180},   -- 箭头位置
        {-0.5, 0, 70000604},                    -- 对话框位置和内容
        "Sprite/new_hero/headFrame_1.png",   -- 对话框头像
        nil,
    })
end

function UI_TradeWagonsView:Guide2(data)
    local otherInfo = data.other_trade_info
    if #otherInfo > 0 then
        local otherData = otherInfo[1]
        self:Guide3(otherData)
    else
        self:Guide4()
    end

    -- self:Guide4()
end

function UI_TradeWagonsView:Guide3(otherData)
    local function NextGuide()
        self:Guide4()
    end

    -- 第一步引导回调，打开我的货车界面
    local function GuideCallback()
        UI_CLOSE(UIDefine.UI_GuideMask)

        local parent = self.ui.m_transWagon
        local pos = GetChild(parent, "pos" .. 1)
        if pos then
            -- 货车节点
            local wagon
            if pos.transform.childCount == 0 then
                self:Guide4()
                return
            else
                wagon = pos.transform:GetChild(0)
            end

            local tradeCarSelectEffect = GetChild(wagon, "TradeCarSelectEffect")

            CurSelectWagon = 1
            CurSelectRecordID = otherData.record_id
            if CurSelectWagon then
                self:HideSelectEffect()
                SetActive(tradeCarSelectEffect, CurSelectWagon == 1)
            else
                SetActive(tradeCarSelectEffect, false)
            end

            if otherData.end_timestamp then
                local remain = otherData.end_timestamp - TimeMgr:GetServerTime()
                if remain > 0 then
                    TradeWagonsManager:RequestWagonInfoOther(otherData.record_id, function (dataInfo)
                        UI_SHOW(UIDefine.UI_TradeWagonsDetail, TRADE_WAGONS_DETAIL.Other, dataInfo.truck_info, NextGuide)
                    end)
                else
                    UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000687))
                    self:Guide4()
                end
            else
                self:Guide4()
            end
        end
    end

    -- 第一步引导点击他人货车
    local pos1 = GetChild(self.ui.m_transWagon, "pos1")
    local centerPos = UIRectPosFit(pos1)
    UI_SHOW(UIDefine.UI_GuideMask, {
        {2, 0, 150},             -- 遮罩类型和大小
        centerPos,                 -- 遮罩位置
        {1.6, 0.85},                    -- 遮罩按钮大小
        0.5,                       -- 缩放动画的时长
        function() GuideCallback() end,   -- 点击回调
        {centerPos[1] / 100, centerPos[2] / 100 - 2.2, 0, 0, 180},   -- 箭头位置
        {-0.5, -1, 70000600},                    -- 对话框位置和内容
        "Sprite/new_hero/headFrame_1.png",   -- 对话框头像
        nil,
    })
end

function UI_TradeWagonsView:Guide4()
    -- 第一步引导回调，打开我的货车界面
    local function GuideCallback()
        UI_CLOSE(UIDefine.UI_GuideMask)
        -- DOScale(self.ui.m_imgHelp.transform, 1, 0.5)
        if IsTableNotEmpty(TradeWagonsManager.wagonsInfoMe) then
            -- UI_SHOW(UIDefine.UI_TradeWagonsWindow, true)
            self:OnClickTab(TRADE_WAGONS_TAB.My, true)
        end
    end

    -- 第一步引导开始运输按钮
    local centerPos = UIRectPosFit(self.ui.m_btnStartShipping)
    UI_SHOW(UIDefine.UI_GuideMask, {
        {2, 0, 150},             -- 遮罩类型和大小
        centerPos,                 -- 遮罩位置
        {2.1, 0.85},                    -- 遮罩按钮大小
        0.5,                       -- 缩放动画的时长
        function() GuideCallback() end,   -- 点击回调
        {centerPos[1] / 100, centerPos[2] / 100 - 2, 0, 0, 180},   -- 箭头位置
        {-0.5, 0, 70000605},                    -- 对话框位置和内容
        "Sprite/new_hero/headFrame_1.png",   -- 对话框头像
        nil,
    })
end

--endregion -------------------------------------- 货车 -----------------------------------------

--region ----------------------------------------- 火车 -----------------------------------------

--- 初始化火车
function UI_TradeWagonsView:InitTrain()
    self.goTradeTrain = GoTradeTrain:Create(self.ui.m_goTradeTrain)

    -- 等待加载完成后再进行初始化
    self.goTradeTrain:WaitForLoad(function(trainInstance)
        trainInstance:SetAnimAuto(true)
        trainInstance:SetEffectPlunder(self.ui.m_goPlunderTrainEffect)
        local btnTrain = GetComponent(trainInstance.trans, UEUI.Button)
        local btnTrainGold = GetComponent(trainInstance.transGold, UEUI.Button)

        local function onClickTrainHead()
            if self.trainData then
                local endTime = self.trainData.end_timestamp or 0
                local endRemain = endTime - TimeMgr:GetServerTime()
                if endRemain >= 0 then
                    local cannotPlunderTime = v2n(TradeWagonsManager:GetTradeSettingConfig(10))
                    if endRemain <= cannotPlunderTime then
                        UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000470))
                        -- TradeWagonsManager:ClearCanLootTrain()
                        UI_UPDATE(UIDefine.UI_TradeWagonsView, 3)
                    else
                        UI_SHOW(UIDefine.UI_TradeWagonsTrainDetail, TRADE_WAGONS_DETAIL.Other, self.trainData)
                    end
                else
                    UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000489))
                    TradeWagonsManager:ClearCanLootTrain()
                    UI_UPDATE(UIDefine.UI_TradeWagonsView, 3)
                end
            end
        end

        btnTrain.onClick:AddListener(function ()
            onClickTrainHead()
        end)
        btnTrainGold.onClick:AddListener(function ()
            onClickTrainHead()
        end)
        self.train = GetComponent(trainInstance.go, UE.RectTransform)
        self.headNode = trainInstance.headNode
        SetMyHeadAndBorderByGo(self.headNode)
    end)
end

--- 刷新火车
function UI_TradeWagonsView:RefreshTrain()
    if not self.ui then return end
    -- 显示可掠夺的火车
    local can_loot_train = TradeWagonsManager.trainLoadInfo.can_loot_train
    if IsTableNotEmpty(can_loot_train) then
        self.trainData = can_loot_train
        local quality = {}
        for _, value in pairs(self.trainData.train_quality) do
            if value.part > 0 then
                quality[value.part] = value.quality
            end
        end

        -- 安全调用 GoTradeTrain 的方法
        if self.goTradeTrain then
            self.goTradeTrain:WaitForLoad(function(trainInstance)
                trainInstance:SetBodyIconByQualityList(quality)
                trainInstance:CheckGold(self.trainData)

                local myPlayerInfo = NetUpdatePlayerData:GetPlayerInfo()
                if self.trainData.captain.id == myPlayerInfo.id then
                    local myCaptain = {
                        name = myPlayerInfo.name,
                        icon = myPlayerInfo.head,
                        border = myPlayerInfo.headBorder
                    }
                    trainInstance:SetConductorInfo(myCaptain)
                else
                    trainInstance:SetConductorInfo(self.trainData.captain)
                end
                trainInstance:ShowEffectPlunder(self.trainData)
            end)
        end

        SetActive(self.ui.m_goTradeTrain, true)
    else
        SetActive(self.ui.m_goTradeTrain, false)
    end
end

--- 火车进入动画
function UI_TradeWagonsView:MoveTrainEnter()
    local canvasRect = UIMgr:GetCanvasRectTrans()
    local canvasWidth = canvasRect.rect.width
    local trainWidth = self.train.rect.width

    local initPosX = -(canvasWidth + trainWidth) / 2

    SetUIPos(self.train, initPosX, 0)

    SetActive(self.ui.m_goTradeTrain, true)

    DOLocalMoveX(self.train, 0, 3, function()

    end, Ease.InOutQuad)
end

--- 火车退出动画
function UI_TradeWagonsView:MoveTrainExit()
    local canvasRect = UIMgr:GetCanvasRectTrans()
    local canvasWidth = canvasRect.rect.width
    local trainWidth = self.train.rect.width

    local endPosX = (canvasWidth + trainWidth) / 2

    DOLocalMoveX(self.train, endPosX, 3, function()
        SetActive(self.ui.m_goTradeTrain, false)
    end, Ease.InQuad)
end

--endregion -------------------------------------- 火车 -----------------------------------------

--region ----------------------------------------- 变强礼包 -----------------------------------------
--刷新变强礼包
function UI_TradeWagonsView:OnRefreshStrongGift()
    local list = StrongGiftManager:GetGiftList()
    local isEmpty = next(list) == nil
    self.canStrongGiftTimer =  not isEmpty
    SetActive(self.ui.m_btnStrongGift,not isEmpty)
    self.giftList = list

    local gift = StrongGiftManager:GetQuickGift()
    if gift then
        self.finalTime = StrongGiftManager:GetFinalTime(gift.gift_id,gift.create_timestamp)
    end
end

--变强礼包倒计时
function UI_TradeWagonsView:OnStrongGiftTimerLogic()
    if self.canStrongGiftTimer then
        local time = self.finalTime - TimeMgr:GetServerTime()
        if time <= 0 then
            self.canStrongGiftTimer = false
            self:OnRefreshStrongGift()
        else
            self.ui.m_txtTimer.text = TimeMgr:ConverSecondToString(time)
        end
    end
end

--endregion -------------------------------------- 变强礼包 -----------------------------------------


return UI_TradeWagonsView