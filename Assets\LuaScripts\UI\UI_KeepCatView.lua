local UI_KeepCatView = Class(BaseView)
local GoGiftItem = require("UI.GoGiftItem")
local ItemBase = require("UI.Common.BaseSlideItem")
local catItem = Class(ItemBase)
local RankBox = require("UI.RankBox")
local ItemShowNums = 4
local CatUpAnimTime = 0.5
local CirculatePopCD = 5
function UI_KeepCatView:OnInit()
    EventMgr:Add(EventID.GLOBAL_TOUCH_BEGIN, self.OnGlobalTouchBegin, self)
end

function UI_KeepCatView:OnCreate(isPush)
	self.isPush = isPush
	self.config = ConfigMgr:GetData(ConfigDefine.ID.cat_illustrated)
	self.catData = {}
	self.flyCatList = {}
	self.tweenRankList = {}
	self.tweenPassPortList = {}
	self.tweenRankCD = 0
	self.tweenPassPortCD = 0
	self.onceCheckSliderTween = false
	self.active = LimitActivityController:GetActive(ActivityTotal.KeepCat)
    local rankPos = self:ConvertToRectPos(self.ui.m_btnGoRank)
    local passportPos = self:ConvertToRectPos(self.ui.m_btnPassPort)
    KeepCatManager:SetKeepCatRankPos({x = rankPos[1], y = rankPos[2]})
    KeepCatManager:SetKeepCatPassPortPos({x = passportPos[1], y = passportPos[2]})
	self.canClick = false
	self:SetIsUpdateTick(true)
	self.scrollIndex = 1
	self.circulateCD = 0
	for k, v in pairs(self.config) do
		if nil == self.catData[v.quality] then
			self.catData[v.quality] = {}
			table.insert(self.catData[v.quality],v)
		else
			table.insert(self.catData[v.quality],v)
		end
	end 
	self.tweenRankGo = self.ui.m_goTweenTxt
	self.airIndex = 1
	self.content = self.ui.m_goContent.transform
	self.slider = require("UI.Common.SlideRect").new()
	self.slider:Init(self.uiGameObject.transform:Find("m_goMid/m_scrollview"):GetComponent(typeof(UEUI.ScrollRect)),2)
	self.catItemList = {}
	for i = 1, ItemShowNums do
		self.catItemList[i] = catItem.new()
		self.catItemList[i]:Init(UEGO.Instantiate(self.ui.m_goCatList.transform))
	end
	self.slider:SetItems(self.catItemList,-15,Vector2.New(5,0))
	
	self.slider:SetData(self.catData)
	--直购礼包入口
	local function callBack(obj)
		self.goGift = obj
	end
	GoGiftItem:Create(self.ui.m_goActGift.transform,ActivityTotal.KeepCat,nil,callBack)
	
	--TopSlider
	self.oldStarNum = KeepCatManager:GetCatAllStar()
	self.nowStarNum = KeepCatManager:GetCatAllStar(true)
	self.oldLv,self.oldReward,self.oldNum,self.oldMaxNum = KeepCatManager:GetCurStarReward(self.oldStarNum)
	self.nowLv,self.nowReward,self.nowNum,self.newMaxNum = KeepCatManager:GetCurStarReward(self.nowStarNum)


	self.ui.m_txtAllStar.text = self.oldLv
	self.ui.m_slider1.value = self.oldNum/self.oldMaxNum
	if self.oldNum < self.oldMaxNum then
		self.ui.m_txtAllStarCount.text = self.oldNum.."/"..self.oldMaxNum
	else
		self.ui.m_txtAllStarCount.text = LangMgr:GetLang(8215);
	end

	local arr = string.split(self.oldReward, "|")
	local itemId = NetSeasonActivity:GetChangeItemId(v2n(arr[1]))
	SetUIImage(self.ui.m_imgStarReward,ItemConfig:GetIcon(v2n(itemId)), false)
	self.ui.m_txtStarReward.text = "x"..arr[2]
	--RightSlider
	self.maxStar = v2n(KeepCatManager:GetSettingConfigById(4).value or 48)
	self.ui.m_txtStarNum.text = self.oldStarNum.."/"..self.maxStar
	self.ui.m_slider2.value = self.oldStarNum/self.maxStar

	self:UpdateStarRewardSign();

	--star Animation
	self.catOpenAnim = GetComponent(self.uiGameObject, UE.Animation)

	PlayAnimStatus(self.catOpenAnim,"UI_KeepCatView",function ()
			if self.isPush then
				self:Guide()
			else
				self:CheckIsUp()
			end
			self.canClick = true
	end)
	-- 旧积分 动画积分
	local tweenOldIntegral = NetKeepCatData:GetOldIntegral()
	if tweenOldIntegral == 0 then
		self:RefreshPassport()
	else
		local tweenOldEnergySch = NetKeepCatData:GetOldEnergySch()
		local maxCount = self.active.form.max
		local schedule = tweenOldEnergySch + 1
		schedule = math.min(schedule,maxCount)
		local oldNeedPoint = tonumber(self.active.form.exps[schedule])
		self.ui.m_txtPassPort.text = tweenOldIntegral .. "/" .. oldNeedPoint
		self.ui.m_slider3.value = tweenOldIntegral / oldNeedPoint
	end
	self:ScrollRectEvent()
	self:CreateScheduleFun(function() self:Timer() end,1)
	KeepCatManager:QueryCatStar(3,function (data)
			self:InitServerData(data)
		end)
	-- 请求排行榜信息拿到排名
	local function rankCallBack(serverData)
		local myselfData = serverData.player
		if myselfData then
			if v2n(myselfData.rank) ~= 0 then
				self.ui.m_txtSelfRank.text = myselfData.rank
			end
		end
	end
	self.ui.m_txtSelfRank.text = "?"
	NetKeepCatData:RequestRankData(rankCallBack)
	
	if self.oldStarNum ~ self.nowStarNum then
		local thinkTable = {
			["cat_oldlevel"] = self.oldLv,
			["cat_oldlevelprogress"] = self.oldNum,
			["cat_newlevel"] = self.nowLv,
			["cat_newlevelprogress"] = self.nowNum,
			["cat_levelprogressmax"] = self.newMaxNum
		}
		SdkHelper:ThinkingTrackEvent(ThinkingKey.CatLevel, thinkTable)
	end
end

function UI_KeepCatView:Timer()
	if self.circulateCD >= CirculatePopCD then 	--and not self.playingSpine
		self:ShowScoreTip()
		self.circulateCD = 0
	else
		self.circulateCD = self.circulateCD + 1
	end

end

function UI_KeepCatView:InitServerPop(data)
	self.maxStarCatSerData = {}
	for k, v in pairs(self.config) do
		if nil ~= data[tostring(v.item)] then
			self.canShowScoreTips = true
			self.maxStarCatSerData[tostring(v.item)] = data[tostring(v.item)]
		else
			self.maxStarCatSerData[tostring(v.item)] = 0
		end
	end
	--self:ShowScoreTip()
end

function UI_KeepCatView:ShowScoreTip()
	if self.canShowScoreTips then
		local showList = self.catItemList[self.scrollIndex]:GetCatLineShowList()
		for k, v in pairs(showList) do
			if self.maxStarCatSerData[tostring(v.id)] > 0 then
				local catItemTrans = self.catItemList[self.scrollIndex].catLineList[v.index].catObj
				self.ui.m_goScoreTip.transform.position = Vector3(catItemTrans.position.x,catItemTrans.position.y+1,0)
				self.ui.m_txtScoreTip.text = LangMgr:GetLangFormat(51040058,tostring(self.maxStarCatSerData[tostring(v.id)]))
				self.catItemList[self.scrollIndex]:RemoveShowLine(k)
				SetActive(self.ui.m_goScoreTip,true)
				UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.ui.m_transScoreBg.transform);
				self.circulateTime = 0
				break
			end
		end
	end
end

function UI_KeepCatView:InitServerData(data)
	if nil ~= data then
		self.serverData = data
		if self.serverData.full_amount > 0 then --cat_nums
			self.ui.m_txtMaxTip.text = LangMgr:GetLangFormat(51040059,tostring(self.serverData.full_amount))
			SetActive(self.ui.m_goMaxTip,true)
		else
			SetActive(self.ui.m_goMaxTip,false)
		end
		if nil ~= self.serverData.cat_nums then
			self:InitServerPop(self.serverData.cat_nums)
		end
	end
end

function UI_KeepCatView:ScrollRectEvent()
	local scrollrect = self.uiGameObject.transform:Find("m_goMid/m_scrollview"):GetComponent(typeof(UEUI.ScrollRect))
	scrollrect.onValueChanged:RemoveAllListeners()
	scrollrect.onValueChanged:AddListener(function (value)
			local verticalNormalizedPosition = self.slider:GetPos()
			local totalNum = table.count(self.catData)
			--local singePro = 1/totalNum
			
			self.scrollIndex = self.slider:GetNearIndex()
			local horValue = Mathf.Clamp(verticalNormalizedPosition,0,1)
			if self.scrollIndex == 3 then
				if horValue < 0.05 then
					self.scrollIndex = 4
				else
					self.scrollIndex = math.random(3,4)
				end				
			elseif self.scrollIndex == 2 then
				if horValue < 0.35 then
					self.scrollIndex = 3
				end
			end
			--SetActive(self.ui.m_goCatTip,false)
			--SetActive(self.ui.m_goItemRewardTip, false);
			--SetActive(self.ui.m_goScoreTip,false)			
		end)
end

function UI_KeepCatView:Guide()
	
	local function guide1()
		local centerPos2 = self:ConvertToRectPos(self.ui.m_btnGoldCat.gameObject.transform)
		UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetCenter,centerPos2)
		UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetShow,{3,250,450})
		UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetBtnSize,{1,1.8})
		UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetDialog,{-2.5,0,51040056})
		UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetArrow,nil)
		UI_UPDATE(UIDefine.UI_GuideMask,GuideMask_Refresh.SetCallBack,function ()
			UI_CLOSE(UIDefine.UI_GuideMask)
			self:CheckIsUp()
		end)
	end
	-- 节点位置
	local centerPos = self:ConvertToRectPos(self.ui.m_btnStarReward.gameObject.transform)
	UI_SHOW(UIDefine.UI_GuideMask, {
			{3,950,130},
			centerPos,
			{4,0.6},
			0.5,
			function ()
				guide1()
			end,
			nil,--{centerPos[1] / 100, centerPos[2] / 100 - 2, 0, 0, 180}
			{-1.5,0,51040075},
			"Sprite/new_hero/headFrame_cat_1.png"
		})
end

--- 转换 UI 坐标
--- @param go any UI 节点
--- @return table 坐标表
function UI_KeepCatView:ConvertToRectPos(go)
	local cam = UIMgr:GetCamera()
	local screenPoint = UE.RectTransformUtility.WorldToScreenPoint(cam, go.transform.position)
	local _, pos = UE.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.uiRectTransform,
		Vector2.New(screenPoint.x, screenPoint.y), cam)
	local posTable = {pos.x, pos.y}
	return posTable
end

function UI_KeepCatView:CheckIsUp()
	--moveIndex
	if nil ~= KeepCatManager.clickFoodType then
		--KeepCatManager.clickFoodType = nil
		self.slider:MoveToIndex(3,0.3,function ()
				TimeMgr:CreateTimer(self, function() self:PlayCatUpStar() end, 0, 1)			
		end)		
	else
		TimeMgr:CreateTimer(self, function() self:PlayCatUpStar() end, 0, 1)
	end
	
end

function UI_KeepCatView:PlayCatUpStar()
	for k, v in pairs(self.catItemList) do
		v:PlayCatUpAnim()
		if k == table.count(self.catItemList) then
			TimeMgr:CreateTimer(self, function() UI_UPDATE(UIDefine.UI_KeepCatView,1) end, CatUpAnimTime+0.8+1.1, 1)--待完善 0.8是flyModel的时间 1.1是catItem时间
		end
	end
end

function UI_KeepCatView:TickUI(deltaTime)
	local time = self.active:GetRemainingTime()
	if time > 0 then
		self.ui.m_txtTime.text = TimeMgr:CheckHMSNotEmpty(time)
	else
		self.ui.m_txtTime.text = " "
	end

	if not IsTableEmpty(self.tweenRankList) then
		if self.tweenRankCD < 0.5 then
			self.tweenRankCD = self.tweenRankCD + deltaTime
		else
			self.tweenRankCD = 0
			local addValue = table.remove(self.tweenRankList,1)
			if addValue then
				local instanceGo = UEGO.Instantiate(self.tweenRankGo,self.ui.m_btnGoRank.transform)
				SetActive(instanceGo,true)
				instanceGo.transform.localScale = Vector3.New(1,1,1)
				instanceGo.transform:SetLocalPosition(0, -150, 0)
				local txtNum = GetChild(instanceGo,"txtTweenAddScore",UEUI.Text)
				local mask = txtNum:GetComponent("CanvasGroup")
				txtNum.text = "+" .. addValue
				DOLocalMoveY(instanceGo.transform,-90,1,function()
					DOFadeAlpha(mask, 1, 0, 0.5, 0.2, 0, Ease.Linear, function()
						UEGO.Destroy(instanceGo)
					end)
				end,Ease.OutQuad)
			end
		end
	end

	if not IsTableEmpty(self.tweenPassPortList) then
		if self.tweenPassPortCD < 0.5 then
			self.tweenPassPortCD = self.tweenPassPortCD + deltaTime
		else
			self.tweenPassPortCD = 0
			local addValue = table.remove(self.tweenPassPortList,1)
			if addValue then
				local instanceGo = UEGO.Instantiate(self.tweenRankGo,self.ui.m_btnPassPort.transform)
				SetActive(instanceGo,true)
				instanceGo.transform.localScale = Vector3.New(1,1,1)
				instanceGo.transform:SetLocalPosition(0, 120, 0)
				local txtNum = GetChild(instanceGo,"txtTweenAddScore",UEUI.Text)
				local mask = txtNum:GetComponent("CanvasGroup")
				txtNum.text = "+" .. addValue
				DOLocalMoveY(instanceGo.transform,210,1,function()
					DOFadeAlpha(mask, 1, 0, 0.5, 0.2, 0, Ease.Linear, function()
						UEGO.Destroy(instanceGo)
					end)
				end,Ease.OutQuad)
			end
		end
	else
		if self.onceCheckSliderTween then
			self.onceCheckSliderTween = false
			self:RefreshPassport()
		end
	end
end

function UI_KeepCatView:OnRefresh(param1,param2,param3,param4)
    if param1 == 1 then
		--升星后刷新主界面
		if self.oldStarNum ~= self.nowStarNum then
			--Log.Error("xxx111",self.flyCatList)
			local function flyEnd()
				--Log.Error("升星后刷新主界面",self.oldStarNum ~= self.nowStarNum)
				self:RefreshTopSlider()
				self:RefreshRightSlider()
				--self.flyCatList = {}
			end
			if table.count(self.flyCatList) > 0 then
				self:FlyAddStar(flyEnd)
			else
				flyEnd()
			end	
		end
	elseif param1 == 2 then
		--需要飞星星的猫
	
		table.insert(self.flyCatList,{["obj"] = param2,["flyNum"] = param3})

	elseif param1 == 3 then
		--单个猫的奖励
		self:FlyCatReward(param2,param3)
	elseif param1 == 4 then
		--星级的领奖
		self:UpdateStarRewardSign();
	elseif param1 == 5 then
		self:RefreshPassport()
	elseif param1 == 6 then
		--猫tisp
		self:ShowCatTip(param2,param3)
	elseif param1 == 7 then
		self:ShowCatItemReward(param2, param3)
	elseif param1 == 8 then
		-- 加排行榜通行证积分
		table.insert(self.tweenRankList,param2)
		table.insert(self.tweenPassPortList,param2)
		if not self.onceCheckSliderTween then
			self.onceCheckSliderTween = true
		end
	end
end

function UI_KeepCatView:UpdateStarRewardSign(curLv)
	local reward = NetKeepCatData:GetStarRewardList()
	local sign = NetKeepCatData.data.starRewardSign
	if curLv == nil then
		curLv = 0
		for k, v in pairs(sign) do
			if v == ITEM_STATE.REWARED then
				curLv = k
			end
		end
	end
	self.ui.m_txtAllStarCount2.text = LangMgr:GetLang(16)..curLv.."/"..table.count(sign)
end

function UI_KeepCatView:ShowCatItemReward(transform, catId)
	local catConfig = ConfigMgr:GetDataByKey(ConfigDefine.ID.cat_illustrated, "item", v2n(catId));
	if catConfig then	
        local catData = NetKeepCatData:GetCatListDataByCatId(catId)
        local oldNum = catData.cur_num
        local newNum = catData.new_num
        local starlv,fill,lvLast,needNum = KeepCatManager:GetCurStarByCatId(catId,oldNum+newNum, true)
        local rewardLevel
        if starlv == 1 then
            rewardLevel = catConfig.reward1
        elseif starlv == 2 then
            rewardLevel = catConfig.reward2
        elseif starlv == 3 then
            rewardLevel = catConfig.reward
        end
		local rewardStr = NetSeasonActivity:GetChangeItemId(rewardLevel);
		local rewardArr = string.split(rewardStr, ";");
		local len = #rewardArr;
		local childCount = self.ui.m_goItemRewardList.transform.childCount;
		for i = 1, childCount do
			local item = GetChild(self.ui.m_goItemRewardList, "item" .. i);
			if i <= len then
				local icon = GetChild(item, "icon",  UEUI.Image);
				local numTxt = GetChild(item, "numTxt", UEUI.Text);

				local list = string.split(rewardArr[i], "|");
				SetImageSync(icon, ItemConfig:GetIcon(v2n(list[1])), false)
				numTxt.text = "x" .. list[2];
			end
			SetActive(item, i <= len);
		end
		self.ui.m_goItemRewardTip.transform.position = Vector3(transform.position.x, transform.position.y + 0.4, 0);

		SetActive(self.ui.m_goItemRewardTip, true);

		local bgTrans = GetChild(self.ui.m_goItemRewardTip, "bg", UE.Transform);
		UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(bgTrans);
	else
		SetActive(self.ui.m_goItemRewardTip, false);
	end
end

function UI_KeepCatView:ShowCatTip(transform,catId)
	
	local catConfig = ConfigMgr:GetDataByKey(ConfigDefine.ID.cat_illustrated, "item",v2n(catId))
	if catConfig then
		local score = NetKeepCatData:GetScoreByCatId(catId)
		local quaLangid
        local itemID = catConfig.item
		if catConfig.quality == 2 then
			quaLangid = 90200116
		elseif catConfig.quality == 3 then
			quaLangid = 90200117
		elseif catConfig.quality == 4 then
			quaLangid = 90200118
		else
			quaLangid = 90200115
		end
        local itemConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.item, itemID)
        if itemConfig then
            self.ui.m_txtCatTip1.text = LangMgr:GetLang(itemConfig.id_lang)
        end
		self.ui.m_txtCatTip2.text = LangMgr:GetLang(quaLangid)
		self.ui.m_txtCatTip3.text = LangMgr:GetLang(90200121).." "..score
		self.ui.m_goCatTip.transform.position = Vector3(transform.position.x,transform.position.y+0.5,0)
		SetActive(self.ui.m_goCatTip,true)
		UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.ui.m_transCatTipBg);
	else
		SetActive(self.ui.m_goCatTip,false)
	end
	
end

function UI_KeepCatView:RefreshTopSlider()
	self.ui.m_txtAllStar.text = self.nowLv
	local starFill = self.oldNum/self.oldMaxNum
	if starFill > self.nowNum/self.newMaxNum then
		starFill = 0
	else
		starFill = 0
	end
	AddDOTweenNumberDelay(starFill,self.nowNum/self.newMaxNum,CatUpAnimTime,0, function(value)
			self.ui.m_slider1.value = value
		end,function ()
			if self.nowNum < self.newMaxNum then
				self.ui.m_txtAllStarCount.text = self.nowNum.."/"..self.newMaxNum
				self:UpdateStarRewardSign(self.nowLv - 1);
			else
				self.ui.m_txtAllStarCount.text = LangMgr:GetLang(8215);
				self:UpdateStarRewardSign(self.nowLv);
			end

			local arr = string.split(self.nowReward, "|")
			local itemId = NetSeasonActivity:GetChangeItemId(v2n(arr[1]))
			SetUIImage(self.ui.m_imgStarReward,ItemConfig:GetIcon(v2n(itemId)), false)
			self.ui.m_txtStarReward.text = "x"..arr[2]
			self:BubbleFlyStarReward()
		end)
end

function UI_KeepCatView:RefreshRightSlider()
	--local curNum = KeepCatManager:GetCatAllStar(true)
	AddDOTweenNumberDelay(self.oldStarNum/self.maxStar,self.nowStarNum/self.maxStar,CatUpAnimTime, 0, function(value)
			self.ui.m_slider2.value = value
		end,function()
			KeepCatManager:CheckStarTotalReward()
			--self:SubCacheData()
			self.ui.m_txtStarNum.text = self.nowStarNum.."/"..self.maxStar			
		end)
end

function UI_KeepCatView:BubbleFlyStarReward()
	--flytoair
	local rewardList = NetKeepCatData:GetStarRewardList()
	for k, v in pairs(rewardList) do
		local arr = string.split(v,"|")		
		self:BubbleByIndexAnim(v2n(arr[1]),v2n(arr[2]))
	end
	
	--Log.Error("rewardList",rewardList)
	--netSign
	--NetKeepCatData:GetStarRewardByCatId()
end

function UI_KeepCatView:FlyAddStar(callBack)
	--Log.Error("FlyAddStar")
	for k, v in pairs(self.flyCatList) do
		
		self:FlyCatStar(v.obj,v.flyNum,function ()
			if k == table.count(self.flyCatList) then
				if callBack then
					callBack()	
				end
			end
		end)
	end
end

function UI_KeepCatView:FlyCatStar(obj,flyNum,onCompleted)
	local oldPos =  UIMgr:GetObjectScreenPos(obj.catObj.transform)
	local posScreen = UIMgr:GetUIPosByWorld(self.ui.m_goFlyStar.transform.position)

	local flyId = 198429
	local num = flyNum
	MapController:FlyUIAnim(oldPos.x,oldPos.y,flyId,num,posScreen.x, posScreen.y,
		nil,nil,nil,0.7,nil,function ()
			if onCompleted then
				onCompleted()
			end
		end)
end

function UI_KeepCatView:BubbleByIndexAnim(rewardId,_num,onCompleted,_oldPos)
	if not self.ui then return end
	SetActive(self.ui.m_goTarget,true)
	local oldPos = _oldPos or UIMgr:GetUIPosByWorld(self.ui.m_btnStarReward.gameObject.transform.position)
	local posScreen = UIMgr:GetUIPosByWorld(self.ui.m_goTarget.transform.position)
	local flyId = NetSeasonActivity:GetChangeItemId(v2n(rewardId))
	local num = _num or 1
	MapController:FlyUIAnim(oldPos.x,oldPos.y,flyId,num,posScreen.x, posScreen.y,
		nil,nil,nil,0.7,nil,function ()
			if not self.ui then return end
			if self.airIndex <= 5 then
				local rewardGo     = GetChild(self.ui.m_goPanel,"pos" .. self.airIndex ,UEUI.Image)
				self.airIndex = self.airIndex + 1
				SetActive(rewardGo,true)
				SetImageSprite(rewardGo,ItemConfig:GetIcon(v2n(NetSeasonActivity:GetChangeItemId(rewardId))),false)
			else
				local rewardGo     = GetChild(self.ui.m_goPanel,"pos" .. 1 ,UEUI.Image)
				SetImageSprite(rewardGo,ItemConfig:GetIcon(v2n(NetSeasonActivity:GetChangeItemId(rewardId))),false)
			end
			-- 奖励云播放动画
			GetChild(self.ui.m_goTarget, "bubble", Animation):Play()
			GetChild(self.ui.m_goTarget, "bg", Animation):Play()
			if onCompleted then
				onCompleted()
			end
		end)
end

function UI_KeepCatView:FlyCatReward(obj,rewardStr,onCompleted)
	local arr = string.split(rewardStr,";")
	for k, v in pairs(arr) do
		local arr2 = string.split(v,"|")
		local id = v2n(arr2[1])
		local num = v2n(arr2[2])
		local oldPos = UIMgr:GetUIPosByWorld(obj.transform.position)
		if id < ItemID._RESOURCE_MAX then
			MapController:AddResourceBoomAnim(oldPos.x,oldPos.y,id,num)
			-- NetUpdatePlayerData:AddResource(PlayerDefine[id],num,nil,nil,"UI_KeepCatView")
		else			
			self:BubbleByIndexAnim(id,num,onCompleted,oldPos)
		end
	end		
end

function UI_KeepCatView:SubCacheData()
	NetKeepCatData:SubNewCatList()
	NetKeepCatData:CheckStarReward()
	
	NetKeepCatData:GetAirReward()
	NetKeepCatData:ClearAddScoreTweenList()
end

function UI_KeepCatView:onDestroy()
	if self.isPush then
		NetPushViewData:RemoveViewByIndex(PushDefine.UI_KeepCatView)
		NetPushViewData:CheckOtherView(true,true)
	end
	KeepCatManager:SetKeepCatRankPos(nil)
	KeepCatManager:SetKeepCatPassPortPos(nil)
	KeepCatManager.clickFoodType = nil 
	self:SubCacheData()
	--addAir
	Tween.Kill("AutoMoveFunc")
	TimeMgr:DeleteTimer(self)
	self.goGift:Close()
	self.tweenRankList = {}
	self.tweenPassPortList = {}
	self.tweenRankCD = 0
	self.tweenPassPortCD = 0
	if self.sequence then
		self.sequence:Kill()
		self.sequence = nil
	end
	NetKeepCatData:SetOldIntegral(0)
	NetKeepCatData:SetOldEnergySch(0)
	self.onceCheckSliderTween = false
	EventMgr:Remove(EventID.GLOBAL_TOUCH_BEGIN, self.OnGlobalTouchBegin, self)

end

function UI_KeepCatView:onUIEventClick(go,param)
    local name = go.name
	if name == "m_btnClose" then
		self:Close()
	end
	if not self.canClick then
		return 
	end
	if name == "m_btnGoldCat" then
		UI_SHOW(UIDefine.UI_KeepCatExtraReward);
	elseif name == "m_btnStarReward" then
		UI_SHOW(UIDefine.UI_KeepCatStarReward);
	elseif name == "m_btnPassPort" then
		UI_SHOW(UIDefine.UI_KeepCatPassPort)
	elseif name == "m_btnSub" then
		--NetKeepCatData:SubNewCatList()
		UI_SHOW(UIDefine.UI_KeepCatHelp);	
	elseif name == "m_btnGoRank" then
		self:ShowRank()
	elseif name == "m_btnFind" then
		self:OnBtnFind()
		self:Close()
	end
end

--- 显示排行榜
function UI_KeepCatView:ShowRank()
	local isOpen,active = LimitActivityController:GetActiveIsOpenByTotal(ActivityTotal.KeepCat)
	if isOpen then
		-- 上榜积分
		local score = KeepCatManager:GetRankPoint()
		local totalScore = NetKeepCatData:GetTotalScore()
		if score and totalScore >= score then
			self:OpenRank()
		else
			UI_SHOW(UIDefine.UI_KeepCatRank,nil,nil,nil,nil,nil,true)
		end
	end
end

function UI_KeepCatView:OpenRank()
	local function rankCallBack(data)
		UI_SHOW(UIDefine.UI_KeepCatRank,data)
	end
	NetKeepCatData:RequestRankData(rankCallBack)
end

function UI_KeepCatView:IsInView(v)
	
end

function UI_KeepCatView:GetNowEnergySch()
	local maxCount = self.active.form.max
	local schedule = self.active.info.energySch + 1
	schedule = math.min(schedule,maxCount)
	return schedule
end

function UI_KeepCatView:RefreshPassport()
	local energySch  = self:GetNowEnergySch()
	local nextScore = self.active.form.exps[energySch]
	local nowScore = v2n(self.active.info.integral)
	if self.active:GetIsMaxLimit() then
		self.ui.m_txtPassPort.text = LangMgr:GetLang(5003)
		self.ui.m_slider3.value = 1
	else
		-- 旧积分 动画积分
		local tweenOldIntegral = NetKeepCatData:GetOldIntegral()
		if tweenOldIntegral ~= 0 then
			self:PlaySliderTween()
		else
			self.ui.m_txtPassPort.text = string.format("%d/%d",nowScore,nextScore)
			self.ui.m_slider3.value = nowScore/nextScore
		end
	end

	
	SetActive(self.ui.m_imgPassPortRed,KeepCatManager:GetPassPortRed() > 0)
end

function UI_KeepCatView:PlaySliderTween()
	local tweenOldIntegral = NetKeepCatData:GetOldIntegral()
	local tweenOldEnergySch = NetKeepCatData:GetOldEnergySch()
	local curIntegral = self.active.info.integral	
	local curEnergySch = self.active.info.energySch
	local upNum = curEnergySch - tweenOldEnergySch
	if self.sequence then
		self.sequence:Kill()
	end
	self.sequence = Tween.Sequence()
	self.sequence:SetAutoKill(true)
	local tweenSpeed = 2

	if upNum > 0 then
		for i = 1, upNum, 1 do
			local speed
			local needPoint = tonumber(self.active.form.exps[tweenOldEnergySch + i])
			if i == 1 then
				speed = (1 - self.ui.m_slider3.value) * tweenSpeed
			else
				speed = tweenSpeed
			end
			self.sequence:Append(self.ui.m_slider3:DOValue(1,speed))
			self.sequence:AppendCallback(function ()
				self.ui.m_slider3.value = 0
			end):OnUpdate(function ()
				local value = self.ui.m_slider3.value * needPoint
				self.ui.m_txtPassPort.text = Mathf.Floor(value + 0.5) .. "/" .. needPoint
			end)
		end
	end
	local nextEngergy = self:GetNowEnergySch()
	local nextScore = self.active.form.exps[nextEngergy]
	local present = curIntegral / nextScore
	self.sequence:Append(self.ui.m_slider3:DOValue(present,tweenSpeed * present):OnUpdate(function ()
		local value = self.ui.m_slider3.value * nextScore
		self.ui.m_txtPassPort.text = Mathf.Floor(value + 0.5) .. "/" .. nextScore
	end))
	self.sequence:AppendCallback(function ()
		self.ui.m_txtPassPort.text = string.format("%d/%d",curIntegral,nextScore)
		self.ui.m_slider3.value = curIntegral/nextScore
		NetKeepCatData:SetOldIntegral(0)
		NetKeepCatData:SetOldEnergySch(0)
	end)
end

function UI_KeepCatView:OnGlobalTouchBegin(vtArray,touchCount,isForce3D)
	SetActive(self.ui.m_goCatTip,false)
	SetActive(self.ui.m_goItemRewardTip, false);
	SetActive(self.ui.m_goScoreTip,false)
end

function UI_KeepCatView:OnBtnFind()
    -- 在副本时切换到主营地场景
    if NetUpdatePlayerData.playerInfo.curMap ~= MAP_ID_MAIN then
        MapController:ChangeMap(MAP_ID_MAIN)
        return
    end

	--local strData = "198419|198420|198424|198423"
	local strData = ""
	local tbTempList = {}

	local listOut = MapController:GetItemByType(ItemUseType.ObjItemCatFoodHight)
	local listOut2 = MapController:GetItemByType(ItemUseType.ObjItemCatFood)

	local listOutTree = MapController:GetItemById(ItemID.CatTree2)
	local listOutTree2 = MapController:GetItemById(ItemID.CatTree)
	
	if not IsTableEmpty(listOut) or not IsTableEmpty(listOut2)	--找到猫粮
		or not IsTableEmpty(listOutTree) or not IsTableEmpty(listOutTree2) --找到猫粮矿
		then

		if not IsTableEmpty(listOut) then
			tbTempList = listOut
		elseif not IsTableEmpty(listOut2) then
			tbTempList = listOut2
		elseif not IsTableEmpty(listOutTree) then
			tbTempList = listOutTree
		elseif not IsTableEmpty(listOutTree2) then
			tbTempList = listOutTree2
		end
		
		local isFirstValue = true
		for k, v in pairs(tbTempList) do
			if not isFirstValue then
				strData = strData .. "|"
			else
				isFirstValue = false
			end

			strData = strData .. v2s(v.m_Id)
		end
		FindController.LookFindItem(1, strData)
	else
		--找不到
		local needPointAndGetRollNum = KeepCatManager:GetSettingConfigById(1).value:split("|")
		UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLangFormat(90200130, v2s(needPointAndGetRollNum[1]), LangMgr:GetLang(90200110)))
	end
	
	AudioMgr:Play(42)
end

-----------catItem

function catItem:OnInit(transform)
	self.catLineList = {}
	self.levelAnim = false
	local obj = transform
	for i = 1, 4 do
		local t = {}
		t.catObj = GetChild(obj, "catItem"..i, UE.RectTransform)
		t.catObjAnim = GetComponent(t.catObj, UE.Animation)
		t.bg = GetChild(t.catObj, "Image", UEUI.Image)
		t.catIconGray = GetChild(t.catObj, "catIcon/catgray", UEUI.Image)
		t.catIconLight = GetChild(t.catObj, "catIcon/catlight", UEUI.Image)
		t.star = GetChild(t.catObj, "catStar")
		t.progress = GetChild(t.catObj, "progress")
		t.txtPro = GetChild(t.catObj, "progress/m_slider/txtPro",UEUI.Text)
		t.pro_slider = GetChild(t.catObj, "progress/m_slider",UEUI.Slider)
		t.pro_slider_bg = GetChild(t.catObj, "progress/m_slider/sliderBg",UEUI.Image)
		t.btnItemRewar = GetChild(t.catObj, "progress/btnItemRewar",UEUI.Button)
		t.catAnim = GetChild(t.catObj,"addCatAnim",UE.Animation)
		t.animIcon = GetChild(t.catObj,"addCatAnim/animIcon",UEUI.Image)
		t.animTxt = GetChild(t.catObj,"addCatAnim/animIcon/animTxt",UEUI.Text)
		t.clickBtn = GetChild(t.catObj, "clickBtn",UEUI.Button)
		t.imgEffect = GetChild(t.catObj, "progress/imgEffect", UEUI.Image)
		t.btnItemRewarAni = GetChild(t.catObj, "progress/btnItemRewar", UE.Animation)
		table.insert(self.catLineList,t)
	end
end

function catItem:UpdateData(data,index)
	self.catListData = data
	self.index = index
	for k, v in pairs(self.catLineList) do	
		local catId = data[k].item
		SetUIImage(v.catIconGray,data[k].cat_icon_dark,true)
		SetUIImage(v.catIconLight,data[k].cat_icon_light,true)
		SetUIImage(v.bg,data[k].quality_icon,false)
		SetUIImage(v.pro_slider_bg,data[k].icon,false)
	
		local isHave = NetKeepCatData:GetCatListDataByCatId(catId)
		local catConfig = ConfigMgr:GetDataByKey(ConfigDefine.ID.cat_illustrated, "item",v2n(catId))

		if isHave then	
			SetUIImage(v.animIcon,data[k].cat_icon_light,true)
			v.animTxt.text = "x"..isHave.new_num	
			local curNum = isHave.cur_num -- isHave.new_num
			--local maxNum = KeepCatManager:GetMaxStarByCatId(catId)
			--v.txtPro.text = curNum .."/"..maxNum
			--v.pro_slider.value = curNum/maxNum
			
			--Star
			local starlv,fill,lvLast,needNum = KeepCatManager:GetCurStarByCatId(catId,curNum,true)
			self:SetStar(starlv,fill,false,k)
			self:SetProgress(starlv,fill,false,k,lvLast,needNum,catId)
			local isShow = isHave.starReward3 ~= ITEM_STATE.REWARED
			SetActive(v.btnItemRewar,isShow)--isShow and starlv > 2

			--local isGet = isHave.starReward == ITEM_STATE.CAN_REWARD;
			if self:IsShowBtnItemReward(catId) then
				v.btnItemRewarAni:Play();
				SetActive(v.imgEffect,true)
			else
				v.btnItemRewarAni:Stop();
				SetActive(v.imgEffect,false)
			end
			--SetActive(v.imgEffect, isGet and starlv > 2);
		else
			SetActive(v.btnItemRewar,false)
			SetActive(v.imgEffect, false);
		end
		if v.btnItemRewar then
			RemoveUIComponentEventCallback(v.btnItemRewar.Button)
			AddUIComponentEventCallback(v.btnItemRewar, UEUI.Button, function(arg1,arg2)
					self:ClickReward(v, v.imgEffect, catId)
				end)
		end
		if v.clickBtn then
			RemoveUIComponentEventCallback(v.clickBtn.Button)
			AddUIComponentEventCallback(v.clickBtn, UEUI.Button, function(arg1,arg2)
				UI_UPDATE(UIDefine.UI_KeepCatView,6,v.clickBtn.gameObject.transform,catId)
			end)
		end
		
		SetActive(v.progress,isHave)
		SetActive(v.star,isHave)
		SetActive(v.catIconLight,isHave)
	end
end

function catItem:IsShowBtnItemReward(catId)
	local catData = NetKeepCatData:GetCatListDataByCatId(catId)
	if catData then
		if catData.starReward1 == ITEM_STATE.CAN_REWARD or 
			catData.starReward2 == ITEM_STATE.CAN_REWARD or
			catData.starReward3 == ITEM_STATE.CAN_REWARD then
			return true
		end
	end
	return false
end

function catItem:GetCatLineShowList()
	if nil == self.catLineShow then
		self.catLineShow = {}
	end
	if table.count(self.catLineShow) < 1 then
		for i = 1, table.count(self.catLineList) do
			table.insert(self.catLineShow,{id = self.catListData[i].item,index = i})
		end
	end
	return self.catLineShow
end

function catItem:RemoveShowLine(k)
	table.remove(self.catLineShow,k)
end

function catItem:ClickReward(obj, imgEffect, catId)
	local isGet,rewardStr = NetKeepCatData:GetStarRewardByCatId(catId)
	if isGet then
		UI_UPDATE(UIDefine.UI_KeepCatView,3,obj.btnItemRewar.gameObject,rewardStr)				
		if self:IsShowBtnItemReward(catId) then
			obj.btnItemRewarAni:Play()
			SetActive(imgEffect, true)
		else
			obj.btnItemRewarAni:Stop()
			SetActive(imgEffect, false)
		end
		local catData = NetKeepCatData:GetCatListDataByCatId(catId)
		local oldNum = catData.cur_num
		local newNum = catData.new_num
		local starlv,fill,lvLast,needNum = KeepCatManager:GetCurStarByCatId(catId,oldNum+newNum, true)
		if starlv >= 3 and fill == 1 then
			SetActive(obj.btnItemRewar, false)
		end
	else
		UI_UPDATE(UIDefine.UI_KeepCatView, 7, obj.btnItemRewar.transform, catId);
	end
end

function catItem:PlayCatUpAnim()
	for k, v in pairs(self.catLineList) do
		local catData = NetKeepCatData:GetCatListDataByCatId(self.catListData[k].item)
		if catData then
			local catId = self.catListData[k].item
			local cur_num = catData.cur_num
			local new_num = catData.new_num
			local maxNum = KeepCatManager:GetMaxStarByCatId(catId)
			if new_num > 0 then
				
				local function catItemAdd()
					EffectConfig:CreateEffect(136,-80,0,0,v.catObj.transform)
					v.txtPro.text =" "--math.floor(value)
					AddDOTweenNumberDelay(cur_num/maxNum, new_num/maxNum,CatUpAnimTime, 0, function(value)
							v.pro_slider.value = value
						end)
					local oldlv = KeepCatManager:GetCurStarByCatId(catId,cur_num,true)
					local starlv,fill,lvLast,needNum = KeepCatManager:GetCurStarByCatId(catId,cur_num+new_num,true)				
					self:SetStar(starlv,fill,true,k,oldlv,catId)
					self:SetProgress(starlv,fill,true,k,lvLast,needNum,catId)
				end
				--if KeepCatManager.clickFoodType then
				local playAnim = true
				if KeepCatManager.clickFoodType then
					if self.index < 3 then
						playAnim = false
					end
				else
					if self.index > 2 then
						playAnim = false
					end
				end
				--Log.Error("xxx",catId,playAnim,self.index)
				if playAnim then
					PlayAnimStatus(v.catAnim,"catItemAdd",catItemAdd)
					PlayAnimStatus(v.catObjAnim,"catItemAdd2")
				else
					catItemAdd()
				end
			else
				local fromPos = MapController:GetUIPosByWorld(v.catIconLight.transform.position)
				self:playAddScoreTween(catId,fromPos)
			end
		end
	end
end

function catItem:SetStar(starlv,fill,isTween,index,oldlv,catId)
	for k, v in pairs(self.catLineList) do
		if index == k then
			for i = 1, 3 do
				local objName = "star"..i
				local star = GetChild(v.star,objName)
				local starImage = GetChild(star,"starFill",UEUI.Image)
				if starlv > i then
					starImage.fillAmount = 1
				elseif starlv == i then
					if isTween then
						AddDOTweenNumberDelay(starImage.fillAmount,fill,CatUpAnimTime, 0, function(value)
								starImage.fillAmount = value
								--local isHave = NetKeepCatData:GetCatListDataByCatId(catId);
								--local isShow = isHave.starReward3 ~= ITEM_STATE.REWARED
								if self:IsShowBtnItemReward(catId) then
									v.btnItemRewarAni:Play();
									SetActive(v.imgEffect,true)
								else
									v.btnItemRewarAni:Stop();
									SetActive(v.imgEffect,false)
								end
								
								--local state = isHave and isHave.starReward == ITEM_STATE.CAN_REWARD;
								
							end,function ()
								if starlv > oldlv then								
									UI_UPDATE(UIDefine.UI_KeepCatView,2,v,starlv - oldlv)
								end
								if starlv >= 3 and fill == 1 then
									local isCanPush = NetKeepCatData:IsCanPushByCatId(catId)
									if isCanPush then
										NetPushViewData:PushView(PushDefine.UI_KeepCatFull,catId)
									end
								end
							end)
					else
						starImage.fillAmount = fill
					end
				else
					starImage.fillAmount = 0
				end
			end
		end
	end
end

function catItem:SetProgress(starlv,fill,isTween,index,lvLast,needMax,catId)
	for k, v in pairs(self.catLineList) do
		if index == k then
			if isTween then
				local oldSlider = v.pro_slider.value
				if fill < v.pro_slider.value then
					oldSlider = 0
				end
				AddDOTweenNumberDelay(oldSlider, fill, CatUpAnimTime, 0, function(value)
					v.pro_slider.value = value
				end, function()
					if starlv > 2 and fill == 1 then
						v.txtPro.text = needMax .. "/" .. needMax
					else
						v.txtPro.text = lvLast .. "/" .. needMax
					end
					
					local fromPos = MapController:GetUIPosByWorld(v.catIconLight.transform.position)
					self:playAddScoreTween(catId,fromPos)
				end)
			else
				if starlv > 2 and fill == 1 then
					v.txtPro.text = needMax.."/"..needMax
				else
					v.txtPro.text = lvLast.."/"..needMax
				end
				v.pro_slider.value = fill
			end
		end
	end
end

function catItem:playAddScoreTween(catId,fromPos)
	local addValue = NetKeepCatData:GetAddScoreTweenByCatId(catId)
	if addValue and addValue ~= 0 then
		local rankPos = KeepCatManager:GetKeepCatRankPos()
		local passPortPos = KeepCatManager:GetKeepCatPassPortPos()
		if rankPos then
			UI_UPDATE(UIDefine.UI_KeepCatView,8,addValue)
            MapController:FlyUIAnim(fromPos.x, fromPos.y, 198428, 10, rankPos.x, rankPos.y)
		end
		if passPortPos then
            MapController:FlyUIAnim(fromPos.x, fromPos.y, 198428, 10, passPortPos.x, passPortPos.y)
		end
	end
end

function catItem:UpdatePosition(vec)
	self.rectTrans.anchoredPosition = vec
end

function catItem:GetAnchoredPositon()
	return self.rectTrans.anchoredPosition
end

return UI_KeepCatView