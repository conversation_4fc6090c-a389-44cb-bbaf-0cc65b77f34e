local UI_CastleHelp_2 = Class(BaseView)

local NUM_CASTLE_STAR_LV = 5

function UI_CastleHelp_2:OnInit()

end

function UI_CastleHelp_2:OnCreate(castleExcelId, lv, item)

    self.m_Level = lv
    self.item = item
    local config = ConfigMgr:GetDataByID(ConfigDefine.ID.castle, castleExcelId)
    if not config then
        Log.Error("###### UI_CastleHelp_2 id_use nil", castleExcelId)
        return
    end
    self.m_CastleConfig = config

    self.m_PrefabCell = ResMgr:LoadAssetSync("Prefab/UI/List_CastleHelp_2.prefab", AssetDefine.LoadType.Instant)

    self:LoadTableView()

    if self.item then
        local useSkinId = self.item:GetUseSkinId()
        if useSkinId and useSkinId > 0 then
            local useConfig = SkinCollectConfig:GetDataByID(useSkinId)
            SetImageSprite(self.ui.m_imgCastle,useConfig.skin_icon,true)
        else
            local mess = SkinCollectConfig:GetDefaultSkin(self.item.m_Id)
            SetImageSprite(self.ui.m_imgCastle,mess.skin_icon,true)
        end
    end
end

function UI_CastleHelp_2:OnRefresh(param)

end

function UI_CastleHelp_2:onDestroy()
    self.item = nil
end

function UI_CastleHelp_2:onUIEventClick(go, param)
    local name = go.name
    if name == "btnClose" then
        self:Close()
    end
end

function UI_CastleHelp_2:setUIIconA(itemId, trans, isShowUp, isLocked)
    if not trans then
        return
    end
    local imgIcon = GET_UI_CHILD(trans, 0, "Image")
    local imgArrow = GET_UI_CHILD(trans, 1, "Image")

    SetUIImageGray(trans, isLocked)
    local config = ItemConfig:GetDataByID(tonumber(itemId))
    if config then
        SetUIImage(imgIcon, config["icon_b"], false)
        SetUIImageGray(imgIcon, isLocked)
        SetActive(trans, true)
    else
        SetActive(trans, false)
    end
    SetActive(imgArrow, false)
end

function UI_CastleHelp_2:setUIIconB(arrInf, trans, isArrow, isLocked)
    if not trans then
        return
    end
    SetUIImageGray(trans, isLocked)
    if arrInf[2] == "0" then
        SetActive(trans, false)
    else
        local imgIcon = GET_UI_CHILD(trans, 0, "Image")
        local quaIcon = GET_UI_CHILD(trans, 1, "Image")
        local transArrow = GET_UI_CHILD(trans, 2)
        local txt = GET_UI_CHILD(trans, 3, "Text")

        local itemId = tonumber(arrInf[1])
        local config = ItemConfig:GetDataByID(itemId)
        if config then
            SetUIImage(imgIcon, config["icon_b"], false)
            SetUIImageGray(imgIcon, isLocked)
        --    SetUIImageGray(quaIcon, isLocked)
            local rarity = ItemConfig:GetZooTypeByItemID(itemId, "rarity")
            local zooRarityConfig = ConfigMgr:GetDataByID(ConfigDefine.ID.zoo_rarity, rarity)
            SetUIImage(quaIcon, zooRarityConfig["rarity_icon"], false)
        end

        txt.text = "x" .. arrInf[2]

        SetActive(trans, true)
        SetActive(transArrow, false)
    end
end

function UI_CastleHelp_2:GetCellPrefab()
    return self.m_PrefabCell
end

function UI_CastleHelp_2:CreateCell(index)
    local obj = CreateGameObject(self:GetCellPrefab())
    if obj then
        local objCell = GetAndAddComponent(obj, CS.CCTableViewCell)
        obj.name = v2s(index + 1)
        return objCell
    end
    return nil
end

function UI_CastleHelp_2:UpdateCellTrans(index, transPa, isLocked)
    local config = self.m_CastleConfig
    local idx = index + 1
    local dropId = GetValueFromStrData(config["drop"], idx)
    local listShow = GetValueFromStrData(config["des_up"], idx)
    local show_icon_inf = GetValueFromStrData(config["show_icon"], idx)
	local show_slg_icon_inf = GetValueFromStrData(config["show_icon2"], idx)
    local normalCount = GetValueFromStrData(config["drop_count"], idx) or 0

    SetUIImageGray(GetChild(transPa,"BG"), isLocked)
    for i=0, 4 do
        local imgStar = GetChild(transPa,"star/" .. i .. "/img")
        if i <= index then
            SetActive(imgStar, true)
            SetUIImageGray(imgStar, isLocked)
        else
            SetActive(imgStar, false)
        end
        SetUIImageGray(GetChild(transPa,"star/" .. i), isLocked)
    end

    local tools = DropController.new()
    local itemList = tools:GetBoxUIList(dropId)
    for i=0, 5 do
        local luaIdx = i + 1
        local imgItem = GetChild(transPa,"item/" .. i)
        self:setUIIconA(itemList[luaIdx], imgItem, string.contains(listShow, tostring(luaIdx)), isLocked)
    end

    local nowIconCount = 0
    local arrShowIcon = string.split(show_icon_inf, ';')
    for i=0, 1 do
        local luaIdx = i + 1
        local infData = string.split(arrShowIcon[luaIdx], ',')
        local transBig = GetChild(transPa, "List/other/" .. i)

        self:setUIIconB(infData, transBig, (index ~= 0) and not (index == 2 and i == 1), isLocked)
        nowIconCount = nowIconCount + tonumber(infData[2])
    end
	------SLG---
	local slg = GetChild(transPa,"List/slg")
	SetActive(slg,NetGlobalData:GetIsOpenActivityRankById(ACTIVITY_RANK_TABINDEX.LevelEnter))
	local arr = string.split(show_slg_icon_inf,",")
	local slgId = v2n(arr[1])
	local slgNum = v2n(arr[2])
	local slgIcon = GetChild(transPa,"List/slg/0/img",UEUI.Image)
	local slgBg = GetChild(transPa,"List/slg/0",UEUI.Image)
	local slgTxt = GetChild(transPa,"List/slg/0/txt",UEUI.Text)
	SetUIImage(slgIcon,ItemConfig:GetIcon(slgId),false)
	slgTxt.text = "x"..slgNum
	SetUIImageGray(slgIcon,isLocked)
	SetUIImageGray(slgBg,isLocked)
	----------------------
	
    local txt = GetChild(transPa,"txtDes",UEUI.Text)
	if NetGlobalData:GetIsOpenActivityRankById(ACTIVITY_RANK_TABINDEX.LevelEnter) then
		txt.text = LangMgr:GetLangFormat(7053, nowIconCount, normalCount,slgNum)	
	else
		txt.text = LangMgr:GetLangFormat(70000276, nowIconCount, normalCount)
	end	
   
    SetUITextGray(txt, isLocked)
	local List = GetChild(transPa,"List")
	--local horLayoutGroup = List:GetComponent(typeof(UEUI.HorizontalLayoutGroup))
	TimeMgr:CreateTimer(self, function() 
			UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(List.transform)
			 end, 0.05, 1)
	
	--SetActive(List,true)
end

function UI_CastleHelp_2:LoadTableView()
    self.ui.m_TableViewD2.GetCellCount = function(tableView)
        return NUM_CASTLE_STAR_LV
    end

    self.ui.m_TableViewD2.GetCellSize = function(tableView, index)
        if not tableView then
            return Vector2.zero
        end

        return Vector2(963, 442)
    end

    self.ui.m_TableViewD2.UpdateCell = function(tableView, index)
        if not tableView then
            return nil
        end
        local cell = tableView:GetReusableCell()
        if not cell then
            cell = self:CreateCell(index)
        end
        if cell then
            self:UpdateCellTrans(index ,cell.transform, not (self.m_Level > index))
        end
        return cell
    end
end
return UI_CastleHelp_2