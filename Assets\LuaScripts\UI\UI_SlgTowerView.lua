local UI_SlgTowerView = Class(BaseView)

local RankBox = require("UI.RankBox")
local TOWER_COUNT = 6

local ItemBg_Path1 = {
    [0] = "Sprite/ui_slg_wanjushilian/wanju_ceng2_1.png",
    [1] = "Sprite/ui_slg_wanjushilian/tanke_ceng2_1.png",
    [2] = "Sprite/ui_slg_wanjushilian/daodanche_ceng2_1.png",
    [3] = "Sprite/ui_slg_wanjushilian/feiji_ceng2_1.png",
}

local ItemBg_Path2 = {
    [0] = "Sprite/ui_slg_wanjushilian/wanju_ceng1_2.png",
    [1] = "Sprite/ui_slg_wanjushilian/tanke_ceng1_2.png",
    [2] = "Sprite/ui_slg_wanjushilian/daodanche_ceng1_2.png",
    [3] = "Sprite/ui_slg_wanjushilian/feiji_ceng1_1.png",
}

local BigBg = {
    [0] = "Texture/UI/ui_huodongjingsai_wanjushilian/wanju_bg.png",
    [1] = "Texture/UI/ui_huodongjingsai_wanjushilian/tanke_bg.png",
    [2] = "Texture/UI/ui_huodongjingsai_wanjushilian/daodanche_bg.png",
    [3] = "Texture/UI/ui_huodongjingsai_wanjushilian/feiji_bg.png",
}

local txtTitleColor = {
    [0] = "04244c",
    [1] = "004b33",
    [2] = "591227",
    [3] = "04244c",
}

function UI_SlgTowerView:OnInit()
    
end

function UI_SlgTowerView:OnCreate(param)
    self.towerList = {}
    self.isOpenBox = false
    self.towerType = param
    if not self.towerType then
        return
    end
    SetActive(self.ui.m_btnRank,true)
    self.ui.m_txtTitle.text = self:GetNameByType(self.towerType)
    
    self.towerServerData = TowerManager:GetTowerServerDataByType(self.towerType)
    
    local data_list = self:GetTowerConfigList()
    if not IsTableEmpty(data_list) then
        for i, v in ipairs(data_list) do
            self:LoadCellData(i,v)
        end
    end
    self:RefreshTowerList()
    self:RefreshLeftTopSorting()

    local maxNum = TowerManager:GetTodayMaxPassNum()
    local towerServerData = TowerManager:GetTowerServerDataByType(self.towerType)
    local passNum = maxNum - towerServerData.today_win_count
    if passNum > 0 then
        passNum = string.format("<color=#08e82c>%s</color>", passNum)
    else
        passNum = string.format("<color=#ff3232>%s</color>", passNum)
    end
    self.ui.m_txtChallenging.text = LangMgr:GetLang(70000179) .. passNum
	--BGM
	local bgm = GlobalConfig:GetNumber(10903,0)
	if bgm and bgm > 0 then
		MapController:PlayBGM(bgm)
	end
    
    local bigBgPath = BigBg[self.towerType]
    if bigBgPath then
        --SetImageSprite(self.ui.m_imgBg,bigBgPath,false)
        SetTexture(self.ui.m_rimgBg,bigBgPath,false)
    end
    
end

function UI_SlgTowerView:RefreshLeftTopSorting()
    local leftTopCanvas = GetComponent(self.ui.m_goLeftTop, TP(UE.Canvas))
    if leftTopCanvas then
        local tSortingOrder = self:GetViewSortingOrder()
        leftTopCanvas.sortingOrder = tSortingOrder + 6
    end
    local canvas = GetComponent(self.ui.m_goTopMask, TP(UE.Canvas))
    if canvas then
        canvas.overrideSorting = true
        canvas.sortingOrder = self:GetViewSortingOrder() + 5
    end    
    local rankCanvas = GetComponent(self.ui.m_btnRank, TP(UE.Canvas))
    if rankCanvas then
        rankCanvas.overrideSorting = true
        rankCanvas.sortingOrder = self:GetViewSortingOrder() + 5
    end
end

function UI_SlgTowerView:LoadCellData(index,config)
    local obj
    if self.towerList[index] == nil then
        self.towerList[index] = {}
        obj = UEGO.Instantiate(self.ui.m_goTowerItem)
        SetParent(obj,self.ui.m_transTowerList)
        SetActive(obj,true)
        obj.transform:SetLocalScale(1.0,1.0,1.0)
        self.towerList[index].go = obj
    else
        obj = self.towerList[index].go
    end
    
    self.towerList[index].config = config
    self.towerList[index].Canvas            = GetComponent(obj, TP(UE.Canvas))
    self.towerList[index].root              = GetChild(obj,"root")
    self.towerList[index].txtLevel_1        = GetChild(obj,"root/bossRoot/levelBg_1/txtLevel_1", UEUI.Text)
    self.towerList[index].txtLevel_2        = GetChild(obj,"root/bossRoot/levelBg_2/txtLevel_2", UEUI.Text)
    self.towerList[index].txtLevel_3        = GetChild(obj,"root/bossRoot/levelBg_3/txtLevel_3", UEUI.Text)
    self.towerList[index].transBossList_1   = GetChild(obj,"root/bossRoot/bossList_1",UE.RectTransform)
    self.towerList[index].transBossList_2   = GetChild(obj,"root/bossRoot/bossList_2",UE.RectTransform)
    self.towerList[index].transBossList_3   = GetChild(obj,"root/bossRoot/bossList_3",UE.RectTransform)
    self.towerList[index].imgReward         = GetChild(obj,"root/rewardRoot/btnReward/imgReward", UEUI.Image)
    self.towerList[index].rewardRoot        = GetChild(obj,"root/rewardRoot")
    
    --self.towerList[index].txtChallenging    = GetChild(obj,"root/rewardRoot/challengingRoot/txtChallenging", UEUI.Text)
    --
    --self.towerList[index].btnBattle         = GetChild(obj,"root/rewardRoot/challengingRoot/btnBattle", UEUI.Button)
    --self.towerList[index].txtBattle         = GetChild(obj,"root/rewardRoot/challengingRoot/btnBattle/txtBattle", UEUI.Text)
    self.towerList[index].txtIsPass_1       = GetChild(obj,"root/bossRoot/txtIsPass_1", UEUI.Text)
    self.towerList[index].txtIsPass_2       = GetChild(obj,"root/bossRoot/txtIsPass_2", UEUI.Text)
    self.towerList[index].txtIsPass_3       = GetChild(obj,"root/bossRoot/txtIsPass_3", UEUI.Text)

    self.towerList[index].rewardRoot        = GetChild(obj,"root/rewardRoot")
    self.towerList[index].mask_1            = GetChild(obj,"root/bossRoot/mask_1", UEUI.Image)
    self.towerList[index].mask_2            = GetChild(obj,"root/bossRoot/mask_2", UEUI.Image)
    self.towerList[index].mask_3            = GetChild(obj,"root/bossRoot/mask_3", UEUI.Image)

    self.towerList[index].btnReward         = GetChild(obj,"root/rewardRoot/btnReward", UEUI.Button)
    self.towerList[index].boxPos            = GetChild(obj,"root/rewardRoot/btnReward/boxPos")

    self.towerList[index].bg                = GetChild(obj,"root/bg",UEUI.Image)
    self.towerList[index].rewardBg          = GetChild(obj,"root/rewardBg",UEUI.Image)
    self.towerList[index].levelBg_1         = GetChild(obj,"root/bossRoot/levelBg_1")
    self.towerList[index].levelBg_2         = GetChild(obj,"root/bossRoot/levelBg_2")
    self.towerList[index].levelBg_3         = GetChild(obj,"root/bossRoot/levelBg_3")
end

function UI_SlgTowerView:RefreshTowerList()
    if IsTableEmpty(self.towerList) then
        return
    end
    local towerID = TowerManager:GetCurTowerID(self.towerType)
    local towerServerData = TowerManager:GetTowerServerDataByType(self.towerType)
    local tSortingOrder = self:GetViewSortingOrder()
    local titleColor = txtTitleColor[self.towerType]
    local curColor = "622300"
    for i, item in ipairs(self.towerList) do
        local config = item.config
        if item.Canvas then
            item.Canvas.sortingOrder = tSortingOrder + (TOWER_COUNT - i)
        end
        item.txtLevel_1.text = LangMgr:GetLangFormat(4027,config.floor)
        item.txtLevel_2.text = LangMgr:GetLangFormat(4027,config.floor) 
        item.txtLevel_3.text = LangMgr:GetLangFormat(4027,config.floor)
        --item.txtBattle.text = LangMgr:GetLang(70000004)
        item.txtIsPass_1.text = LangMgr:GetLang(70000181)
        item.txtIsPass_2.text = LangMgr:GetLang(70000181)
        item.txtIsPass_3.text = LangMgr:GetLang(70000181)
        local curTitleColor = titleColor
        SetActive(item.txtIsPass_1,false)
        SetActive(item.txtIsPass_2,false)
        SetActive(item.txtIsPass_3,false)
        SetActive(item.transBossList,true)
        SetActive(item.rewardBg,false)
        --SetActive(item.rewardRoot,false)
        SetActive(item.transBossList_1,false)
        SetActive(item.transBossList_2,false)
        SetActive(item.transBossList_3,false)
        SetActive(item.txtIsPass,false)
        
        self:RefreshBossList(item,config)
        self:RefreshBg(item)
        
        -- 正在挑战
        if config.id == towerID then
            curTitleColor = curColor
            --SetActive(item.rewardBg,true)
            --SetActive(item.rewardRoot,true)
            --local maxNum = TowerManager:GetTodayMaxPassNum()
            --local passNum = maxNum - towerServerData.today_win_count
            --if passNum > 0 then
            --    passNum = string.format("<color=#08e82c>%s</color>", passNum)
            --else
            --    passNum = string.format("<color=#ff3232>%s</color>", passNum)
            --end
            --item.txtChallenging.text = LangMgr:GetLang(70000179) .. passNum
        elseif config.id <= towerID then    
            --已通关
            SetActive(item.txtIsPass,true)
        elseif config.id > towerID then
            -- 未通关
        end
        UnifyOutline(item.txtLevel_1,curTitleColor)
        UnifyOutline(item.txtLevel_2,curTitleColor)
        UnifyOutline(item.txtLevel_3,curTitleColor)
        --RemoveUIComponentEventCallback(item.btnReward,UEUI.Button)
        --AddUIComponentEventCallback(item.btnReward,UEUI.Button,function (go,param)
        --    --self:ShowRankBox(item.btnReward,config.reward)
        --    self:onUIEventClick(go, { parent = item.boxPos,config = config })
        --end)        
        --
        --RemoveUIComponentEventCallback(item.btnBattle,UEUI.Button)
        --AddUIComponentEventCallback(item.btnBattle,UEUI.Button,function (go,param)
        --    self:onUIEventClick(go, { config = config })
        --end)
    end

    --local floor = TowerManager:GetTowerFloorById(towerID)
    --if floor > 1 then
    --    local pos = self.ui.m_transTowerList.localPosition
    --    self.ui.m_transTowerList.localPosition = Vector3(pos.x,pos.y-140,0)
    --end
end

function UI_SlgTowerView:RefreshBg(item)
    local config = item.config
    local bgPath1 = ItemBg_Path1[self.towerType]
    local bgPath2 = ItemBg_Path2[self.towerType]
    local selectBgPath1 = "Sprite/ui_slg_wanjushilian/feiji_ceng2_2.png"
    local selectBgPath2 = "Sprite/ui_slg_wanjushilian/feiji_ceng1_2.png"
    local rewardBgPath1 = "Sprite/ui_slg_wanjushilian/feiji_ceng2_2_1.png"
    local rewardBgPath2 = "Sprite/ui_slg_wanjushilian/feiji_ceng1_2_1.png"
    local rewardBgPath3 = "Sprite/ui_slg_wanjushilian/feiji_ceng1_2_2.png"
    
    local rewardBgPos1 = {730,-31}
    local rewardBgPos2 = {686,-61}
    local rewardBgPos3 = {721,-8}
    
    local rewardRootPos1 = {722,-33}
    local rewardRootPos2 = {663,-61}
    local rewardRootPos3 = {713,-4}
    
    local rewardBgPath = ""
    local bgPath = ""
    local selectBgPath = ""
    local bgScale = Vector3.New(1,1,1)
    local rewardBgPos 
    local rewardRootPos 
    local txtPass

    SetActive(item.txtIsPass,false)
    SetActive(item.levelBg_1,false)
    SetActive(item.levelBg_2,false)
    SetActive(item.levelBg_3,false)
    SetActive(item.mask_1,false)
    SetActive(item.mask_2,false)
    SetActive(item.mask_3,false)
    
    local cur_mask 
    local floor = config.floor
    local curFloorMod = floor % 3
    if curFloorMod == 1 then
        bgPath = bgPath1
        selectBgPath = selectBgPath1
        rewardBgPath = rewardBgPath1
        cur_mask = item.mask_1
        SetActive(item.levelBg_1,true)
        rewardBgPos = rewardBgPos1
        rewardRootPos = rewardRootPos1
        item.root.transform.localPosition = Vector3(0,0,0)
        --item.rewardRoot.transform.localRotation = Quaternion.Euler(0,0,0)
        txtPass = item.txtIsPass_1
    elseif curFloorMod == 0 then
        bgPath = bgPath2
        selectBgPath = selectBgPath2
        rewardBgPath = rewardBgPath3
        bgScale = Vector3.New(-1,1,1)
        cur_mask = item.mask_3
        SetActive(item.levelBg_3,true)
        rewardBgPos = rewardBgPos3
        rewardRootPos = rewardRootPos3
        item.root.transform.localPosition = Vector3(50,0,0)
        --item.rewardRoot.transform.localRotation = Quaternion.Euler(0,0,5)
        txtPass = item.txtIsPass_3
    elseif curFloorMod == 2 then
        bgPath = bgPath2
        selectBgPath = selectBgPath2
        rewardBgPath = rewardBgPath2
        cur_mask = item.mask_2
        SetActive(item.levelBg_2,true)
        rewardBgPos = rewardBgPos2
        rewardRootPos = rewardRootPos2
        item.root.transform.localPosition = Vector3(-50,0,0)
        --item.rewardRoot.transform.localRotation = Quaternion.Euler(0,0,-2)
        txtPass = item.txtIsPass_2
    end
    item.bg.transform.localScale = bgScale
    --item.rewardBg.transform.localPosition = Vector3.New(rewardBgPos[1],rewardBgPos[2],0)
    --item.rewardRoot.transform.localPosition = Vector3.New(rewardRootPos[1],rewardRootPos[2],0)
    --SetImageSprite(item.rewardBg,rewardBgPath,true)

    if curFloorMod == 2 then
        SetUIFirstSibling(item.bg)
    end
    
    local towerID = TowerManager:GetCurTowerID(self.towerType)
    -- 正在挑战
    if config.id == towerID then
        SetImageSprite(item.bg,selectBgPath,false)
        
    elseif config.id <= towerID then
        --已通关
        SetImageSprite(item.bg,bgPath,false)
        SetActive(cur_mask,true)
        SetActive(txtPass,true)
    elseif config.id > towerID then
        -- 未通关
        SetImageSprite(item.bg,bgPath,false)
        SetActive(cur_mask,true)
    end
end

function UI_SlgTowerView:RefreshBossList(item,config)
    local parent
    local floor = config.floor
    local curFloorMod = floor % 3
    if curFloorMod == 1 then
        parent = item.transBossList_1
    elseif curFloorMod == 0 then
        parent = item.transBossList_3
    elseif curFloorMod == 2 then
    
        parent = item.transBossList_2
    end
    if parent == nil then
        return
    end
    local towerID = TowerManager:GetCurTowerID(self.towerType)
    -- 正在挑战
    if config.id == towerID or config.id > towerID then
        SetActive(parent,true)
    end
    local groupId = v2n(config.monster_group)
    local monsterList = TowerManager:GetMonsterListById(groupId)
    for i, v in ipairs(monsterList) do
        if item.bossList == nil then
            item.bossList = {}
        end
        local obj
        if item.bossList[i] == nil then
            item.bossList[i] = {}
            obj = UEGO.Instantiate(self.ui.m_goBossItem)
            item.bossList[i].go = obj
        else
            obj = item.bossList[i].go
        end
        SetParent(obj,parent)
        SetActive(obj,true)
        obj.transform:SetLocalScale(1.0,1.0,1.0)

        local bossIcon          = GetChild(obj,"bossIcon",      UEUI.Image)
        local txtBossLevel      = GetChild(obj,"txtBg/txtBossLevel",     UEUI.Text)
        local btnBoss           = GetChild(obj,"bossIcon",  UEUI.Button)

        local monsterConfig = HeroManager:GetHeroConfigById(v.monsterId)
        local monsterVo = HeroModule.new(v.monsterId, monsterConfig)
        monsterVo:initData()
        monsterVo:SetHeroValueByKey("isMonster", true)
        local level = v.monsterLevel
        local star = v.monsterStar
        local atk = v.monsterAtk
        local hp = v.monsterHp
        local def = v.monsterDef
        monsterVo:SetHeroValueByKey("level", level)
        monsterVo:SetHeroValueByKey("starLv", star)
        monsterVo:SetHeroValueByKey("atk", atk)
        monsterVo:SetHeroValueByKey("hp", hp)
        monsterVo:SetHeroValueByKey("def", def)
        monsterVo:SetHeroValueByKey("power", v.monsterFight)

        if monsterConfig.resources_jingtai then
            SetImageSprite(bossIcon,monsterConfig.resources_jingtai,true)
        end
        
        --正在挑战
        local isChallenge = config.id == towerID  
        local levelTextColor = isChallenge and "540800" or "123662"
        local levelStr = "Lv."..level
        txtBossLevel.text = levelStr
        UnifyOutline(txtBossLevel,levelTextColor)
        
        RemoveUIComponentEventCallback(btnBoss, UEUI.Button)
        AddUIComponentEventCallback(btnBoss, UEUI.Button, function (go, param)
            UI_SHOW(UIDefine.UI_SlgHeroDetailView,monsterVo)
        end)
    end
end

function UI_SlgTowerView:GetTowerConfigList()
    local data_list = {}
    local allConfig = TowerManager:GetTowerListByType(self.towerType)
    local towerID = TowerManager:GetCurTowerID(self.towerType)
    local floor = TowerManager:GetTowerFloorById(towerID)
    -- 全部通关
    if floor == -1 then
        floor = TowerManager:GetTowerFloorById(towerID - 1)
    end
    local startFloor = floor
    if floor > 1 then
        startFloor = startFloor - 1
    end
    local count = 0
    for i, v in ipairs(allConfig) do
        if v.floor >= startFloor then
            count = count + 1
            if count <= TOWER_COUNT then
                table.insert(data_list,v)
            else
                break
            end
        end
    end
    local dataCount = table.count(data_list)
    if dataCount < 4 then
        local addCount = 4 - dataCount
        for i = (#allConfig - dataCount), 1,-1 do
            local config = allConfig[i]
            table.insert(data_list,config)
            addCount = addCount - 1
            if addCount <= 0 then
                break
            end
        end
    end
    table.sort(data_list, function(a, b)
        return a.id > b.id
    end)
    return data_list
end

function UI_SlgTowerView:GetNameByType(towerType)
    local langID = 0
    if towerType == TOWER_TYPE.Plane then
        langID = 70000175
    elseif towerType == TOWER_TYPE.Tank then
        langID = 70000173
    elseif towerType == TOWER_TYPE.Missile then
        langID = 70000174
    else
        langID = 70000176
    end
    local name = ""
    if langID ~= 0 then
        name = LangMgr:GetLang(langID)
    end
    return name
end

--- 初始化排名宝箱奖励列表
function UI_SlgTowerView:InitRankBox()
    if self.rankBox == nil then
        self.rankBox = RankBox.new(self.uiGameObject)
        self.rankBox:InitUI(5)
    end
    if self.rankBox then
        local sortingOrder = self:GetViewSortingOrder()
        self.rankBox:SetCanvasOrder(sortingOrder+6)
    end
end

function UI_SlgTowerView:ShowRankBox(obj, rewardStr)
    self:InitRankBox()
    -- 获取定位物体
    local rewardPosObj = obj
    if not rewardPosObj then return end
    if self.isOpenBox then
        self:CloseRankBox()
        return
    end
    self.isOpenBox = true
    -- 刷新奖励列表
    self.rankBox:UpdateItem(rewardPosObj, rewardStr)
    self:CheckRankBoxOverScreen(rewardPosObj)
    self:MoveRankBoxArrow(rewardPosObj)
end

--- 移动排名宝箱奖励列表的底部箭头
--- @param rewardPosObj any 定位物体
function UI_SlgTowerView:MoveRankBoxArrow(rewardPosObj)
    local downArrow = GetChild(self.rankBox.BoxGo, "Image")
    if not downArrow then return end
    -- 底部箭头向宝箱位置偏移
    local pos = rewardPosObj.transform.localPosition
    local downArrowPos = downArrow.transform.localPosition
    downArrow.transform.localPosition = Vector3.New(-pos.x, downArrowPos.y, downArrowPos.z)
end

--- 检查排名宝箱奖励列表是否超出屏幕边缘
--- @param rewardPosObj any 定位物体
function UI_SlgTowerView:CheckRankBoxOverScreen(rewardPosObj)
    local canvas = UIMgr:GetCanvasRectTrans()
    local rankBoxBg = GetChild(self.rankBox.BoxGo, "bg", UE.RectTransform)
    if not rankBoxBg then return end
    UEUI.LayoutRebuilder.ForceRebuildLayoutImmediate(rankBoxBg)
    -- 右侧边缘位置
    local edgePos = canvas.rect.width / 2 - rankBoxBg.rect.width / 2
    -- 奖励列表当前位置
    local rewardPos = UIMgr:GetUIPosByWorld(rewardPosObj.transform.position)
    -- 奖励列表当前位置超过了右侧边缘位置，需要往左边移动
    if rewardPos.x > edgePos then
        -- 超出的差值
        local diff = rewardPos.x - edgePos
        local pos = rewardPosObj.transform.localPosition
        -- 定位物体的新位置
        local space = 10
        pos.x = pos.x - diff - space
        rewardPosObj.transform.localPosition = pos
        -- 根据定位物体重新设置奖励列表的位置
        local targetPos = UIMgr:GetUIPosByWorld(rewardPosObj.transform.position)
        self.rankBox.BoxGo.transform.localPosition = targetPos
    end
end

--- 关闭排名宝箱奖励列表
function UI_SlgTowerView:CloseRankBox()
    if self.rankBox then
        if self.isOpenBox then
            self.rankBox:Close()
            self.isOpenBox = false
        end
    end
end

function UI_SlgTowerView:DestoryRankBox()
    if self.rankBox then
        self.rankBox:Destory()
        self.rankBox = nil
    end
end

function UI_SlgTowerView:OnRefresh(param)
    if param == 1 then
        local data_list = self:GetTowerConfigList()
        if not IsTableEmpty(data_list) then
            for index, v in ipairs(self.towerList) do
                local config = data_list[index]
                if config then
                    self.towerList[index].config = config
                else
                    local obj = self.towerList[index].go
                    SetActive(obj,false)
                end
            end
        end
        self:RefreshTowerList()
        self:RefreshLeftTopSorting()
    end
end

function UI_SlgTowerView:onDestroy()
    self:DestoryRankBox()
    if not IsTableEmpty(self.towerList) then
        for k, v in ipairs(self.towerList) do
            UEGO.Destroy(v.go)
        end
        self.showRewardList = {}
    end

	--BGM
	local bgm = GlobalConfig:GetNumber(10903,0)
	if bgm and bgm > 0 then
		MapController:PlayBGM()
	end
end

function UI_SlgTowerView:onUIEventClick(go,param)
    local name = go.name
    if name ~= "btnReward" then
        self:CloseRankBox()
    end
    
    if name == "m_btnBack" then
        self:Close()
    elseif name == "m_btnReward" then

        local towerID = TowerManager:GetCurTowerID(self.towerType)
        local config = TowerManager:GetTowerConfigById(towerID)
        local parent = self.ui.m_goBoxPos
        --self:ShowRankBox(parent,config.reward)
        UI_SHOW(UIDefine.UI_SlgTowerRewardTips,self.towerType)
    elseif name == "m_btnBattle" then
        local teamType = BATTLE_TEAM_TYPE.TOWER
        if self.towerType == TOWER_TYPE.Plane then
            teamType = BATTLE_TEAM_TYPE.TOWER_AIRPLANE
        elseif self.towerType == TOWER_TYPE.Tank then
            teamType = BATTLE_TEAM_TYPE.TOWER_TANK
        elseif self.towerType == TOWER_TYPE.Missile then
            teamType = BATTLE_TEAM_TYPE.TOWER_MISSILE
        end
        local isOpen = TowerManager:GetTodayIsOpenByKind(self.towerType)
        if not isOpen then
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000206))
            return
        end
        local maxNum = TowerManager:GetTodayMaxPassNum()
        local towerServerData = TowerManager:GetTowerServerDataByType(self.towerType)
        local passNum = maxNum - towerServerData.today_win_count
        if passNum <= 0 then
            UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000207))
            return
        end

        local towerID = TowerManager:GetCurTowerID(self.towerType)
        local config = TowerManager:GetTowerConfigById(towerID)
        local towerId = config.id
        
		BattleSceneManager:SetTeamType(teamType)
		BattleSceneManager:SetTowerData({["tower_id"] = towerId,["tower_type"] = self.towerType})
		BattleSceneManager:ChangeSceneType(BATTLE_SCENE_TYPE.CHOOSE)
    elseif name == "m_btnRank" then
        UI_SHOW(UIDefine.UI_SlgTowerRank, { tabType = self.towerType, subTabType = TOWER_RANK_TYPE.World })
    end
end

return UI_SlgTowerView