local UI_SlgTowerClimbingView = Class(BaseView)

local Week_Lang = {
    [1] = 6007904,
    [2] = 6007905,
    [3] = 6007906,
    [4] = 6007907,
    [5] = 6007908,
    [6] = 6007909,
    [7] = 6007910,
}

function UI_SlgTowerClimbingView:OnInit()
    
end

function UI_SlgTowerClimbingView:OnCreate(param)
    SetActive(self.ui.m_btnRank,true)
    local data_list = self:GetRefreshTowerList()
    for i, v in pairs(data_list) do
        self:RefreshTower(v)
    end

    local BattleEnterItem = require("UI.BattleEnterItem");
    local obj = GetChild(self.uiGameObject, "root/BattleEnter");
    self.battleEnterItem = BattleEnterItem.new(obj, 200, function()
        UI_CLOSE(UIDefine.UI_ActivityRankCenter);
    end);
    self.battleEnterItem:OnInit();
end

function UI_SlgTowerClimbingView:GetRefreshTowerList()
    local data_list = {
        TOWER_TYPE.All,
        TOWER_TYPE.Plane,
        TOWER_TYPE.Tank,
        TOWER_TYPE.Missile,
    }
    return data_list
end

function UI_SlgTowerClimbingView:RefreshTower(towerType)
    local isOpen,openDayList = TowerManager:GetTodayIsOpenByKind(towerType)
    local towerServerData = TowerManager:GetTowerServerDataByType(towerType)
    local bgImgStr = "m_imgKindItem_" .. towerType
    local leftImgStr = "m_imgKindItemLeft_" .. towerType
    local rightImgStr = "m_imgKindItemRight_" .. towerType
    local titleImgStr = "m_imgKindItemTitle_" .. towerType
    local txtTitleStr = "m_txtKindItemlTitle_" .. towerType
    local typeImgStr = "m_imgKindItemType_" .. towerType
    local txtNameStr = "m_txtKindItemName_" .. towerType
    local txtNotOpenNameStr = "m_txtKindItemNameNoOpen_" .. towerType
    local txtCanPassStr = "m_txtItemCanPassNum_" .. towerType
    local openEffStr = "m_goOpenEff_" .. towerType
    
    local bgImg = self.ui[bgImgStr]
    local leftImg = self.ui[leftImgStr]
    local rightImg = self.ui[rightImgStr]
    local titleImg = self.ui[titleImgStr]
    local txtTitle = self.ui[txtTitleStr]
    local typeImg = self.ui[typeImgStr]
    local txtName = self.ui[txtNameStr]
    local txtNotOpenName = self.ui[txtNotOpenNameStr]
    local txtCanPass = self.ui[txtCanPassStr]
    local openEff = self.ui[openEffStr]
    
    local bgPath,titlePath = self:GetImagePath(isOpen)
    SetImageSprite(bgImg,bgPath,false)
    SetImageSprite(titleImg,titlePath,false)
    SetActive(leftImg,false)
    SetActive(rightImg,false)

    local level = 1
    local isMax = false;
    if towerServerData then
        isMax = TowerManager:GetIsMaxTower(towerType,towerServerData.id)
        local maxId = TowerManager:GetIsMaxTowerId(towerType)
        local towerId = isMax and maxId or towerServerData.id
        level = TowerManager:GetTowerFloorById(towerId)
    end
    
    local name = self:GetNameByType(towerType)
    txtName.text = name
    txtNotOpenName.text = name
    
    local typeImgPath = self:GetKindImagePath(isOpen,towerType)
    SetImageSprite(typeImg,typeImgPath,true)
    local titleStr = LangMgr:GetLangFormat(4027,level)
    if isOpen then
        local maxNum = TowerManager:GetTodayMaxPassNum()
        local toDayWinCount = 0
        if towerServerData then
            toDayWinCount = towerServerData.today_win_count
        end
        local passNum = maxNum - toDayWinCount
        if passNum > 0 then
            passNum = string.format("<color=#08e82c>%s</color>", passNum)
        else
            passNum = string.format("<color=#ff3232>%s</color>", passNum)
        end
        txtCanPass.text = LangMgr:GetLang(70000179) .. passNum
        titleStr = string.format("<color=#004872>%s</color>", titleStr)
    else
        local openStr = ""
        for i, v in ipairs(openDayList) do
            local day = LangMgr:GetLang(Week_Lang[v])
            if i ~= #openDayList then
                openStr = openStr .. day .. "/"
            else
                openStr = openStr .. day
            end
        end
        --openStr = string.format("<color=#08e82c>%s</color>", openStr)
        txtCanPass.text = LangMgr:GetLangFormat(70000177,openStr)
        titleStr = string.format("<color=#363C66>%s</color>", titleStr)
    end
    if isMax then
        txtCanPass.text = LangMgr:GetLang(70000015)
    end
    txtTitle.text = titleStr
    SetActive(txtName,isOpen)
    SetActive(openEff,isOpen)
    SetActive(txtNotOpenName,not isOpen)
end

function UI_SlgTowerClimbingView:GetImagePath(isOpen)
    local bgPath = "Sprite/ui_huodongjingsai/wanjushilian_dikuang1.png"
    local titlePath = "Sprite/ui_huodongjingsai/wanjushilian_dikuang1_1.png"
    if not isOpen then
        bgPath = "Sprite/ui_huodongjingsai/wanjushilian_dikuang2.png"
        titlePath = "Sprite/ui_huodongjingsai/wanjushilian_dikuang2_1.png"
    end
    return bgPath,titlePath
end

function UI_SlgTowerClimbingView:GetKindImagePath(isOpen,towerType)
    local path = "Sprite/ui_huodongjingsai/wanjushilian_icon_all_1.png"
    if isOpen then
        if towerType == TOWER_TYPE.Plane then
            path = "Sprite/ui_huodongjingsai/wanjushilian_icon_feiji_1.png"
        elseif towerType == TOWER_TYPE.Tank then
            path = "Sprite/ui_huodongjingsai/wanjushilian_icon_tanke_1.png"
        elseif towerType == TOWER_TYPE.Missile then
            path = "Sprite/ui_huodongjingsai/wanjushilian_icon_daodanche_1.png"
        end
    else
        if towerType == TOWER_TYPE.Plane then
            path = "Sprite/ui_huodongjingsai/wanjushilian_icon_feiji_2.png"
        elseif towerType == TOWER_TYPE.Tank then
            path = "Sprite/ui_huodongjingsai/wanjushilian_icon_tanke_2.png"
        elseif towerType == TOWER_TYPE.Missile then
            path = "Sprite/ui_huodongjingsai/wanjushilian_icon_daodanche_2.png"
        else
            path = "Sprite/ui_huodongjingsai/wanjushilian_icon_all_2.png"
        end
    end
    return path
end

function UI_SlgTowerClimbingView:GetNameByType(towerType)
    local langID = 0
    if towerType == TOWER_TYPE.Plane then
        langID = 70000175
    elseif towerType == TOWER_TYPE.Tank then
        langID = 70000173
    elseif towerType == TOWER_TYPE.Missile then
        langID = 70000174
    else
        langID = 70000176
    end
    local name = ""
    if langID ~= 0 then
        name = LangMgr:GetLang(langID)
    end
    return name
end

function UI_SlgTowerClimbingView:OnRefresh(param)
    if param == 1 then
        local data_list = self:GetRefreshTowerList()
        for i, v in pairs(data_list) do
            self:RefreshTower(v)
        end
    end
end

function UI_SlgTowerClimbingView:onDestroy()
    if self.battleEnterItem then
        self.battleEnterItem:OnDestroy();
        self.battleEnterItem = nil;
    end
end

function UI_SlgTowerClimbingView:CheckIsOpenByType(towerType)
    local isOpen,openDayList = TowerManager:GetTodayIsOpenByKind(towerType)
    if not isOpen then
        UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000206))
        return false
    end
    local towerServerData = TowerManager:GetTowerServerDataByType(towerType)
    local floor = TowerManager:GetTowerFloorById(towerServerData.id)
    local maxFloor = TowerManager:GetMaxTowerFloor(towerType)
    if floor == -1 then
        UI_SHOW(UIDefine.UI_WidgetTip, LangMgr:GetLang(70000207))
        return false
    end
    return true
end

function UI_SlgTowerClimbingView:onUIEventClick(go,param)
    local name = go.name
    if name == "m_btnKindItem_0" then
        local isCanOpen = self:CheckIsOpenByType(TOWER_TYPE.All)
        if isCanOpen then
            UI_SHOW(UIDefine.UI_SlgTowerView,TOWER_TYPE.All)
        end
    elseif name == "m_btnKindItem_3" then
        local isCanOpen = self:CheckIsOpenByType(TOWER_TYPE.Plane)
        if isCanOpen then
            UI_SHOW(UIDefine.UI_SlgTowerView,TOWER_TYPE.Plane)
        end 
    elseif name == "m_btnKindItem_1" then
        local isCanOpen = self:CheckIsOpenByType(TOWER_TYPE.Tank)
        if isCanOpen then
            UI_SHOW(UIDefine.UI_SlgTowerView,TOWER_TYPE.Tank)
        end 
    elseif name == "m_btnKindItem_2" then
        local isCanOpen = self:CheckIsOpenByType(TOWER_TYPE.Missile)
        if isCanOpen then
            UI_SHOW(UIDefine.UI_SlgTowerView,TOWER_TYPE.Missile)
        end
    elseif name == "m_btnRank" then
        UI_SHOW(UIDefine.UI_SlgTowerRank, { tabType = TOWER_TYPE.All, subTabType = TOWER_RANK_TYPE.World })
    elseif name == "m_btnHelp" then
        local content = LangMgr:GetLang(70000184)
        UI_SHOW(UIDefine.UI_SlgHelpTip,content)
        --ChatManager:testTrainRobShare()
        --BattleSceneManager:SetTeamType(BATTLE_TEAM_TYPE.TOPFIGHT_BATTELE_TEAM1)
        ----BattleSceneManager:SetTradeTrainDefendData({["trainIndex"] = 2})
        --BattleSceneManager:ChangeSceneType(BATTLE_SCENE_TYPE.CHOOSE)
        --BattlePassManager:PopTaskCompleteUI(34100101)
    end
end

return UI_SlgTowerClimbingView