using System;
using System.IO;
using System.Text;
using Network.Message;
using UnityEngine;
using UnityWebSocket;
using WeChatWASM;
using ErrorEventArgs = UnityWebSocket.ErrorEventArgs;

public class WebTCPSession
{
    public long ConnID => m_ConnID;

    protected long m_ConnID;
    protected WXTCPSocket m_Socket;

    protected Action<int, long, int, byte[]> m_LuaCallback;

    protected MessageStream m_sendbuf = new MessageStream();
    protected MessageStream m_recvbuf = new MessageStream();

    private bool m_IsConnecting = false;

    private string host;
    private int port;
    
    public WebTCPSession(long id, string host,int port, Action<int, long, int, byte[]> luaCallback)
    {
        this.host = host;
        this.port = port;
        
        m_ConnID = id;
        m_LuaCallback = luaCallback;
        m_Socket = WX.CreateTCPSocket();
        m_Socket.OnConnect(res =>
        {
            m_IsConnecting = true;
            Socket_OnOpen(res);
        });
        m_Socket.OnMessage(res =>
        {
            Socket_OnMessage(res);
        });
        m_Socket.OnClose(res =>
        {
            Socket_OnClose(res);
        });
        m_Socket.OnError(res =>
        {
            m_IsConnecting = false;
            Socket_OnError(res);
        });
    }

    public void ConnectAsync()
    {
        m_Socket?.Connect(new TCPSocketConnectOption()
        {
            address = this.host,
            port = this.port
        });
    }

    public virtual void SendMessage(int iProtoID, byte[] msg)
    {
        Debug.LogError(m_ConnID + " Web_TCP_SendMessage:" + iProtoID + "_" + msg.Length);
        
        ushort uLen = (ushort)msg.Length;
        uLen += 4;
        ushort uProtoID = (ushort)(iProtoID);

        m_sendbuf.push_ushort(uLen);
        m_sendbuf.push_ushort(uProtoID);
        m_sendbuf.push_bytes(msg);
        
        m_Socket?.Write(m_sendbuf.getBytes());

        m_sendbuf.erase(0, m_sendbuf.size());
    }

    public bool IsConnected()
    {
        return m_IsConnecting;
    }

    public void Disconnect()
    {
        m_Socket?.Close();
    }


    private void Socket_OnOpen(GeneralCallbackResult result)
    {
        Debug.LogError(m_ConnID+" Web_TCP_OnOpen:"+result.errMsg);
        m_LuaCallback?.Invoke(4, m_ConnID, 0, null);
    }

    protected virtual void Socket_OnMessage(TCPSocketOnMessageListenerResult result)
    {
        Debug.LogError(m_ConnID+" Web_TCP_OnMessage:"+result.message.Length);
        m_recvbuf.clear();
        m_recvbuf.insert(m_recvbuf.size(), result.message, 0, result.message.Length);
        ushort uLen = m_recvbuf.pull_ushort();
        ushort uProtoID = m_recvbuf.pull_ushort();
        int iPacketLen = uLen - 4;
        if (m_recvbuf.size() >= uLen)
        {
            byte[] msg = m_recvbuf.pull_bytes(iPacketLen);
            m_recvbuf.erase(0,uLen);
            m_recvbuf.reset_pos();
            m_LuaCallback?.Invoke(8, m_ConnID, uProtoID, msg);
        }
        else
        {
            Debug.LogError(m_ConnID+" Socket_OnMessage data size error" + m_recvbuf.size() + "<" + uLen);
        }
    }

    private void Socket_OnClose(GeneralCallbackResult result)
    {
        Debug.LogError(m_ConnID+" Web_TCP_OnClose:"+result.errMsg);
        m_LuaCallback?.Invoke(6, m_ConnID, 0, null);
    }

    private void Socket_OnError(GeneralCallbackResult result)
    {
        Debug.LogError(m_ConnID+" Web_TCP_OnError:"+result.errMsg);
        m_LuaCallback?.Invoke(5, m_ConnID, 0, null);
    }
}
