local UI_TopFightDetail = Class(BaseView)
local GoSlgHeroItem = require("UI.GoSlgHeroItem")

-- 1 左边胜利 2 右边胜利
local BgIcon = {
    [1] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_duorenjiesuan_zhanbao_dikuang2.png",
    [2] = "Sprite/ui_huodongjingsai_maoyihuoyun/maoyiyunhuo_duorenjiesuan_zhanbao_dikuang1.png"
}

function UI_TopFightDetail:OnInit()
    
end

function UI_TopFightDetail:OnCreate(data)
    self.data = data
    if self.data == nil then
        return
    end
    self.goTeamList = {
        [1] = self.ui.m_goTeam1,
        [2] = self.ui.m_goTeam2,
        [3] = self.ui.m_goTeam3
    }
    -- 创建头像
    local headLeft = GetChild(self.ui.m_goHeadLeft, "head")
    self.headNodeLeft = CreateCommonHead(headLeft, 0.91)
    SetMyHeadAndBorderByGo(self.headNodeLeft)

    local headRight = GetChild(self.ui.m_goHeadRight, "head")
    self.headNodeRight = CreateCommonHead(headRight, 0.91)
    SetMyHeadAndBorderByGo(self.headNodeRight)

    if not self.isInit then
        self.isInit = true
        self.heroItemList = {}
        self.heroItemList[1] = {}
        self.heroItemList[2] = {}
        for i = 1, 3, 1 do
            self.heroItemList[1][i] = {}
            self.heroItemList[2][i] = {}
        end
        self:InitHero()
    end
    
    self:RefreshLeftResult()
    self:RefreshRightResult()
    self:RefreshPanelByBattleResult()
    
    local leftTeams = {}
    local rightTeams = {}
    for i = 1, 3 do
        local team = self.data.list[i]
        if team then
            table.insert(leftTeams,team.attacker)
            table.insert(rightTeams,team.defender)
        end
    end
    self:RefreshTeamLeft(leftTeams)
    self:RefreshTeamRight(rightTeams)
end

function UI_TopFightDetail:RefreshLeftResult()
    local leftPlayerInfo = self.data.info.attacker
    local isWin = self.data.info.result == 1
    SetActive(self.ui.m_goWinLeft,isWin)
    SetActive(self.ui.m_goFailLeft,not isWin)
    local player = leftPlayerInfo.player
    local league = leftPlayerInfo.league
    if player then
        SetHeadAndBorderByGo(self.headNodeLeft, player.icon, player.border, function ()
            if player and not IsNilOrEmpty(player.id) then
                FriendManager:ShowPlayerById(player.id)
            end
        end)
    end
    self:RefreshPlayer(self.ui.m_goHeadLeft, player, league)
end

function UI_TopFightDetail:RefreshRightResult()
    local PlayerInfo = self.data.info.defender
    local isWin = self.data.info.result == 2
    SetActive(self.ui.m_goWinRight,isWin)
    SetActive(self.ui.m_goFailRight,not isWin)
    local player = PlayerInfo.player
    local league = PlayerInfo.league
    if player then
        SetHeadAndBorderByGo(self.headNodeRight, player.icon, player.border, function ()
            if player and not IsNilOrEmpty(player.id) then
                FriendManager:ShowPlayerById(player.id)
            end
        end)
    end
    self:RefreshPlayer(self.ui.m_goHeadRight, player, league)
end

function UI_TopFightDetail:RefreshPlayer(parent, player, league)
    local txtName = GetChild(parent, "txtName", UEUI.Text)
    local txtLeague = GetChild(parent, "txtLeague", UEUI.Text)
    local imgLeagueIcon = GetChild(parent, "txtLeague/imgLeagueIcon", UEUI.Image)
    --local txtFight = GetChild(parent, "txtFight", UEUI.Text)

    if league then
        if not IsNilOrEmpty(league.icon) then
            SetImageSprite(imgLeagueIcon, LeagueManager:GetUnionImageById(league.icon), false)
            SetActive(imgLeagueIcon, true)

            if league.icon == 0 then
                SetActive(imgLeagueIcon, false)
            end
        else
            SetActive(imgLeagueIcon, false)
        end
        txtLeague.text = league.name
        SetActive(txtLeague, true)
    else
        SetActive(imgLeagueIcon, false)
        SetActive(txtLeague, false)
    end

    if player then
        local playerInfo = NetUpdatePlayerData:GetPlayerInfo()
        if playerInfo.id == player.id then
            txtName.text = playerInfo.name .. LangMgr:GetLang(70000493)
            local myLeagueDetails = LeagueManager:GetMyLeagueDetails()
            local myLeague = {
                name = myLeagueDetails.name,
                icon = myLeagueDetails.pic,
            }
            if myLeague then
                if not IsNilOrEmpty(myLeague.icon) then
                    SetImageSprite(imgLeagueIcon, LeagueManager:GetUnionImageById(myLeague.icon), false)
                    SetActive(imgLeagueIcon, true)
                    if myLeague.icon == 0 then
                        SetActive(imgLeagueIcon, false)
                    end
                else
                    SetActive(imgLeagueIcon, false)
                end
                txtLeague.text = myLeague.name
                SetActive(txtLeague, true)
            else
                SetActive(imgLeagueIcon, false)
                SetActive(txtLeague, false)
            end
        else
            txtName.text = player.name
            --txtFight.text = NumToGameString(player.power)
        end
    end
end

--- 初始化英雄
function UI_TopFightDetail:InitHero()
    local left1 = GetChild(self.ui.m_goTeam1, "teamLeft")
    local right1 = GetChild(self.ui.m_goTeam1, "teamRight")
    self:GenerateHero(left1, 1, 1)
    self:GenerateHero(right1, 2, 1)

    local left2 = GetChild(self.ui.m_goTeam2, "teamLeft")
    local right2 = GetChild(self.ui.m_goTeam2, "teamRight")
    self:GenerateHero(left2, 1, 2)
    self:GenerateHero(right2, 2, 2)

    local left3 = GetChild(self.ui.m_goTeam3, "teamLeft")
    local right3 = GetChild(self.ui.m_goTeam3, "teamRight")
    self:GenerateHero(left3, 1, 3)
    self:GenerateHero(right3, 2, 3)
end

--- 生成英雄节点
--- @param parent any 父节点
function UI_TopFightDetail:GenerateHero(parent, type, index)
    local heroParent = GetChild(parent, "hero")
    local item1 = GetChild(heroParent, "row1/item1")
    local item2 = GetChild(heroParent, "row1/item2")
    local item3 = GetChild(heroParent, "row2/item3")
    local item4 = GetChild(heroParent, "row2/item4")
    local item5 = GetChild(heroParent, "row2/item5")
    local itemList = { item1, item2, item3, item4, item5 }

    for _, value in ipairs(itemList) do
        local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_hero, 1001)
        if config then
            local hero = HeroModule.new(1001, config)
            hero:SetHeroValueByKey("level", 1)
            hero:SetHeroValueByKey("starLv", 1)
            local heroGo = GoSlgHeroItem:Create(value, hero)
            heroGo:SetIsNeedShowSelect(false)
            heroGo:SetItem()
            heroGo:SetScale(0.48, 0.48)
            table.insert(self.heroItemList[type][index], heroGo)
        end
    end
end

--- 刷新左边的队伍
function UI_TopFightDetail:RefreshTeamLeft(teams)
    local teamLeft1 = GetChild(self.ui.m_goTeam1, "teamLeft")
    local teamLeft2 = GetChild(self.ui.m_goTeam2, "teamLeft")
    local teamLeft3 = GetChild(self.ui.m_goTeam3, "teamLeft")

    -- 按照队伍类型排序
    table.sort(teams, function (a, b)
        if a.team_type ~= b.team_type then
            return a.team_type < b.team_type
        end
        return false
    end)

    if IsTableEmpty(self.teamDataList) then
        self.teamDataList = {}
    end

    for _, value in ipairs(self.heroItemList[1]) do
        for _, item in ipairs(value) do
            SetActive(item.go, false)
        end
    end

    if teams[1] then
        local teamDataLeft1 = {
            index = 1,
            power = self:ComputeTeamPower(teams[1]),
            heroes = teams[1].heroes
        }
        self.teamDataList[1] = teamDataLeft1
        self:RefreshTeam(1, teamLeft1, self.teamDataList[1])
    end

    if teams[2] then
        local teamDataLeft2 = {
            index = 2,
            power = self:ComputeTeamPower(teams[2]),
            heroes = teams[2].heroes
        }
        self.teamDataList[2] = teamDataLeft2
        self:RefreshTeam(1, teamLeft2, self.teamDataList[2])
    end

    if teams[3] then
        local teamDataLeft3 = {
            index = 3,
            power = self:ComputeTeamPower(teams[3]),
            heroes = teams[3].heroes
        }
        self.teamDataList[3] = teamDataLeft3
        self:RefreshTeam(1, teamLeft3, self.teamDataList[3])
    end

    local totalFight = self:ComputeTotalPower(teams)
    local txtFight = GetChild(self.ui.m_goHeadLeft, "txtFight", UEUI.Text)
    txtFight.text = NumToGameString(totalFight)
end

function UI_TopFightDetail:RefreshTeamRight(teams)
    local teamRight1 = GetChild(self.ui.m_goTeam1, "teamRight")
    local teamRight2 = GetChild(self.ui.m_goTeam2, "teamRight")
    local teamRight3 = GetChild(self.ui.m_goTeam3, "teamRight")

    -- 按照队伍类型排序
    table.sort(teams, function (a, b)
        if a.team_type ~= b.team_type then
            return a.team_type < b.team_type
        end
        return false
    end)

    for _, value in ipairs(self.heroItemList[2]) do
        for _, item in ipairs(value) do
            SetActive(item.go, false)
        end
    end

    -- 第一编队
    if teams[1] then
        local teamDataRight1 = {
            index = 1,
            power = self:ComputeTeamPower(teams[1]),
            heroes = teams[1].heroes
        }
        self:RefreshTeam(2, teamRight1, teamDataRight1)
    end

    -- 第二编队
    if teams[2] then
        local teamDataRight2 = {
            index = 2,
            power = self:ComputeTeamPower(teams[2]),
            heroes = teams[2].heroes
        }
        self:RefreshTeam(2, teamRight2, teamDataRight2)
    end

    -- 第三编队
    if teams[3] then
        local teamDataRight3 = {
            index = 3,
            power = self:ComputeTeamPower(teams[3]),
            heroes = teams[3].heroes
        }
        self:RefreshTeam(2, teamRight3, teamDataRight3)
    end

    local totalFight = self:ComputeTotalPower(teams)
    local txtFight = GetChild(self.ui.m_goHeadRight, "txtFight", UEUI.Text)
    txtFight.text = NumToGameString(totalFight)
end

--- 刷新队伍信息
--- @param type number 类型 1 左边 2 右边
--- @param parent any 父节点
--- @param data table 队伍信息
function UI_TopFightDetail:RefreshTeam(type, parent, data)
    local txtFight = GetChild(parent, "txtFight", UEUI.Text)
    local txtFightGrey = GetChild(parent, "txtFightGrey", UEUI.Text)

    local index = data.index
    -- 战力
    local totalPower = TradeWagonsManager:GetBattleListTotalPower(data.heroes)
    txtFight.text = NumToGameString(totalPower)
    txtFightGrey.text = NumToGameString(totalPower)

    for _, value in ipairs(self.heroItemList[type][index]) do
        SetActive(value.go, false)
    end

    -- 英雄
    for key, value in ipairs(data.heroes) do
        local config = ConfigMgr:GetDataByID(ConfigDefine.ID.slg_hero, value.code)
        if config then
            local hero = HeroModule.new(value.code, config)
            hero:SetHeroValueByKey("level", value.level)
            hero:SetHeroValueByKey("starLv", value.star)
            local heroGo

            local heroCount = #data.heroes
            if heroCount == 3 then
                key = key + 2
            end

            if key <= #self.heroItemList[type][index] then
                heroGo = self.heroItemList[type][index][key]
                heroGo:ChangHero(hero)
                SetActive(heroGo.go, true)
            end
        end
    end

    -- 根据英雄数量，改变布局
    local heroParent = GetChild(parent, "hero")
    local row1 = GetChild(heroParent, "row1")
    local row2 = GetChild(heroParent, "row2")
    local item1 = GetChild(heroParent, "row1/item1")
    local item2 = GetChild(heroParent, "row1/item2")
    local item3 = GetChild(heroParent, "row2/item3")
    local item4 = GetChild(heroParent, "row2/item4")
    local item5 = GetChild(heroParent, "row2/item5")

    local heroCount = #data.heroes

    if heroCount == 1 then
        SetActive(row1, true)
        SetActive(row2, false)
        SetActive(item1, true)
        SetActive(item2, false)
    elseif heroCount == 2 then
        SetActive(row1, true)
        SetActive(row2, false)
        SetActive(item1, true)
        SetActive(item2, true)
    elseif heroCount == 3 then
        SetActive(row1, false)
        SetActive(row2, true)
        SetActive(item3, true)
        SetActive(item4, true)
        SetActive(item5, true)
    elseif heroCount == 4 then
        SetActive(row1, true)
        SetActive(row2, true)
        SetActive(item1, true)
        SetActive(item2, true)
        SetActive(item3, true)
        SetActive(item4, true)
        SetActive(item5, false)
    elseif heroCount == 5 then
        SetActive(row1, true)
        SetActive(row2, true)
        SetActive(item1, true)
        SetActive(item2, true)
        SetActive(item3, true)
        SetActive(item4, true)
        SetActive(item5, true)
    end
end


--- 根据战斗结果刷新界面
--- @param data table 战斗数据
function UI_TopFightDetail:RefreshPanelByBattleResult()
    local winCount = 0
    local battleResultInfo = self.data.list
    for i = 1, 3, 1 do
        if IsTableNotEmpty(battleResultInfo[i]) then
            local battleResult = battleResultInfo[i].result
            local battleReportID = battleResultInfo[i].report_id

            self:SetTeamView(self.goTeamList[i], battleResult == 1)
            self:SetRecordButton(self.goTeamList[i], battleReportID)

            local isWin = battleResult == 1
            self:SetHeroGrey(1, i, isWin)
            self:SetHeroGrey(2, i, not isWin)

            if battleResult == 1 then
                winCount = winCount + 1
            end
        end
    end

    local isWin = winCount >= 2
    SetActive(self.ui.m_goWinLeft, isWin)
    SetActive(self.ui.m_goFailLeft, not isWin)

    SetActive(self.ui.m_goWinRight, not isWin)
    SetActive(self.ui.m_goFailRight, isWin)

    self.ui.m_txtScoreLeft.text = winCount
    self.ui.m_txtScoreRight.text = 3 - winCount

    UnifyOutline(self.ui.m_txtScoreLeft, isWin and "#EA4120" or "#0A7CE7")
    UnifyOutline(self.ui.m_txtScoreRight, not isWin and "#EA4120" or "#0A7CE7")
end

--- 计算队伍战力
--- @param team table 队伍信息
--- @return number power 队伍战力
function UI_TopFightDetail:ComputeTeamPower(team)
    local power = 0
    local heroes = team.heroes
    if IsTableEmpty(heroes) then
        heroes = team
    end
    for _, hero in ipairs(heroes) do
        if IsTableNotEmpty(hero) then
            power = power + hero.power
        end
    end
    return power
end

--- 设置队伍背景图
--- @param parent any 父节点
--- @param isWin boolean 是否胜利
function UI_TopFightDetail:SetTeamView(parent, isWin)
    local fightWinLeft = GetChild(parent, "teamLeft/txtFight")
    local fightFailLeft = GetChild(parent, "teamLeft/txtFightGrey")
    local fightWinRight = GetChild(parent, "teamRight/txtFight")
    local fightFailRight = GetChild(parent, "teamRight/txtFightGrey")

    local indexLeft = 1
    local indexRight = 2

    if not isWin then
        indexLeft = 2
        indexRight = 1
    end

    local imgTeamBg = GetComponent(parent, UEUI.Image)
    SetUIImage(imgTeamBg, BgIcon[indexLeft], false)

    SetActive(fightWinLeft, indexLeft == 1)
    SetActive(fightFailLeft, indexLeft == 2)
    SetActive(fightWinRight, indexRight == 1)
    SetActive(fightFailRight, indexRight == 2)
end

--- 设置回放按钮
--- @param parent any 父节点
--- @param reportID number 战报 ID
function UI_TopFightDetail:SetRecordButton(parent, reportID)
    local btnBattleReset = GetChild(parent, "playback/btnPlayback", UEUI.Button)
    btnBattleReset.onClick:RemoveAllListeners()
    btnBattleReset.onClick:AddListener(function ()
        BattleSceneManager:RequestBattleReportById(reportID, function (res, report, isReplay)

        end)
    end)
end

--- 设置英雄头像置灰
--- @param type number 类型 1 左边 2 右边
--- @param index number 编队
--- @param isWin boolean 是否胜利
function UI_TopFightDetail:SetHeroGrey(type, index, isWin)
    for _, value in ipairs(self.heroItemList[type][index]) do
        value:SetGray(not isWin)
    end
end

--- 计算三个队伍的总战力
--- @param teams table 队伍信息
--- @return number totalFight 总战力
function UI_TopFightDetail:ComputeTotalPower(teams)
    local totalFight = 0
    for _, value in ipairs(teams) do
        local power = TradeWagonsManager:GetBattleListTotalPower(value.heroes)
        totalFight = totalFight + power
    end
    return totalFight
end

function UI_TopFightDetail:OnRefresh(param)
    
end

function UI_TopFightDetail:onDestroy()
    self.isInit = nil
end

function UI_TopFightDetail:onUIEventClick(go,param)
    local name = go.name
    -- 关闭按钮
    if name == "m_btnClose" then
        self:Close()
    end
end

return UI_TopFightDetail